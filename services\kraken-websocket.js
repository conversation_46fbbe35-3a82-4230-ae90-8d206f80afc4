const WebSocket = require('ws');
const config = require('./config');

const {
  WS_URL,
  WS,
  KRAKEN_PAIRS
} = config.KRAKEN;

// 🔍 DEBUG: Verify KRAKEN_PAIRS import
console.log('🔍 KRAKEN_PAIRS loaded:', KRAKEN_PAIRS);
console.log('🔍 Available pairs:', Object.keys(KRAKEN_PAIRS || {}));

// 🛡️ FALLBACK: Define KRAKEN_PAIRS if import failed
if (!KRAKEN_PAIRS) {
  console.warn('⚠️ KRAKEN_PAIRS not found in config, using fallback');
  const FALLBACK_KRAKEN_PAIRS = {
    'xbtusdt': 'XBT/USDT',
    'ethusdt': 'ETH/USDT',
    'ltcusdt': 'LTC/USDT',
    'xrpusdt': 'XRP/USDT',
    'adausdt': 'ADA/USDT',
    'solusdt': 'SOL/USDT',
    'dotusdt': 'DOT/USDT',
    'bnbusdt': 'BNB/USDT',
    'avaxusdt': 'AVAX/USDT',
  };

  // Assign fallback to the imported variable
  Object.assign(config.KRAKEN, { KRAKEN_PAIRS: FALLBACK_KRAKEN_PAIRS });
  console.log('✅ Fallback KRAKEN_PAIRS assigned');
}

class KrakenWebSocket {
  // 🎯 GENIUS ENHANCEMENT: Advanced state tracking and analytics
  lastPrices = new Map();
  priceHistory = new Map(); // Track price history for trend analysis
  connectionMetrics = {
    connectTime: null,
    totalReconnects: 0,
    messagesReceived: 0,
    lastMessageTime: null,
    averageLatency: 0,
    uptime: 0
  };

  constructor(wss) {
    this.ws = null;
    this.wss = wss;
    this.pingInterval = null;
    this.pongTimeout = null;
    this.subscriptions = new Set();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = WS.RECONNECT_DELAY;
    this.pingIntervalMs = WS.PING_INTERVAL;
    this.pongTimeoutMs = WS.PONG_TIMEOUT;
    this.maxReconnectAttempts = WS.MAX_RECONNECT_ATTEMPTS;

    // 🚀 GENIUS: Health monitoring and auto-optimization
    this.healthMonitor = {
      isHealthy: true,
      lastHealthCheck: Date.now(),
      consecutiveErrors: 0,
      performanceScore: 100
    };

    // Start health monitoring
    this.startHealthMonitoring();
  }

  connect() {
    if (this.ws) {
      console.log('WebSocket already connected');
      return;
    }

    this.ws = new WebSocket(WS_URL);

    this.ws.on('open', () => {
      console.log('🎯 KRAKEN WEBSOCKET: Connected to Kraken API');
      this.reconnectAttempts = 0;
      this.connectionMetrics.connectTime = Date.now();
      this.connectionMetrics.totalReconnects = this.reconnectAttempts;
      this.healthMonitor.consecutiveErrors = 0;
      this.healthMonitor.isHealthy = true;

      // No ping/pong needed - Kraken handles keep-alive internally
      this.resubscribe();

      console.log('✅ KRAKEN WEBSOCKET: Ready for subscriptions');
    });

    this.ws.on('message', (data) => this.handleMessage(data));
    this.ws.on('close', () => this.handleClose());
    this.ws.on('error', (error) => this.handleError(error));
  }

  handleMessage(data) {
    try {
      const messageStartTime = Date.now();
      this.connectionMetrics.messagesReceived++;
      this.connectionMetrics.lastMessageTime = messageStartTime;

      const message = JSON.parse(data);
      if (Array.isArray(message) && message[0] > 0) {
        this.resetPongTimeout();

        if (message[1]?.a) {
          this.handleEnhancedPriceUpdate(message, messageStartTime);
        }
      }

      // Handle subscription confirmations and system messages
      if (message.event) {
        this.handleSystemMessage(message);
      }

    } catch (error) {
      console.error('🚨 KRAKEN WEBSOCKET: Error processing message:', error);
      this.healthMonitor.consecutiveErrors++;
    }
  }

  // 🚀 GENIUS: Enhanced price update with analytics and trend analysis
  handleEnhancedPriceUpdate(message, messageStartTime) {
    const price = parseFloat(message[1].a[0][0]);
    const volume = parseFloat(message[1].a[0][1]);
    const timestamp = Math.floor(Date.now() / 1000);

    const pair = Object.entries(KRAKEN_PAIRS).find(
      ([_, krakenPair]) => krakenPair === message[3]?.replace('/', '')
    )?.[0];

    if (pair && !isNaN(price)) {
      // Get previous price for change calculation
      const lastPriceData = this.lastPrices.get(pair);
      const lastPrice = lastPriceData?.price || price;

      const priceChange = price - lastPrice;
      const priceChangePercent = lastPrice ? (priceChange / lastPrice) * 100 : 0;

      // Analyze price trend
      const trendAnalysis = this.analyzePriceTrend(pair, price);

      // Update last price with enhanced data
      this.lastPrices.set(pair, {
        price,
        volume,
        timestamp,
        priceChange,
        priceChangePercent,
        trend: trendAnalysis,
        source: 'websocket',
        latency: Date.now() - messageStartTime
      });

      // Enhanced broadcast with analytics
      this.broadcastEnhancedPriceUpdate(pair, {
        price,
        volume,
        timestamp,
        priceChange,
        priceChangePercent,
        trend: trendAnalysis,
        latency: Date.now() - messageStartTime,
        quality: this.healthMonitor.performanceScore
      });

      // Update connection metrics
      this.updateLatencyMetrics(Date.now() - messageStartTime);
      this.healthMonitor.consecutiveErrors = 0; // Reset on successful processing
    }
  }

  // Legacy method for backward compatibility
  handlePriceUpdate(message) {
    this.handleEnhancedPriceUpdate(message, Date.now());
  }

  // 🎯 GENIUS: System message handler
  handleSystemMessage(message) {
    switch (message.event) {
      case 'subscriptionStatus':
        console.log('🎯 KRAKEN WEBSOCKET: Subscription status:', message);
        if (message.status === 'subscribed') {
          console.log(`✅ Successfully subscribed to ${message.pair || message.subscription?.name}`);
          this.healthMonitor.consecutiveErrors = 0;
        } else if (message.status === 'error') {
          console.error(`❌ Subscription error: ${message.errorMessage}`);
          this.healthMonitor.consecutiveErrors++;
        }
        break;

      case 'systemStatus':
        console.log('🌐 KRAKEN SYSTEM STATUS:', message.status);
        if (message.status === 'online') {
          console.log('✅ Kraken system is online');
        } else {
          console.warn('⚠️ Kraken system status:', message.status);
        }
        break;

      case 'heartbeat':
        // Update last message time for heartbeat
        this.connectionMetrics.lastMessageTime = Date.now();
        break;

      default:
        console.log('📨 KRAKEN WEBSOCKET: Unknown event:', message.event);
    }
  }
  
  /**
   * Get the last known price for a trading pair
   * @param {string} pair - The trading pair (e.g., 'BTC/USD')
   * @returns {Object|null} - The last price data or null if not available
   */
  getLastPrice(pair) {
    return this.lastPrices.get(pair) || null;
  }

  // 🚀 GENIUS: Enhanced price broadcast with analytics
  broadcastEnhancedPriceUpdate(pair, data) {
    if (!this.wss) {
      console.warn('🚨 KRAKEN WEBSOCKET: No WebSocket server to broadcast to');
      return;
    }

    const enhancedUpdate = JSON.stringify({
      type: 'price_update',
      pair,
      ...data,
      source: 'kraken_websocket',
      serverTimestamp: Date.now(),
      connectionQuality: this.healthMonitor.performanceScore
    });

    const changeIndicator = data.priceChangePercent > 0 ? '📈' : data.priceChangePercent < 0 ? '📉' : '➡️';
    console.log(`🎯 KRAKEN WEBSOCKET: ${changeIndicator} ${pair} = $${data.price} (${data.priceChangePercent > 0 ? '+' : ''}${data.priceChangePercent.toFixed(2)}%) [${data.latency}ms]`);

    let clientCount = 0;
    this.wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(enhancedUpdate);
        clientCount++;
      }
    });

    console.log(`📡 KRAKEN WEBSOCKET: Enhanced data sent to ${clientCount} clients (Quality: ${data.quality}%)`);
  }

  // Legacy method for backward compatibility
  broadcastPriceUpdate(pair, price, volume, timestamp) {
    this.broadcastEnhancedPriceUpdate(pair, {
      price,
      volume,
      timestamp,
      priceChange: 0,
      priceChangePercent: 0,
      trend: null,
      latency: 0,
      quality: this.healthMonitor.performanceScore
    });
  }

  // 🎯 GENIUS: Latency metrics tracking
  updateLatencyMetrics(latency) {
    // Calculate rolling average latency
    if (this.connectionMetrics.averageLatency === 0) {
      this.connectionMetrics.averageLatency = latency;
    } else {
      // Exponential moving average (90% old, 10% new)
      this.connectionMetrics.averageLatency = (this.connectionMetrics.averageLatency * 0.9) + (latency * 0.1);
    }

    // Track uptime
    if (this.connectionMetrics.connectTime) {
      this.connectionMetrics.uptime = Date.now() - this.connectionMetrics.connectTime;
    }
  }

  handlePong() {
    // DISABLED - No longer using ping/pong
    return;
  }

  handleClose() {
    console.log('WebSocket disconnected');
    this.cleanup();
    this.attemptReconnect();
  }

  handleError(error) {
    console.error('WebSocket error:', error);
    this.cleanup();
    this.attemptReconnect();
  }

  setupPingPong() {
    // DISABLED - Kraken WebSocket doesn't require ping/pong
    // The constant ping/pong was causing disconnections
    console.log('Ping/pong disabled - Kraken WebSocket handles keep-alive internally');

    // Clear any existing intervals
    if (this.pingInterval) clearInterval(this.pingInterval);
    if (this.pongTimeout) clearTimeout(this.pongTimeout);
  }

  resetPongTimeout() {
    // DISABLED - No longer using ping/pong
    return;
  }

  cleanup() {
    if (this.pingInterval) clearInterval(this.pingInterval);
    if (this.pongTimeout) clearTimeout(this.pongTimeout);
    if (this.ws) {
      this.ws.removeAllListeners();
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.close();
      }
      this.ws = null;
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnection attempts (${this.maxReconnectAttempts}) reached. Please check your connection.`);
      // Reset attempts after a longer delay
      setTimeout(() => {
        this.reconnectAttempts = 0;
        this.attemptReconnect();
      }, 60000); // Try again after 1 minute
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  subscribe(pair) {
    // 🔍 DEBUG: Enhanced subscription debugging
    console.log(`🎯 KRAKEN WEBSOCKET: Attempting to subscribe to pair: ${pair}`);
    console.log(`🔍 KRAKEN_PAIRS available:`, Object.keys(KRAKEN_PAIRS || config.KRAKEN?.KRAKEN_PAIRS || {}));

    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('🚨 KRAKEN WEBSOCKET: WebSocket not connected, cannot subscribe');
      return;
    }

    // 🛡️ ENHANCED ERROR HANDLING: Get KRAKEN_PAIRS with fallback
    const pairsMap = KRAKEN_PAIRS || config.KRAKEN?.KRAKEN_PAIRS;
    if (!pairsMap) {
      console.error('🚨 KRAKEN WEBSOCKET: KRAKEN_PAIRS is undefined! Check config import.');
      console.error('🔍 Config structure:', Object.keys(config));
      console.error('🔍 KRAKEN config:', Object.keys(config.KRAKEN || {}));
      return;
    }

    const krakenPair = pairsMap[pair];
    if (!krakenPair) {
      console.error(`🚨 KRAKEN WEBSOCKET: No Kraken pair mapping for ${pair}`);
      console.error(`🔍 Available pairs: ${Object.keys(pairsMap).join(', ')}`);
      return;
    }

    console.log(`✅ KRAKEN WEBSOCKET: Mapping ${pair} → ${krakenPair}`);

    const subscription = {
      event: 'subscribe',
      pair: [krakenPair],
      subscription: {
        name: 'ticker'
      }
    };

    this.ws.send(JSON.stringify(subscription));
    this.subscriptions.add(pair);
  }

  resubscribe() {
    this.subscriptions.forEach(pair => this.subscribe(pair));
  }

  // 🚀 GENIUS ENHANCEMENT: Advanced Health Monitoring System
  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthCheck();
      this.optimizePerformance();
      this.generateHealthReport();
    }, 30000); // Every 30 seconds
  }

  performHealthCheck() {
    const now = Date.now();
    const timeSinceLastMessage = now - (this.connectionMetrics.lastMessageTime || now);

    // Check connection health
    const isConnected = this.ws && this.ws.readyState === WebSocket.OPEN;
    const isReceivingData = timeSinceLastMessage < 60000; // Less than 1 minute
    const hasLowErrors = this.healthMonitor.consecutiveErrors < 5;

    this.healthMonitor.isHealthy = isConnected && isReceivingData && hasLowErrors;
    this.healthMonitor.lastHealthCheck = now;

    // Auto-recovery if unhealthy
    if (!this.healthMonitor.isHealthy && isConnected) {
      console.warn('🚨 KRAKEN WEBSOCKET: Health check failed, initiating auto-recovery');
      this.initiateAutoRecovery();
    }
  }

  optimizePerformance() {
    // Calculate performance score based on metrics
    let score = 100;

    if (this.healthMonitor.consecutiveErrors > 0) score -= this.healthMonitor.consecutiveErrors * 10;
    if (this.connectionMetrics.averageLatency > 1000) score -= 20;
    if (this.connectionMetrics.totalReconnects > 5) score -= 10;

    this.healthMonitor.performanceScore = Math.max(0, score);

    // Auto-optimize based on performance
    if (this.healthMonitor.performanceScore < 70) {
      console.log('🔧 KRAKEN WEBSOCKET: Performance optimization triggered');
      this.optimizeConnection();
    }
  }

  initiateAutoRecovery() {
    console.log('🔄 KRAKEN WEBSOCKET: Initiating intelligent auto-recovery');

    // Graceful reconnection with subscription preservation
    const savedSubscriptions = Array.from(this.subscriptions);

    this.cleanup();

    setTimeout(() => {
      this.connect();

      // Restore subscriptions after connection
      setTimeout(() => {
        savedSubscriptions.forEach(pair => this.subscribe(pair));
      }, 2000);
    }, 1000);
  }

  optimizeConnection() {
    // Intelligent connection optimization
    if (this.connectionMetrics.averageLatency > 1000) {
      console.log('🚀 KRAKEN WEBSOCKET: Optimizing for high latency');
      this.pingIntervalMs = Math.min(this.pingIntervalMs * 1.5, 120000);
    }

    if (this.healthMonitor.consecutiveErrors > 3) {
      console.log('🚀 KRAKEN WEBSOCKET: Optimizing for error recovery');
      this.reconnectDelay = Math.min(this.reconnectDelay * 1.2, 30000);
    }
  }

  generateHealthReport() {
    const uptime = this.connectionMetrics.connectTime ?
      Date.now() - this.connectionMetrics.connectTime : 0;

    const report = {
      status: this.healthMonitor.isHealthy ? '✅ HEALTHY' : '⚠️ UNHEALTHY',
      performance: `${this.healthMonitor.performanceScore}%`,
      uptime: `${Math.round(uptime / 1000)}s`,
      messages: this.connectionMetrics.messagesReceived,
      reconnects: this.connectionMetrics.totalReconnects,
      subscriptions: this.subscriptions.size,
      latency: `${this.connectionMetrics.averageLatency}ms`
    };

    console.log('📊 KRAKEN WEBSOCKET HEALTH:', report);
  }

  // 🎯 GENIUS: Price trend analysis
  analyzePriceTrend(pair, price) {
    if (!this.priceHistory.has(pair)) {
      this.priceHistory.set(pair, []);
    }

    const history = this.priceHistory.get(pair);
    history.push({ price, timestamp: Date.now() });

    // Keep only last 100 price points
    if (history.length > 100) {
      history.shift();
    }

    // Calculate trend
    if (history.length >= 10) {
      const recent = history.slice(-10);
      const trend = recent[recent.length - 1].price - recent[0].price;
      const trendPercent = (trend / recent[0].price) * 100;

      return {
        trend: trend > 0 ? 'UP' : trend < 0 ? 'DOWN' : 'FLAT',
        strength: Math.abs(trendPercent),
        velocity: trend / (recent[recent.length - 1].timestamp - recent[0].timestamp)
      };
    }

    return null;
  }
}

module.exports = KrakenWebSocket;
