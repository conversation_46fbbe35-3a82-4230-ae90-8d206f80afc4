/**
 * WebSocketProcessor - Handles WebSocket message processing with queue management
 * and error handling to prevent stack overflow and other issues.
 */
class WebSocketProcessor {
    /**
     * Create a new WebSocketProcessor
     * @param {Object} options - Configuration options
     * @param {number} [options.maxBatchSize=10] - Maximum number of messages to process in a single batch
     * @param {number} [options.maxQueueSize=1000] - Maximum number of messages to queue before dropping old messages
     * @param {number} [options.processDelay=10] - Delay between processing batches (ms)
     * @param {number} [options.maxDepth=5] - Maximum recursion depth for message processing
     * @param {number} [options.maxConsecutiveErrors=10] - Maximum consecutive errors before pausing
     * @param {number} [options.errorResetTime=60000] - Time to wait before resetting error count (ms)
     */
    constructor({
        maxBatchSize = 10,
        maxQueueSize = 1000,
        processDelay = 10,
        maxDepth = 5,
        maxConsecutiveErrors = 10,
        errorResetTime = 60000
    } = {}) {
        // Configuration
        this.maxBatchSize = maxBatchSize;
        this.maxQueueSize = maxQueueSize;
        this.processDelay = processDelay;
        this.maxDepth = maxDepth;
        this.maxConsecutiveErrors = maxConsecutiveErrors;
        this.errorResetTime = errorResetTime;
        
        // State
        this.messageQueue = [];
        this.messageTimestamps = new Map();
        this.messageHandlers = new Map();
        this.processedMessages = new Set();
        this.currentlyProcessing = new Set(); // Track currently processing messages
        this.isProcessing = false;
        this.isPaused = false;
        this.consecutiveErrors = 0;
        this.lastErrorTime = 0;
        this.maxProcessedMessages = maxQueueSize * 2; // Keep some history to prevent duplicates
        this.cleanupInterval = null; // Will hold the cleanup interval ID
        
        // Bind methods
        this.queueMessage = this.queueMessage.bind(this);
        this.processQueue = this.processQueue.bind(this);
        this.processBatch = this.processBatch.bind(this);
        this.processMessage = this.processMessage.bind(this);
        this.addMessageHandler = this.addMessageHandler.bind(this);
        this.removeMessageHandler = this.removeMessageHandler.bind(this);
        this.pauseProcessing = this.pauseProcessing.bind(this);
        this.resumeProcessing = this.resumeProcessing.bind(this);
        this.cleanup = this.cleanup.bind(this);
        this.cleanupOldMessages = this.cleanupOldMessages.bind(this);
        
        // Start cleanup interval
        this.cleanupInterval = setInterval(() => this.cleanupOldMessages(), 30000);
    }
    
    /**
     * Queue a message for processing
     * @param {Object} message - The message to queue
     */
    queueMessage(message) {
        try {
            // Validate input
            if (!message || typeof message !== 'object') {
                console.warn('[WebSocket] Invalid message format:', message);
                return;
            }
            
            // Skip if in error state
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                console.warn('[WebSocket] Skipping message due to error state');
                return;
            }
            
            // Generate message ID
            const now = Date.now();
            const messageId = message._id || `msg_${now}_${Math.random().toString(36).substr(2, 9)}`;
            
            // Skip duplicates
            if (this.processedMessages.has(messageId)) {
                console.debug(`[WebSocket] Skipping duplicate message: ${messageId}`);
                return;
            }
            
            // Add to processed messages with cleanup
            this.processedMessages.add(messageId);
            if (this.processedMessages.size > this.maxProcessedMessages) {
                const firstId = Array.from(this.processedMessages)[0];
                this.processedMessages.delete(firstId);
            }
            
            // Check queue size
            if (this.messageQueue.length >= this.maxQueueSize) {
                console.warn('[WebSocket] Message queue full, dropping message:', messageId);
                return;
            }
            
            // Add to queue with metadata
            this.messageQueue.push({
                ...message,
                _messageId: messageId,
                _receivedAt: now,
                _processingDepth: 0
            });
            
            // Start processing if not already running
            if (!this.isProcessing && !this.isPaused) {
                setTimeout(() => this.processQueue().catch(console.error), 0);
            }
        } catch (error) {
            console.error('[WebSocket] Error in queueMessage:', error);
            this.handleError();
        }
    }
    
    /**
     * Process the message queue
     */
    async processQueue() {
        if (this.isProcessing || this.isPaused || this.messageQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        try {
            // Process messages in batches
            while (this.messageQueue.length > 0 && !this.isPaused) {
                const batch = this.messageQueue.splice(0, this.maxBatchSize);
                await this.processBatch(batch);
                
                // Add delay between batches
                if (this.messageQueue.length > 0) {
                    await new Promise(resolve => setTimeout(resolve, this.processDelay));
                }
            }
        } catch (error) {
            console.error('[WebSocket] Error in processQueue:', error);
            this.handleError();
        } finally {
            this.isProcessing = false;
            
            // Schedule next batch if there are more messages
            if (this.messageQueue.length > 0 && !this.isPaused) {
                setTimeout(() => this.processQueue().catch(console.error), 0);
            }
        }
    }
    
    /**
     * Process a batch of messages
     * @param {Array} batch - Batch of messages to process
     */
    async processBatch(batch) {
        const results = [];
        
        for (const message of batch) {
            try {
                // Skip if max depth reached
                if (message._processingDepth >= this.maxDepth) {
                    console.warn(`[WebSocket] Max depth (${this.maxDepth}) reached for message:`, message._messageId);
                    continue;
                }
                
                // Process message with timeout
                const result = await Promise.race([
                    this.processMessage({
                        ...message,
                        _processingDepth: (message._processingDepth || 0) + 1
                    }),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Message processing timeout')), 10000)
                    )
                ]);
                
                results.push(result);
                this.consecutiveErrors = 0; // Reset error counter on success
                
            } catch (error) {
                console.error('[WebSocket] Error processing message:', {
                    error,
                    messageId: message._messageId,
                    type: message.type
                });
                
                this.handleError();
                
                // If too many errors, stop processing this batch
                if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                    console.error(`[WebSocket] Pausing processing after ${this.consecutiveErrors} errors`);
                    this.pauseProcessing(this.errorResetTime);
                    break;
                }
            }
            
            // Small delay between messages to prevent UI blocking
            await new Promise(resolve => setTimeout(resolve, 1));
        }
        
        return results;
    }
    
    /**
     * Process a single message
     * @param {Object} message - The message to process
     */
    async processMessage(message) {
        // Add additional validation and recursion protection
        if (this.currentlyProcessing.has(message._messageId)) {
            console.warn(`[WebSocket] Detected recursive processing for message: ${message._messageId}`);
            return;
        }

        if (!message || !message.type) {
            throw new Error('Invalid message format: missing type');
        }
        
        // Check recursion depth
        if (message._processingDepth > this.maxDepth) {
            throw new Error(`Max processing depth (${this.maxDepth}) exceeded`);
        }
        
        const handlers = this.messageHandlers.get(message.type);
        if (!handlers || handlers.size === 0) {
            console.debug(`[WebSocket] No handlers for message type: ${message.type}`);
            return;
        }
        
        // Add to currently processing set
        this.currentlyProcessing.add(message._messageId);
        
        try {
            // Process all handlers in sequence with timeout
            for (const handler of handlers) {
                if (typeof handler !== 'function') {
                    console.warn(`[WebSocket] Invalid handler for message type ${message.type}`);
                    continue;
                }
                
                // Execute with timeout
                await Promise.race([
                    (async () => {
                        try {
                            return await handler(message);
                        } catch (error) {
                            console.error(`[WebSocket] Error in ${message.type} handler:`, {
                                error,
                                messageId: message._messageId,
                                handler: handler.name || 'anonymous'
                            });
                            throw error; // Re-throw to be caught by the race
                        }
                    })(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Handler timeout')), 5000)
                    )
                ]).catch(error => {
                    console.error(`[WebSocket] Handler timeout or error for message ${message._messageId}:`, error);
                    throw error;
                });
                
                // Small delay between handlers to prevent stack buildup
                await new Promise(resolve => setTimeout(resolve, 1));
            }
        } finally {
            // Always clean up
            this.currentlyProcessing.delete(message._messageId);
        }
    }
    
    /**
     * Add a message handler
     * @param {string} type - Message type to handle
     * @param {Function} handler - Handler function
     * @returns {Function} Unsubscribe function
     */
    addMessageHandler(type, handler) {
        if (typeof type !== 'string' || typeof handler !== 'function') {
            throw new Error('Invalid arguments: type must be a string and handler must be a function');
        }
        
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, new Set());
        }
        
        this.messageHandlers.get(type).add(handler);
        
        // Return unsubscribe function
        return () => this.removeMessageHandler(type, handler);
    }
    
    /**
     * Remove a message handler
     * @param {string} type - Message type
     * @param {Function} handler - Handler function to remove
     */
    removeMessageHandler(type, handler) {
        if (this.messageHandlers.has(type)) {
            this.messageHandlers.get(type).delete(handler);
        }
    }
    
    /**
     * Pause message processing
     * @param {number} duration - Duration in ms to pause for
     */
    pauseProcessing(duration = 0) {
        this.isPaused = true;
        
        if (duration > 0) {
            setTimeout(() => this.resumeProcessing(), duration);
        }
    }
    
    /**
     * Resume message processing
     */
    resumeProcessing() {
        this.isPaused = false;
        this.consecutiveErrors = 0;
        this.lastErrorTime = 0;
        
        if (this.messageQueue.length > 0) {
        this.consecutiveErrors++;
        this.lastErrorTime = Date.now();
        
        // Auto-resume after error reset time
        if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
     */
    cleanupOldMessages() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5 minutes
        
        // Clean up old timestamps
        for (const [id, timestamp] of this.messageTimestamps.entries()) {
            if (now - timestamp > maxAge) {
                this.messageTimestamps.delete(id);
            }
        }
        
        // Clean up processed messages set if it gets too large
        if (this.processedMessages.size > this.maxProcessedMessages) {
            const array = Array.from(this.processedMessages);
            this.processedMessages = new Set(array.slice(-this.maxProcessedMessages));
        }
    }
    
    /**
     * Clean up resources and prevent memory leaks
     */
    cleanup() {
        try {
            // Stop any ongoing processing
            this.isPaused = true;
            this.isProcessing = false;
            
            // Clear the message queue
            this.messageQueue = [];
            
            // Clear all maps and sets
            if (this.messageTimestamps) this.messageTimestamps.clear();
            if (this.processedMessages) this.processedMessages.clear();
            if (this.currentlyProcessing) this.currentlyProcessing.clear();
            
            // Clear all message handlers
            if (this.messageHandlers) this.messageHandlers.clear();
            
            // Clear any pending timeouts/intervals
            if (this.cleanupInterval) {
                clearInterval(this.cleanupInterval);
                this.cleanupInterval = null;
            }
            
            // Reset error state
            this.consecutiveErrors = 0;
            this.lastErrorTime = 0;
            
            console.log('[WebSocket] Processor cleanup complete');
        } catch (error) {
            console.error('[WebSocket] Error during cleanup:', error);
        }
    }
}

// Export for use in browser
typeof window !== 'undefined' && (window.WebSocketProcessor = WebSocketProcessor);
