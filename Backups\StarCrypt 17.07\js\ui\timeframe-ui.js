/**
 * Timeframe UI Manager
 * Handles timeframe selection and updates across the application
 */

// Only define TimeframeUIManager if it doesn't already exist
if (typeof TimeframeUIManager === 'undefined') {
  class TimeframeUIManager {
    constructor() {
      this.timeframes = [...(window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d', '1w'])];
      this.activeTimeframes = [...this.timeframes];
      this.currentTimeframe = '1h'; // Default timeframe
      this.signalManager = window.StarCrypt?.SignalManager;
      this.webSocketProcessor = window.StarCrypt?.WebSocketProcessor;
      this.initialize();
    }

    initialize() {
      if (!this.cacheElements()) {
        console.error('[TimeframeUI] Failed to initialize: Required elements not found');
        return false;
      }
      this.setupEventListeners();
      this.renderTimeframeControls();
      this.initializeWebSocketHandlers();
      console.log('[TimeframeUI] Initialized with timeframes:', this.timeframes);
      return true;
    }

    cacheElements() {
      this.timeframeContainer = document.getElementById('timeframeControls');
      if (!this.timeframeContainer) {
        console.warn('[TimeframeUI] Timeframe container not found');
        return false;
      }
      return true;
    }

    setupEventListeners() {
      // Delegate events for timeframe buttons
      document.addEventListener('click', (e) => {
        if (e.target.classList.contains('timeframe-btn')) {
          const timeframe = e.target.dataset.timeframe;
          if (timeframe) {
            this.setTimeframe(timeframe);
          }
        }
      });
    }

    initializeWebSocketHandlers() {
      if (this.webSocketProcessor) {
        this.webSocketProcessor.on('timeframeUpdate', (data) => {
          this.handleTimeframeUpdate(data);
        });
      }
    }

    handleTimeframeUpdate(data) {
      // Handle timeframe updates from WebSocket
      console.log('[TimeframeUI] Received timeframe update:', data);
    }

    renderTimeframeControls() {
      if (!this.timeframeContainer) return;
      
      // Clear existing content
      this.timeframeContainer.innerHTML = '';
      
      // Create buttons for each timeframe
      this.timeframes.forEach(timeframe => {
        const button = document.createElement('button');
        button.className = `timeframe-btn ${timeframe === this.currentTimeframe ? 'active' : ''}`;
        button.dataset.timeframe = timeframe;
        button.textContent = timeframe;
        this.timeframeContainer.appendChild(button);
      });
    }

    setTimeframe(timeframe) {
      if (this.timeframes.includes(timeframe)) {
        this.currentTimeframe = timeframe;
        this.updateActiveTimeframeButtons();
        this.updateSignalManagerTimeframe();
        console.log(`[TimeframeUI] Timeframe set to: ${timeframe}`);
      }
    }

    updateActiveTimeframeButtons() {
      const buttons = this.timeframeContainer?.querySelectorAll('.timeframe-btn') || [];
      buttons.forEach(button => {
        const isActive = button.dataset.timeframe === this.currentTimeframe;
        button.classList.toggle('active', isActive);
      });
    }

    updateSignalManagerTimeframe() {
      if (this.signalManager) {
        this.signalManager.setTimeframe(this.currentTimeframe);
      }
    }
  }

  // Initialize the TimeframeUI when the DOM is ready
  const initTimeframeUI = () => {
    if (window.StarCrypt) {
      try {
        window.TimeframeUI = new TimeframeUIManager();
        console.log('[TimeframeUI] Initialized successfully');
        return true;
      } catch (error) {
        console.error('[TimeframeUI] Failed to initialize:', error);
        return false;
      }
    }
    console.log('[TimeframeUI] Waiting for StarCrypt to be available...');
    setTimeout(initTimeframeUI, 100);
    return false;
  };

  // Start initialization when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTimeframeUI);
  } else {
    initTimeframeUI();
  }
} // End of if (typeof TimeframeUIManager === 'undefined')
