/**
 * StarCrypt System Status Dashboard
 * Real-time monitoring and status display for all StarCrypt systems
 */

class SystemStatusDashboard {
  constructor() {
    this.statusData = new Map();
    this.updateInterval = 5000; // 5 seconds
    this.dashboardElement = null;
    
    this.init();
  }

  init() {
    console.log('📊 SYSTEM STATUS: Initializing comprehensive status dashboard...');
    
    this.createDashboard();
    this.startMonitoring();
    this.setupKeyboardShortcuts();
    
    console.log('✅ SYSTEM STATUS: Dashboard active and monitoring');
  }

  createDashboard() {
    // Create dashboard element
    this.dashboardElement = document.createElement('div');
    this.dashboardElement.id = 'system-status-dashboard';
    this.dashboardElement.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 300px;
      max-height: 80vh;
      background: rgba(0, 0, 0, 0.9);
      border: 2px solid #00FFFF;
      border-radius: 8px;
      padding: 15px;
      color: #00FFFF;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      z-index: 10000;
      overflow-y: auto;
      display: none;
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    `;

    document.body.appendChild(this.dashboardElement);
  }

  startMonitoring() {
    this.updateStatus();
    setInterval(() => {
      this.updateStatus();
    }, this.updateInterval);
  }

  updateStatus() {
    const status = this.gatherSystemStatus();
    this.displayStatus(status);
  }

  gatherSystemStatus() {
    const status = {
      timestamp: new Date().toLocaleTimeString(),
      systems: {},
      performance: {},
      errors: {},
      data: {}
    };

    // WebSocket Status
    status.systems.websocket = {
      name: 'WebSocket Connection',
      status: window.ws && window.ws.readyState === WebSocket.OPEN ? 'ONLINE' : 'OFFLINE',
      details: window.ws ? `State: ${window.ws.readyState}` : 'Not initialized'
    };

    // Starfield Animation Status
    status.systems.starfield = {
      name: 'Starfield Animation',
      status: window.starfieldAnimation && window.starfieldAnimation.isRunning ? 'RUNNING' : 'STOPPED',
      details: window.starfieldAnimation ? 
        `Stars: ${window.starfieldAnimation.stars?.length || 0}, Meteors: ${window.starfieldAnimation.meteors?.length || 0}` : 
        'Not initialized'
    };

    // Signal Lights Status
    const signalCircles = document.querySelectorAll('.signal-circle');
    const connectedSignals = document.querySelectorAll('.signal-circle[data-admiral-connected="true"]');
    status.systems.signalLights = {
      name: 'Signal Lights',
      status: signalCircles.length > 0 ? 'ACTIVE' : 'INACTIVE',
      details: `Total: ${signalCircles.length}, Connected: ${connectedSignals.length}`
    };

    // ML Systems Status
    status.systems.mlSystems = {
      name: 'ML Analysis',
      status: (window.MLHistoricalAnalysis || window.mlHistoricalAnalysis) ? 'ACTIVE' : 'INACTIVE',
      details: window.advancedMLFeatures ? 'Advanced ML loaded' : 'Basic ML only'
    };

    // Unified Commander Status
    status.systems.unifiedCommander = {
      name: 'Unified Commander',
      status: window.unifiedSignalCommander ? 'ACTIVE' : 'INACTIVE',
      details: window.unifiedSignalCommander ? 
        `Selected: ${window.unifiedSignalCommander.selectedLights?.size || 0}` : 
        'Not initialized'
    };

    // Tooltip System Status
    status.systems.tooltips = {
      name: 'Tooltip System',
      status: window.tooltipFixes || document.getElementById('global-tooltip') ? 'ACTIVE' : 'INACTIVE',
      details: document.getElementById('global-tooltip') ? 'Global tooltip ready' : 'Missing tooltip element'
    };

    // Performance Metrics
    status.performance = {
      memory: this.getMemoryUsage(),
      fps: this.getFPS(),
      eventQueue: window.systemCoordinator ? window.systemCoordinator.getEventQueueStatus() : null
    };

    // Error Tracking
    status.errors = {
      recovery: window.errorRecoverySystem ? window.errorRecoverySystem.getErrorHistory().length : 0,
      health: window.systemHealthMonitor ? window.systemHealthMonitor.getSystemHealth() : null
    };

    // Data Status
    status.data = {
      indicators: window.indicatorsData ? Object.keys(window.indicatorsData).length : 0,
      currentPrice: window.currentPrice || 'Not available',
      lastUpdate: window.lastDataUpdate ? new Date(window.lastDataUpdate).toLocaleTimeString() : 'Never'
    };

    return status;
  }

  displayStatus(status) {
    if (!this.dashboardElement) return;

    const html = `
      <div style="text-align: center; margin-bottom: 10px; border-bottom: 1px solid #00FFFF; padding-bottom: 5px;">
        <strong>🚀 STARCRYPT STATUS</strong><br>
        <small>${status.timestamp}</small>
      </div>

      <div style="margin-bottom: 10px;">
        <strong>🔧 SYSTEMS</strong>
        ${Object.entries(status.systems).map(([key, system]) => `
          <div style="margin: 3px 0; padding: 2px; background: rgba(0, 255, 255, 0.1);">
            <span style="color: ${system.status === 'ONLINE' || system.status === 'RUNNING' || system.status === 'ACTIVE' ? '#00FF00' : '#FF6666'};">
              ${system.status === 'ONLINE' || system.status === 'RUNNING' || system.status === 'ACTIVE' ? '✅' : '❌'}
            </span>
            <strong>${system.name}:</strong> ${system.status}<br>
            <small style="color: #CCCCCC;">${system.details}</small>
          </div>
        `).join('')}
      </div>

      <div style="margin-bottom: 10px;">
        <strong>⚡ PERFORMANCE</strong>
        <div style="margin: 3px 0;">Memory: ${status.performance.memory}</div>
        <div style="margin: 3px 0;">FPS: ${status.performance.fps}</div>
        ${status.performance.eventQueue ? `
          <div style="margin: 3px 0;">Queue: ${status.performance.eventQueue.unprocessed}/${status.performance.eventQueue.total}</div>
        ` : ''}
      </div>

      <div style="margin-bottom: 10px;">
        <strong>📊 DATA</strong>
        <div style="margin: 3px 0;">Timeframes: ${status.data.indicators}</div>
        <div style="margin: 3px 0;">Price: $${typeof status.data.currentPrice === 'number' ? status.data.currentPrice.toLocaleString() : status.data.currentPrice}</div>
        <div style="margin: 3px 0;">Last Update: ${status.data.lastUpdate}</div>
      </div>

      <div style="margin-bottom: 10px;">
        <strong>🛡️ PROTECTION</strong>
        <div style="margin: 3px 0;">Errors Handled: ${status.errors.recovery}</div>
        ${status.errors.health ? `
          <div style="margin: 3px 0;">Health: ${this.calculateOverallHealth(status.errors.health)}%</div>
        ` : ''}
      </div>

      <div style="text-align: center; margin-top: 10px; padding-top: 5px; border-top: 1px solid #00FFFF;">
        <small>Press Ctrl+Shift+D to toggle</small>
      </div>
    `;

    this.dashboardElement.innerHTML = html;
  }

  getMemoryUsage() {
    if (performance.memory) {
      const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      return `${used}MB`;
    }
    return 'Unknown';
  }

  getFPS() {
    // Simple FPS counter
    if (!this.fpsCounter) {
      this.fpsCounter = { frames: 0, lastTime: Date.now() };
    }
    
    this.fpsCounter.frames++;
    const now = Date.now();
    if (now - this.fpsCounter.lastTime >= 1000) {
      const fps = this.fpsCounter.frames;
      this.fpsCounter.frames = 0;
      this.fpsCounter.lastTime = now;
      this.lastFPS = fps;
    }
    
    return this.lastFPS || 'Calculating...';
  }

  calculateOverallHealth(healthData) {
    if (!healthData) return 0;
    
    const systems = Object.values(healthData);
    const healthySystems = systems.filter(system => system.healthy).length;
    return Math.round((healthySystems / systems.length) * 100);
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      // Ctrl+Shift+D to toggle dashboard
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        this.toggleDashboard();
        event.preventDefault();
      }
      
      // Ctrl+Shift+H for health check
      if (event.ctrlKey && event.shiftKey && event.key === 'H') {
        this.performHealthCheck();
        event.preventDefault();
      }
      
      // Ctrl+Shift+R for recovery
      if (event.ctrlKey && event.shiftKey && event.key === 'R') {
        this.performRecovery();
        event.preventDefault();
      }
    });
  }

  toggleDashboard() {
    if (this.dashboardElement.style.display === 'none') {
      this.dashboardElement.style.display = 'block';
      console.log('📊 SYSTEM STATUS: Dashboard shown');
    } else {
      this.dashboardElement.style.display = 'none';
      console.log('📊 SYSTEM STATUS: Dashboard hidden');
    }
  }

  performHealthCheck() {
    console.log('🏥 SYSTEM STATUS: Performing manual health check...');
    if (window.systemHealthMonitor) {
      window.systemHealthMonitor.performHealthCheck();
    }
    this.updateStatus();
  }

  performRecovery() {
    console.log('🛡️ SYSTEM STATUS: Performing manual recovery...');
    if (window.errorRecoverySystem) {
      // Trigger recovery for common issues
      window.errorRecoverySystem.forceRecovery('starfield_meteors');
      window.errorRecoverySystem.forceRecovery('signal_connection');
      window.errorRecoverySystem.forceRecovery('tooltip_timeframe');
    }
    this.updateStatus();
  }

  // Public API
  show() {
    this.dashboardElement.style.display = 'block';
  }

  hide() {
    this.dashboardElement.style.display = 'none';
  }

  getStatus() {
    return this.gatherSystemStatus();
  }
}

// Initialize system status dashboard
window.systemStatusDashboard = new SystemStatusDashboard();

// Export for global access
window.SystemStatusDashboard = SystemStatusDashboard;

// Add to global controls
if (window.starCryptControls) {
  window.starCryptControls.showStatus = () => window.systemStatusDashboard.show();
  window.starCryptControls.hideStatus = () => window.systemStatusDashboard.hide();
  window.starCryptControls.getStatus = () => window.systemStatusDashboard.getStatus();
}

console.log('📊 SYSTEM STATUS: Dashboard loaded - Press Ctrl+Shift+D to toggle');
