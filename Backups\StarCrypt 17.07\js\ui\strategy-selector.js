// Strategy selector functionality for StarCrypt
// Implements the strategy selection menu and functionality

// Use the global TRADING_STRATEGIES defined in global-variables.js
// No need to redefine here, just ensure it exists
if (!window.TRADING_STRATEGIES) {
  console.error('Error: TRADING_STRATEGIES not defined in global-variables.js');
}

/* Original TRADING_STRATEGIES definition - now defined in global-variables.js
window.TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'The flagship strategy that combines momentum indicators (RSI, Stoch RSI) with trend confirmation (MACD), price position (Bollinger Bands), trend strength (ADX), volume analysis, and ML confirmation for high-confidence signals.',
    helperText: `
      <p><strong>Step 1:</strong> Check RSI and <PERSON>och RSI for momentum confirmation</p>
      <p><strong>Step 2:</strong> Confirm with MACD crossover or histogram direction</p>
      <p><strong>Step 3:</strong> Verify price position relative to Bollinger Bands</p>
      <p><strong>Step 4:</strong> Check ADX for trend strength (>25 is strong trend)</p>
      <p><strong>Step 5:</strong> Look for volume confirmation of the move</p>
      <p><strong>Step 6:</strong> Verify ML signals align with traditional indicators</p>
      <p><strong>Step 7:</strong> Check for time anomalies and correlation patterns</p>
    `
  },
  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'Advanced AI strategy that uses neural networks to identify complex market patterns and predict price movements with high accuracy by combining traditional indicators with machine learning signals.',
    helperText: `
      <p><strong>Step 1:</strong> Monitor ML signal for pattern recognition & ML alerts</p>
      <p><strong>Step 2:</strong> Confirm with RSI and MACD for momentum alignment</p>
      <p><strong>Step 3:</strong> Check Bollinger Bands for volatility context</p>
      <p><strong>Step 4:</strong> Analyze sentiment data for market mood confirmation</p>
      <p><strong>Step 5:</strong> Review entropy and correlation metrics for market structure</p>
      <p><strong>Step 6:</strong> Look for time anomalies that could indicate reversal points</p>
    `
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'],
    description: 'A more aggressive strategy focusing on momentum indicators with looser thresholds for more frequent trading signals.',
    helperText: `
      <p><strong>Step 1:</strong> Look for RSI crossing above 60 (bullish) or below 40 (bearish)</p>
      <p><strong>Step 2:</strong> Confirm with Stoch RSI crossing above 70 (bullish) or below 30 (bearish)</p>
      <p><strong>Step 3:</strong> Check MACD for crossover or histogram direction change</p>
      <p><strong>Step 4:</strong> Verify MFI aligns with other momentum indicators</p>
      <p><strong>Step 5:</strong> Ensure volume is increasing in the direction of the move</p>
      <p><strong>Note:</strong> This strategy generates more signals but with lower accuracy - use smaller position sizes!</p>
    `
  },
  top_bottom_feeder: {
    name: 'Top/Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'mfi', 'volume'],
    description: 'Specialized for catching market extremes, focusing on deeply oversold or overbought conditions across multiple indicators.',
    helperText: `
      <p><strong>Step 1:</strong> Wait for RSI below 20 (buy) or above 80 (sell)</p>
      <p><strong>Step 2:</strong> Confirm with Stoch RSI below 10 (buy) or above 90 (sell)</p>
      <p><strong>Step 3:</strong> Verify price is at or beyond Bollinger Bands (2.5 standard deviations)</p>
      <p><strong>Step 4:</strong> Check MFI for extreme readings aligned with other indicators</p>
      <p><strong>Step 5:</strong> Look for volume climax or exhaustion</p>
      <p><strong>Step 6:</strong> Wait for the first sign of reversal before entering</p>
      <p><strong>Note:</strong> This strategy has fewer signals but higher win rate when properly executed</p>
    `
  },
  trend_rider: {
    name: 'Trend Rider',
    indicators: ['adx', 'macd', 'rsi', 'williamsR', 'volume'],
    description: 'Focuses on riding established trends by combining ADX for trend strength with momentum indicators for entry timing.',
    helperText: `
      <p><strong>Step 1:</strong> Confirm ADX is above 25 to ensure a strong trend exists</p>
      <p><strong>Step 2:</strong> Check MACD alignment with the trend direction</p>
      <p><strong>Step 3:</strong> Look for RSI above 50 in uptrends or below 50 in downtrends</p>
      <p><strong>Step 4:</strong> Use Williams %R pullbacks to -50 in uptrends or -50 in downtrends as entry points</p>
      <p><strong>Step 5:</strong> Verify volume is supporting the trend direction</p>
      <p><strong>Note:</strong> This strategy works best in clearly trending markets, avoid ranging conditions</p>
    `
  }
};
*/

// Initialize strategy selector (single canonical)
function initializeStrategySelector() {
  // Close menu when clicking outside
  function closeAllMenus() {
    const menu = document.getElementById('strategyMenu');
    if (menu && menu.classList.contains('open')) {
      menu.classList.remove('open');
    }
  }
  
  // Load saved strategy or use default
  function loadSavedStrategy() {
    const savedStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa';
    if (window.TRADING_STRATEGIES[savedStrategy]) {
      window.currentStrategy = savedStrategy;
    } else {
      console.warn(`Saved strategy ${savedStrategy} not found, using default`);
      window.currentStrategy = 'admiral_toa';
    }
    
    // Update UI to reflect current strategy
    const selector = document.getElementById('strategySelector') || document.getElementById('mainStrategySelector');
    if (selector) {
      selector.value = window.currentStrategy;
      updateStrategyDescription();
    }
  }

  document.addEventListener('mousedown', function(event) {
    const menu = document.getElementById('strategyMenu');
    if (menu && menu.classList.contains('open')) {
      if (!menu.contains(event.target) && event.target.id !== 'strategyButton') {
        closeAllMenus();
      }
    }
  });

  // Get strategy selector elements
  const strategyButton = document.getElementById('strategyButton');
  const strategyMenu = document.getElementById('strategyMenu');
  const strategySelector = document.getElementById('strategySelector');
  const applyStrategyButton = document.getElementById('applyStrategyButton');
  
  // If elements don't exist, create them
  if (!strategyButton) {
    console.error('Strategy button not found, cannot initialize strategy selector');
    return;
  }
  
  // Create strategy menu if it doesn't exist
  let menu = document.getElementById('strategyMenu');
  if (!menu) {
    menu = document.createElement('div');
    menu.id = 'strategyMenu';
    menu.className = 'menu-content';
    menu.style.display = 'none'; // Start hidden
    menu.style.position = 'absolute';
    menu.style.zIndex = '1000';
    const tickerContainer = document.getElementById('tickerContainer');
    if (tickerContainer) {
      tickerContainer.appendChild(menu);
    } else {
      console.error('Could not find tickerContainer, appending menu to body as a fallback.');
      document.body.appendChild(menu);
    }
    
    menu.innerHTML = `
      <div class="strategy-controls" id="strategyControls">
        <h3>Strategy Selector</h3>
        <div class="strategy-description">
          Select a trading strategy to optimize your signal lights and indicators. Each strategy focuses on different market conditions and trading styles.
        </div>
        <select id="strategySelector" class="strategy-selector">
          ${Object.entries(window.TRADING_STRATEGIES).map(([key, strategy]) => 
            `<option value="${key}">${strategy.name}</option>`
          ).join('')}
        </select>
        <div id="strategyDescription" class="strategy-info"></div>
        <div id="strategyHelperText" class="strategy-helper"></div>
        <button id="applyStrategyButton" class="apply-strategy-button">Apply Strategy</button>
      </div>
    `;
  }
  
  // Add event listeners with proper cleanup
  strategyButton.removeEventListener('click', toggleStrategyMenu);
  strategyButton.addEventListener('click', toggleStrategyMenu);
  
  const newStrategySelector = document.getElementById('strategySelector');
  const newApplyStrategyButton = document.getElementById('applyStrategyButton');
  
  if (newStrategySelector) {
    newStrategySelector.removeEventListener('change', updateStrategyDescription);
    newStrategySelector.addEventListener('change', updateStrategyDescription);
  }
  
  if (newApplyStrategyButton) {
    // Remove any existing click handlers to prevent duplicates
    const newButton = newApplyStrategyButton.cloneNode(true);
    newApplyStrategyButton.parentNode.replaceChild(newButton, newApplyStrategyButton);
    
    // Add our click handler with proper error handling
    newButton.addEventListener('click', function(event) {
      try {
        console.log('Apply button clicked');
        const selector = document.getElementById('strategySelector');
        if (selector) {
          applySelectedStrategy(selector.value);
        } else {
          console.error('Strategy selector not found');
        }
      } catch (error) {
        console.error('Error in apply button handler:', error);
      }
    });
  }
  
    // Load saved strategy and update UI
  loadSavedStrategy();
  
  // Initialize with current strategy
  updateStrategyUI(window.currentStrategy);
  updateStrategyDescription();
}

// Toggle strategy menu visibility
function toggleStrategyMenu(event) {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }
  
  const strategyMenu = document.getElementById('strategyMenu');
  if (strategyMenu) {
    const isVisible = strategyMenu.style.display === 'block';
    strategyMenu.style.display = isVisible ? 'none' : 'block';
    
    // Position the menu below the strategy button
    if (!isVisible) {
      const button = document.getElementById('strategyButton');
      if (button) {
        const rect = button.getBoundingClientRect();
        strategyMenu.style.top = `${rect.bottom + window.scrollY}px`;
        strategyMenu.style.left = `${rect.left + window.scrollX}px`;
      }
    }
    
    // Hide other menus
    const otherMenus = document.querySelectorAll('.menu-content:not(#strategyMenu)');
    otherMenus.forEach(menu => {
      menu.style.display = 'none';
    });
  }
}

// Update strategy description based on selected strategy
function updateStrategyDescription() {
  try {
    const strategyId = window.currentStrategy || 'admiral_toa';
    const strategy = window.TRADING_STRATEGIES[strategyId];
    const descriptionElement = document.getElementById('strategyDescription') || 
                              document.querySelector('.strategy-description');
    const helperTextElement = document.getElementById('strategyHelperText') ||
                              document.querySelector('.strategy-helper-text');
    const strategyNameElement = document.getElementById('strategyName') ||
                               document.querySelector('.strategy-name');
    
    if (!strategy) {
      console.error('Strategy not found:', strategyId);
      return;
    }
    
    console.log('Updating strategy info for:', strategy.name);
    
    // Update strategy name
    if (strategyNameElement) {
      strategyNameElement.textContent = strategy.name || 'Unnamed Strategy';
    }
    
    // Update description
    if (descriptionElement) {
      descriptionElement.innerHTML = strategy.description || 'No description available.';
    } else {
      console.warn('Could not find strategy description element');
    }
    
    // Update helper text
    if (helperTextElement) {
      helperTextElement.innerHTML = strategy.helperText || 'No helper text available.';
    } else {
      console.warn('Could not find strategy helper text element');
    }
    
    // Update active state in the dropdown
    const dropdownItems = document.querySelectorAll('.strategy-option');
    dropdownItems.forEach(item => {
      if (item.dataset.strategy === strategyId) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
    
    // Update window state
    window.currentStrategy = strategyId;
    
    // Save to localStorage
    localStorage.setItem('currentStrategy', strategyId);
    
    console.log('Updated strategy description for:', strategy.name);
    
  } catch (error) {
    console.error('Error updating strategy description:', error);
  }
  
  // 🚫 REMOVED DUPLICATE STRATEGY UI UPDATE - Handled by updateStrategyUI function
  console.log('[StrategySelector] 🚫 BLOCKED duplicate strategy UI update - using unified system');
}

// Handle strategy change event


function handleStrategyChange(event) {
  const strategyId = event.target.value;
  console.log('Strategy changed to:', strategyId);

  // Validate strategy exists
  if (!strategyId || !window.TRADING_STRATEGIES || !window.TRADING_STRATEGIES[strategyId]) {
    console.error('Invalid strategy ID or TRADING_STRATEGIES not available:', strategyId);
    showStatusMessage('Error: Invalid strategy selected', 'error');
    return;
  }

  // Update current strategy
  window.currentStrategy = strategyId;

  // Update strategy info panel
  updateStrategyDescription();

  // Update indicators for the new strategy
  updateIndicatorsForStrategy(strategyId);

  // Force update the signal matrix
  if (window.updateSignalMatrix) {
    window.updateSignalMatrix();
  }

  // Notify other components
  notifyStrategyChange(strategyId);

  // Force a UI refresh
  setTimeout(() => {
    if (window.updateAllSignalLights) {
      window.updateAllSignalLights();
    }
  }, 100);

  // Update description
  updateStrategyDescription();

  // Show success message
  const strategyName = window.TRADING_STRATEGIES[strategyId].name || strategyId;
  showStatusMessage(`Strategy applied: ${strategyName}`, 'success');
  
  // Close the menu after a short delay
  setTimeout(() => {
    const menu = document.getElementById('strategyMenu');
    if (menu) menu.style.display = 'none';
  }, 1000);
}

// Update UI components for the new strategy
function updateStrategyUI(strategyId) {
  const strategy = window.TRADING_STRATEGIES[strategyId];
  if (!strategy) return;

  // Update strategy info panel
  if (window.updateStrategyInfoPanel) {
    window.updateStrategyInfoPanel(strategy);
  }
  
  // Update signal matrix
  if (window.updateSignalMatrix) {
    window.updateSignalMatrix(strategyId);
  }
  
  // Update indicators
  updateIndicatorsForStrategy(strategy);
}

// Update indicators based on selected strategy
function updateIndicatorsForStrategy(strategy) {
  if (!strategy || !window.updateEnabledIndicators) return;
  
  // Get indicators for this strategy
  const indicators = getStrategySpecificIndicators(strategy);
  
  // Update enabled indicators
  window.updateEnabledIndicators(indicators);
  
  // Update signal lights
  if (window.updateAllSignalLights) {
    window.updateAllSignalLights();
  }
}

// Get indicators specific to the current strategy
function getStrategySpecificIndicators(strategy) {
  if (!strategy || !strategy.indicators) return [];
  
  return strategy.indicators.filter(indicator => {
    return typeof indicator === 'string' && indicator.trim() !== '';
  });
}

// DISABLED - Unified Signal Commander handles strategy changes now
function notifyStrategyChange(strategyId) {
  console.log('[StrategySelector] DISABLED - Strategy changes handled by Unified Signal Commander');
  return;

  // Original code disabled to prevent duplicate notifications:
  // const event = new CustomEvent('strategyChanged', {
  //   detail: { strategyId }
  // });
  // document.dispatchEvent(event);
}

// Apply the selected strategy and update all relevant UI
function applySelectedStrategy(strategyId) {
  if (!strategyId) {
    strategyId = window.currentStrategy;
  }
  
  const strategy = window.TRADING_STRATEGIES[strategyId];
  
  if (!strategy) {
    console.error('Strategy not found:', strategyId);
    return;
  }
  
  console.log('Applying strategy:', strategy.name);
  
  if (strategyId === window.currentStrategy) return;
  
  console.log('Strategy changed to:', strategyId);
  
  // Update current strategy
  window.currentStrategy = strategyId;
  
  // Save to localStorage
  localStorage.setItem('currentStrategy', strategyId);
  
  // Update UI
  updateStrategyUI(strategyId);
  
  // Update description
  updateStrategyDescription();
  
  // Update indicators for the new strategy
  updateIndicatorsForStrategy(strategy);
  
  // Force update the signal matrix with the new strategy
  if (window.updateSignalMatrix) {
    window.updateSignalMatrix(strategyId);
  }
  
  // Update the Oracle Matrix with the new strategy indicators
  updateOracleMatrixForStrategy(strategyId);
  
  // Show success message
  showStatusMessage(`Strategy applied: ${strategy.name}`, 'success');
  
  // Close the menu after a short delay
  setTimeout(() => {
    const menu = document.getElementById('strategyMenu');
    if (menu) menu.style.display = 'none';
  }, 1000);
}

// Update Oracle Matrix for the selected strategy
function updateOracleMatrixForStrategy(strategyId) {
  try {
    const strategy = window.TRADING_STRATEGIES[strategyId];
    if (!strategy || !strategy.indicators) return;
    
    // Get the Oracle Matrix container
    const matrixContainer = document.querySelector('.oracle-matrix');
    if (!matrixContainer) {
      console.warn('Oracle Matrix container not found');
      return;
    }
    
    // Get all indicator rows
    const indicatorRows = matrixContainer.querySelectorAll('.indicator-row');
    if (!indicatorRows.length) {
      console.warn('No indicator rows found in Oracle Matrix');
      return;
    }
    
    // Show/hide indicators based on the current strategy
    indicatorRows.forEach(row => {
      const indicatorName = row.getAttribute('data-indicator');
      if (indicatorName && strategy.indicators.includes(indicatorName.toLowerCase())) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
    
    // Force a reflow to ensure the UI updates
    matrixContainer.offsetHeight;
    
  } catch (error) {
    console.error('Error updating Oracle Matrix for strategy:', error);
  }
}

// Initialize Oracle Matrix
function initializeOracleMatrix() {
  try {
    console.log('Initializing Oracle Matrix...');

    // Get the Oracle Matrix container. It MUST exist in the DOM.
    const matrixContainer = document.querySelector('.indicators-section.cosmic-indicators');
    if (!matrixContainer) {
      console.error('CRITICAL: Oracle Matrix container (.indicators-section.cosmic-indicators) not found. UI cannot be initialized.');
      return false; // Stop initialization if the container is missing
    }

    // Initialize with current strategy if available
    const currentStrategy = window.currentStrategy || 'admiral_toa';
    updateOracleMatrixForStrategy(currentStrategy);

    console.log('Oracle Matrix initialized');
    return true;
  } catch (error) {
    console.error('Error initializing Oracle Matrix:', error);
    return false;
  }
}

// Initialize on DOM ready
function initializeStrategySelectorModule() {
  console.log('Initializing strategy selector module...');
  
  // Initialize Oracle Matrix
  initializeOracleMatrix();
  
  // Wait a bit to ensure all elements are loaded
  setTimeout(() => {
    try {
      // Make sure TRADING_STRATEGIES is available
      if (!window.TRADING_STRATEGIES) {
        console.error('TRADING_STRATEGIES is not defined');
        return;
      }
      // Load saved strategy or use default
      const savedStrategy = localStorage.getItem('currentStrategy');
      if (savedStrategy && window.TRADING_STRATEGIES[savedStrategy]) {
        window.currentStrategy = savedStrategy;
      } else {
        window.currentStrategy = 'admiral_toa';
        localStorage.setItem('currentStrategy', window.currentStrategy);
      }
      
      // Initialize the selector UI
      initializeStrategySelector();
      
      // Initialize strategy selectors
      const strategySelectors = [
        document.getElementById('strategySelector'),
        document.getElementById('mainStrategySelector')
      ].filter(Boolean);
      
      // Set the selected value for all selectors
      strategySelectors.forEach(selector => {
        if (selector) {
          selector.value = window.currentStrategy;
          selector.removeEventListener('change', handleStrategyChange);
          selector.addEventListener('change', handleStrategyChange);
        }
      });
      
      // Initialize apply buttons
      const applyButtons = [
        document.getElementById('applyStrategyButton'),
        document.getElementById('mainApplyStrategyButton')
      ].filter(Boolean);
      
      applyButtons.forEach(button => {
        if (button) {
          button.removeEventListener('click', applySelectedStrategy);
          button.addEventListener('click', applySelectedStrategy);
        }
      });
      
      // Update UI with current strategy
      updateStrategyUI(window.currentStrategy);
      updateStrategyDescription();
      
      // Update indicators for the current strategy
      const currentStrategy = window.TRADING_STRATEGIES[window.currentStrategy];
      if (currentStrategy) {
        updateIndicatorsForStrategy(currentStrategy);
      }
      
      console.log('Strategy selector initialized with:', window.currentStrategy);
      showStatusMessage(`Strategy loaded: ${currentStrategy.name}`, 'success');
      
    } catch (error) {
      console.error('Error initializing strategy selector:', error);
      showStatusMessage('Error initializing strategy selector', 'error');
    }
  }, 300); // Increased delay to ensure all elements are loaded
}

// Show status message to user
function showStatusMessage(message, type = 'info') {
  try {
    // Try to use existing status message element
    let statusEl = document.getElementById('strategyStatusMessage');
    
    // Create status element if it doesn't exist
    if (!statusEl) {
      statusEl = document.createElement('div');
      statusEl.id = 'strategyStatusMessage';
      statusEl.style.position = 'fixed';
      statusEl.style.bottom = '20px';
      statusEl.style.right = '20px';
      statusEl.style.padding = '10px 20px';
      statusEl.style.borderRadius = '4px';
      statusEl.style.color = '#fff';
      statusEl.style.zIndex = '10000';
      statusEl.style.transition = 'opacity 0.3s ease-in-out';
      statusEl.style.fontFamily = '"Orbitron", sans-serif';
      statusEl.style.fontSize = '14px';
      statusEl.style.fontWeight = 'bold';
      document.body.appendChild(statusEl);
    }
    
    // Set message and style based on type
    statusEl.textContent = message;
    
    // Set background color based on message type
    switch(type) {
      case 'success':
        statusEl.style.backgroundColor = 'rgba(0, 200, 83, 0.9)';
        break;
      case 'error':
        statusEl.style.backgroundColor = 'rgba(255, 0, 0, 0.9)';
        break;
      case 'warning':
        statusEl.style.backgroundColor = 'rgba(255, 193, 7, 0.9)';
        break;
      default: // info
        statusEl.style.backgroundColor = 'rgba(33, 150, 243, 0.9)';
    }
    
    // Show the message
    statusEl.style.opacity = '1';
    
    // Auto-hide after 3 seconds
    clearTimeout(window.statusMessageTimeout);
    window.statusMessageTimeout = setTimeout(() => {
      if (statusEl) {
        statusEl.style.opacity = '0';
      }
    }, 3000);
    
  } catch (error) {
    console.error('Error showing status message:', error);
  }
}

// Make functions available globally
window.initializeStrategySelector = initializeStrategySelector;
window.applySelectedStrategy = applySelectedStrategy;
window.updateStrategyDescription = updateStrategyDescription;
window.handleStrategyChange = handleStrategyChange;
window.updateStrategyUI = updateStrategyUI;
window.updateIndicatorsForStrategy = updateIndicatorsForStrategy;
window.getStrategySpecificIndicators = getStrategySpecificIndicators;
window.notifyStrategyChange = notifyStrategyChange;
window.handleStrategyChange = handleStrategyChange;
window.updateStrategyUI = updateStrategyUI;
window.updateIndicatorsForStrategy = updateIndicatorsForStrategy;
window.getStrategySpecificIndicators = getStrategySpecificIndicators;
window.showStatusMessage = showStatusMessage;
window.notifyStrategyChange = notifyStrategyChange;

// Get the current strategy
function getCurrentStrategy() {
  return window.currentStrategy || 'admiral_toa';
}

// Make function available globally
window.getCurrentStrategy = getCurrentStrategy;

// Update the indicator menu: always show all indicators, tick those in the given strategy or as customized
function updateIndicatorMenu(selectedStrategy, resetToStrategy) {
  try {
    console.log('Updating indicator menu for strategy:', selectedStrategy);
    
    const strategy = window.TRADING_STRATEGIES[selectedStrategy];
    if (!strategy) {
      console.error('Strategy not found:', selectedStrategy);
      return;
    }
    
    const menu = document.getElementById('indicatorMenu');
    if (!menu) {
      console.warn('Indicator menu not found, will attempt to create it');
      // Try to create the menu if it doesn't exist
      createIndicatorMenu();
      return;
    }
    
    // Get all checkboxes
    const checkboxes = menu.querySelectorAll('input[type="checkbox"]');
    
    if (checkboxes.length === 0) {
      console.log('No indicator checkboxes found, creating them...');
      createIndicatorCheckboxes(menu, strategy.indicators);
      return;
    }
    
    // Update each checkbox
    checkboxes.forEach(checkbox => {
      try {
        const indicator = checkbox.value;
        const isInStrategy = strategy.indicators.includes(indicator);
        
        // If resetting to strategy defaults or if indicator is in strategy, update the checkbox
        if (resetToStrategy || isInStrategy) {
          checkbox.checked = isInStrategy;
          
          // Trigger change event to update any dependent UI
          const event = new Event('change', { bubbles: true });
          checkbox.dispatchEvent(event);
        }
      } catch (e) {
        console.error('Error updating checkbox:', checkbox, e);
      }
    });
    
    console.log('Indicator menu updated for strategy:', selectedStrategy);
    
    // Update the signal matrix after a short delay
    if (window.updateSignalMatrix) {
      setTimeout(() => {
        console.log('Triggering signal matrix update from indicator menu update');
        window.updateSignalMatrix();
      }, 100);
    }
  } catch (error) {
    console.error('Error updating indicator menu:', error);
  }
}

// Create indicator checkboxes in the menu
function createIndicatorCheckboxes(menu, indicators) {
  if (!menu) return;
  
  // Clear existing content
  menu.innerHTML = '';
  
  // Create a header
  const header = document.createElement('div');
  header.className = 'indicator-menu-header';
  header.textContent = 'Select Indicators';
  menu.appendChild(header);
  
  // Create checkboxes for each indicator
  indicators.forEach(indicator => {
    const container = document.createElement('div');
    container.className = 'indicator-checkbox';
    
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = `indicator-${indicator}`;
    checkbox.value = indicator;
    checkbox.checked = true; // Default to checked
    
    const label = document.createElement('label');
    label.htmlFor = `indicator-${indicator}`;
    label.textContent = indicator.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    
    container.appendChild(checkbox);
    container.appendChild(label);
    menu.appendChild(container);
    
    // Add change event listener
    checkbox.addEventListener('change', () => {
      console.log(`Indicator ${indicator} ${checkbox.checked ? 'enabled' : 'disabled'}`);
      // You can add additional logic here when checkboxes change
    });
  });
  
  console.log('Created indicator checkboxes:', indicators);
}

// Create the indicator menu if it doesn't exist
function createIndicatorMenu() {
  let menu = document.getElementById('indicatorMenu');
  
  if (!menu) {
    menu = document.createElement('div');
    menu.id = 'indicatorMenu';
    menu.className = 'indicator-menu';
    
    // Add the menu to the DOM (you might want to adjust the parent element)
    const container = document.querySelector('.strategy-controls') || document.body;
    container.appendChild(menu);
    
    console.log('Created indicator menu');
  }
  
  return menu;
}

// Attach indicator menu event listeners for custom strategies
function attachIndicatorMenuHandler() {
  const menu = document.getElementById('indicatorMenu');
  if (!menu) return;
  menu.addEventListener('change', function(e) {
    if (e.target.classList.contains('indicator-toggle')) {
      const indName = e.target.getAttribute('data-name');
      const checked = e.target.checked;
      if (window.enabledIndicators) {
        const ind = window.enabledIndicators.find(i => i.name === indName);
        if (ind) ind.enabled = checked;
      }
      // Update matrix and signal lights immediately
      if (typeof updateSignalMatrix === 'function') updateSignalMatrix();
      if (typeof updateAllSignalLights === 'function') updateAllSignalLights();
    }
  });
}

// Ensure indicator menu handler is attached after DOMContentLoaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', attachIndicatorMenuHandler);
} else {
  attachIndicatorMenuHandler();
}

function ensureRequiredContainersExist() {
  const tickerContainer = document.getElementById('tickerContainer');
  const indicatorsColumn = document.querySelector('.indicators-column');
  const fallbackAnchor = document.getElementById('tradingview_main_chart') || document.body;
  const fallbackParent = fallbackAnchor.parentElement || document.body;

  const containersToEnsure = {
    'logicControls': { 
      parent: tickerContainer, 
      tag: 'div', 
      className: 'menu-content',
      fallback: document.body
    },
    'threshold-sliders': { 
      parent: tickerContainer, 
      tag: 'div', 
      className: 'menu-content',
      fallback: document.body
    },
    'cosmic-indicators': { 
      parent: indicatorsColumn, 
      tag: 'div', 
      className: 'indicators-section cosmic-indicators',
      fallback: fallbackParent,
      insertBefore: fallbackAnchor
    }
  };

  Object.entries(containersToEnsure).forEach(([id, config]) => {
    // Use a more specific query for the Oracle Matrix container to avoid creating duplicates.
    const elementExists = id === 'cosmic-indicators' 
      ? document.querySelector('.indicators-section.cosmic-indicators') 
      : document.getElementById(id);

    if (!elementExists) {
      console.warn(`Container with id '${id}' not found. Creating it dynamically.`);
      const container = document.createElement(config.tag);
      container.id = id;
      if (config.className) {
        container.className = config.className;
      }

      if (config.parent) {
        config.parent.appendChild(container);
        console.log(`Dynamically created and appended '${id}' to`, config.parent);
      } else {
        console.warn(`Preferred parent for '${id}' not found. Using fallback.`);
        const fallback = config.fallback || document.body;
        if (config.insertBefore && config.insertBefore !== document.body) {
          fallback.insertBefore(container, config.insertBefore);
        } else {
          fallback.appendChild(container);
        }
        console.log(`Dynamically created and appended '${id}' to fallback parent`, fallback);
      }
    }
  });
}

// Initialize everything when the DOM is ready
function initializeAll() {
  ensureRequiredContainersExist(); // Ensure all UI containers are present before initialization.
  try {
    console.log('Initializing strategy selector module...');
    
    // Initialize the main strategy selector module
    initializeStrategySelectorModule();
    
    // Attach indicator menu handler
    console.log('Attaching indicator menu handler...');
    attachIndicatorMenuHandler();
    
    // Set a default strategy if none is selected
    if (!window.currentStrategy) {
      const savedStrategy = localStorage.getItem('currentStrategy');
      window.currentStrategy = savedStrategy || 'admiral_toa';
      console.log('No current strategy found, using:', window.currentStrategy);
    }
    
    // Update the UI to reflect the current strategy
    console.log('Updating UI for strategy:', window.currentStrategy);
    const selector = document.getElementById('strategySelector') || 
                   document.getElementById('mainStrategySelector');
    
    if (selector) {
      selector.value = window.currentStrategy;
      
      // Trigger change event to update the UI after a short delay
      setTimeout(() => {
        try {
          console.log('Dispatching change event for strategy:', window.currentStrategy);
          const event = new Event('change');
          selector.dispatchEvent(event);
          
          // Update the signal matrix if available
          if (window.updateSignalMatrix) {
            console.log('Triggering signal matrix update...');
            window.updateSignalMatrix();
          }
        } catch (e) {
          console.error('Error dispatching change event:', e);
        }
      }, 100);
    } else {
      console.warn('Could not find strategy selector element');
    }
    
    // Update the strategy description
    console.log('Updating strategy description...');
    updateStrategyDescription();
    
    console.log('Strategy module fully initialized with strategy:', window.currentStrategy);
  } catch (error) {
    console.error('Error during initialization:', error);
  }
}

// Make functions available globally
window.toggleStrategyMenu = toggleStrategyMenu;
window.updateStrategyDescription = updateStrategyDescription;
window.applySelectedStrategy = applySelectedStrategy;

// DISABLED - Unified Signal Commander handles all strategy operations
console.log('[StrategySelector] 🚫 DISABLED - Unified Signal Commander has full control');

// Disable all strategy selector initialization to prevent duplicate notifications
// if (document.readyState === 'loading') {
//   document.addEventListener('DOMContentLoaded', initializeStrategySelectorModule);
// } else {
//   initializeStrategySelectorModule();
// }
window.updateIndicatorMenu = updateIndicatorMenu;
window.initializeAll = initializeAll;

// Initialize when the DOM is ready
console.log('Setting up DOM ready listener for strategy selector...');
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM fully loaded, initializing strategy selector...');
    setTimeout(initializeAll, 100);
  });
} else {
  // DOM already loaded, initialize immediately with a small delay
  console.log('DOM already loaded, initializing strategy selector with delay...');
  setTimeout(initializeAll, 100);
}
