/**
 * ML Loop Prevention System for StarCrypt
 * Prevents infinite loops and spam in ML prediction systems
 */

class MLLoopPrevention {
  constructor() {
    this.lastPredictionTime = 0;
    this.predictionCount = 0;
    this.maxPredictionsPerMinute = 6; // Maximum 6 predictions per minute
    this.minTimeBetweenPredictions = 10000; // Minimum 10 seconds between predictions
    this.isBlocked = false;
    this.blockUntil = 0;
    
    this.init();
  }

  init() {
    console.log('🛡️ ML LOOP PREVENTION: Initializing prediction throttling system...');
    
    this.interceptMLMethods();
    this.setupMonitoring();
    
    console.log('✅ ML LOOP PREVENTION: System active and monitoring');
  }

  interceptMLMethods() {
    // Intercept the AdvancedMLFeatures constructor to prevent multiple instances
    if (window.AdvancedMLFeatures) {
      const OriginalAdvancedMLFeatures = window.AdvancedMLFeatures;
      let singleInstance = null;
      
      window.AdvancedMLFeatures = function(...args) {
        if (singleInstance) {
          console.log('🛡️ ML LOOP PREVENTION: Preventing duplicate AdvancedMLFeatures instance');
          return singleInstance;
        }
        
        singleInstance = new OriginalAdvancedMLFeatures(...args);
        return singleInstance;
      };
      
      // Copy static properties
      Object.setPrototypeOf(window.AdvancedMLFeatures, OriginalAdvancedMLFeatures);
      Object.assign(window.AdvancedMLFeatures, OriginalAdvancedMLFeatures);
    }

    // Intercept generateAdvancedPredictions method
    if (window.advancedMLFeatures && window.advancedMLFeatures.generateAdvancedPredictions) {
      const originalMethod = window.advancedMLFeatures.generateAdvancedPredictions;
      
      window.advancedMLFeatures.generateAdvancedPredictions = (...args) => {
        if (!this.shouldAllowPrediction()) {
          console.log('🛡️ ML LOOP PREVENTION: Blocking prediction to prevent spam');
          return;
        }
        
        this.recordPrediction();
        return originalMethod.apply(window.advancedMLFeatures, args);
      };
    }
  }

  shouldAllowPrediction() {
    const now = Date.now();
    
    // Check if we're currently blocked
    if (this.isBlocked && now < this.blockUntil) {
      return false;
    } else if (this.isBlocked && now >= this.blockUntil) {
      this.isBlocked = false;
      this.predictionCount = 0;
      console.log('🛡️ ML LOOP PREVENTION: Block expired, resuming predictions');
    }
    
    // Check minimum time between predictions
    if (now - this.lastPredictionTime < this.minTimeBetweenPredictions) {
      return false;
    }
    
    // Check rate limiting
    const oneMinuteAgo = now - 60000;
    if (this.predictionCount >= this.maxPredictionsPerMinute) {
      this.blockPredictions(30000); // Block for 30 seconds
      return false;
    }
    
    // Check if we have valid market data
    if (!this.hasValidMarketData()) {
      console.log('🛡️ ML LOOP PREVENTION: No valid market data, blocking prediction');
      return false;
    }
    
    return true;
  }

  hasValidMarketData() {
    // Check if window.currentPrice is valid
    if (!window.currentPrice || typeof window.currentPrice !== 'number' || 
        isNaN(window.currentPrice) || window.currentPrice <= 0 || window.currentPrice === 120000) {
      return false;
    }
    
    // Check if we have some indicator data
    if (!window.indicatorsData || Object.keys(window.indicatorsData).length === 0) {
      return false;
    }
    
    // Check if WebSocket is connected
    if (!window.ws || window.ws.readyState !== WebSocket.OPEN) {
      return false;
    }
    
    return true;
  }

  recordPrediction() {
    const now = Date.now();
    this.lastPredictionTime = now;
    this.predictionCount++;
    
    // Reset count every minute
    setTimeout(() => {
      this.predictionCount = Math.max(0, this.predictionCount - 1);
    }, 60000);
  }

  blockPredictions(duration) {
    this.isBlocked = true;
    this.blockUntil = Date.now() + duration;
    console.warn(`🛡️ ML LOOP PREVENTION: Blocking predictions for ${duration/1000} seconds due to rate limiting`);
  }

  setupMonitoring() {
    // Monitor for excessive console output
    let consoleCallCount = 0;
    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;
    
    console.log = (...args) => {
      consoleCallCount++;
      if (consoleCallCount > 100) { // More than 100 console calls
        if (args[0] && args[0].includes('[AdvancedML]')) {
          // Throttle ML-related console output
          if (consoleCallCount % 10 === 0) { // Only show every 10th message
            originalConsoleLog.apply(console, ['🛡️ ML SPAM DETECTED - Throttling output:', ...args]);
          }
          return;
        }
      }
      originalConsoleLog.apply(console, args);
    };
    
    console.warn = (...args) => {
      consoleCallCount++;
      if (consoleCallCount > 50) {
        if (args[0] && args[0].includes('[AdvancedML]')) {
          if (consoleCallCount % 5 === 0) {
            originalConsoleWarn.apply(console, ['🛡️ ML SPAM DETECTED - Throttling warnings:', ...args]);
          }
          return;
        }
      }
      originalConsoleWarn.apply(console, args);
    };
    
    // Reset console count every 10 seconds
    setInterval(() => {
      consoleCallCount = 0;
    }, 10000);
    
    // Monitor for runaway intervals
    this.monitorIntervals();
  }

  monitorIntervals() {
    const originalSetInterval = window.setInterval;
    const activeIntervals = new Set();
    
    window.setInterval = function(callback, delay, ...args) {
      // Prevent very fast intervals that could cause loops
      if (delay < 1000) {
        console.warn('🛡️ ML LOOP PREVENTION: Preventing fast interval:', delay, 'ms');
        delay = Math.max(delay, 1000); // Minimum 1 second
      }
      
      const intervalId = originalSetInterval.call(this, callback, delay, ...args);
      activeIntervals.add(intervalId);
      
      // Auto-cleanup after 5 minutes
      setTimeout(() => {
        if (activeIntervals.has(intervalId)) {
          clearInterval(intervalId);
          activeIntervals.delete(intervalId);
          console.log('🛡️ ML LOOP PREVENTION: Auto-cleared long-running interval');
        }
      }, 300000);
      
      return intervalId;
    };
    
    const originalClearInterval = window.clearInterval;
    window.clearInterval = function(intervalId) {
      activeIntervals.delete(intervalId);
      return originalClearInterval.call(this, intervalId);
    };
  }

  // Emergency stop function
  emergencyStop() {
    console.warn('🚨 ML LOOP PREVENTION: EMERGENCY STOP ACTIVATED');
    
    // Block all predictions
    this.blockPredictions(60000); // Block for 1 minute
    
    // Clear all ML-related intervals
    if (window.advancedMLFeatures && window.advancedMLFeatures.predictionInterval) {
      clearInterval(window.advancedMLFeatures.predictionInterval);
      window.advancedMLFeatures.predictionInterval = null;
    }
    
    // Reset ML system
    if (window.advancedMLFeatures) {
      window.advancedMLFeatures.isInitialized = false;
      window.advancedMLFeatures.signalsInitialized = false;
    }
    
    console.log('🚨 ML LOOP PREVENTION: Emergency stop complete');
  }

  // Public API
  getStatus() {
    return {
      isBlocked: this.isBlocked,
      blockUntil: this.blockUntil,
      predictionCount: this.predictionCount,
      lastPredictionTime: this.lastPredictionTime,
      hasValidData: this.hasValidMarketData()
    };
  }

  reset() {
    this.isBlocked = false;
    this.blockUntil = 0;
    this.predictionCount = 0;
    this.lastPredictionTime = 0;
    console.log('🛡️ ML LOOP PREVENTION: System reset');
  }
}

// Initialize the loop prevention system
window.mlLoopPrevention = new MLLoopPrevention();

// Add emergency controls to global scope
window.emergencyStopML = () => window.mlLoopPrevention.emergencyStop();
window.resetMLPrevention = () => window.mlLoopPrevention.reset();
window.getMLStatus = () => window.mlLoopPrevention.getStatus();

// Add to StarCrypt controls if available
if (window.starCryptControls) {
  window.starCryptControls.emergencyStopML = window.emergencyStopML;
  window.starCryptControls.resetMLPrevention = window.resetMLPrevention;
  window.starCryptControls.getMLStatus = window.getMLStatus;
}

console.log('🛡️ ML LOOP PREVENTION: System loaded and active');
console.log('🎮 EMERGENCY CONTROLS: Use emergencyStopML() to stop runaway ML processes');
