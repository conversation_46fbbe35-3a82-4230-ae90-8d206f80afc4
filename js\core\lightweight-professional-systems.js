/**
 * StarCrypt Lightweight Professional Systems
 * Minimal overhead professional monitoring without performance impact
 * Focuses on critical trading integrity without system overload
 */

class LightweightProfessionalSystems {
  constructor() {
    this.criticalErrors = [];
    this.dataViolations = [];
    this.wsStatus = 'UNKNOWN';
    this.lastHealthCheck = 0;
    
    this.init();
  }

  init() {
    console.log('⚡ LIGHTWEIGHT SYSTEMS: Initializing minimal overhead professional monitoring...');
    
    this.setupCriticalErrorDetection();
    this.setupDataIntegrityWatch();
    this.setupWebSocketMonitoring();
    
    console.log('✅ LIGHTWEIGHT SYSTEMS: Professional monitoring active with minimal overhead');
  }

  setupCriticalErrorDetection() {
    // Only catch critical errors that affect trading
    window.addEventListener('error', (event) => {
      const error = event.error;
      
      // Only log critical trading-related errors
      if (this.isCriticalTradingError(error)) {
        this.logCriticalError({
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          timestamp: Date.now(),
          type: 'CRITICAL_TRADING_ERROR'
        });
      }
    });

    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      if (this.isCriticalTradingError(event.reason)) {
        this.logCriticalError({
          message: event.reason?.message || 'Unhandled promise rejection',
          type: 'CRITICAL_PROMISE_REJECTION',
          timestamp: Date.now()
        });
      }
    });
  }

  isCriticalTradingError(error) {
    if (!error) return false;
    
    const message = error.message?.toLowerCase() || '';
    const stack = error.stack?.toLowerCase() || '';
    
    // Only flag errors that directly affect trading operations
    return (
      message.includes('trading_integrity_violation') ||
      message.includes('websocket') ||
      message.includes('price') ||
      message.includes('indicator') ||
      stack.includes('websocket') ||
      stack.includes('server.js')
    );
  }

  logCriticalError(errorData) {
    this.criticalErrors.push(errorData);
    
    // Keep only last 10 critical errors
    if (this.criticalErrors.length > 10) {
      this.criticalErrors.shift();
    }

    console.error('🚨 CRITICAL TRADING ERROR:', errorData.message);
    
    // Show user notification for critical errors
    if (this.criticalErrors.length >= 3) {
      this.showCriticalErrorNotification();
    }
  }

  setupDataIntegrityWatch() {
    // Monitor for dummy data usage
    const originalCurrentPrice = window.currentPrice;
    
    Object.defineProperty(window, 'currentPrice', {
      get: () => originalCurrentPrice,
      set: (value) => {
        // Check for suspicious dummy values
        if (value === 120000 || value === 100000 || value === 50000) {
          this.logDataViolation({
            type: 'SUSPICIOUS_PRICE_VALUE',
            value: value,
            timestamp: Date.now()
          });
        }
        originalCurrentPrice = value;
      }
    });
  }

  logDataViolation(violation) {
    this.dataViolations.push(violation);
    
    // Keep only last 5 violations
    if (this.dataViolations.length > 5) {
      this.dataViolations.shift();
    }

    console.error('🔒 DATA INTEGRITY VIOLATION:', violation);
    
    // Trigger protective mode if too many violations
    if (this.dataViolations.length >= 3) {
      this.triggerProtectiveMode();
    }
  }

  setupWebSocketMonitoring() {
    // Monitor WebSocket status changes
    if (window.professionalWebSocketManager) {
      const wsManager = window.professionalWebSocketManager;
      
      wsManager.on('connected', () => {
        this.wsStatus = 'CONNECTED';
        console.log('⚡ LIGHTWEIGHT: WebSocket connected');
      });
      
      wsManager.on('disconnected', () => {
        this.wsStatus = 'DISCONNECTED';
        console.warn('⚡ LIGHTWEIGHT: WebSocket disconnected');
      });
    }
  }

  showCriticalErrorNotification() {
    // Only show notification once per session
    if (this.notificationShown) return;
    this.notificationShown = true;

    const notification = document.createElement('div');
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(255, 68, 68, 0.4);
        z-index: 10000;
        max-width: 350px;
        font-family: 'Segoe UI', sans-serif;
      ">
        <div style="font-weight: bold; margin-bottom: 8px;">⚠️ Critical System Alert</div>
        <div style="font-size: 14px; margin-bottom: 10px;">
          Multiple critical errors detected. System stability may be affected.
        </div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
        ">Dismiss</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 10000);
  }

  triggerProtectiveMode() {
    console.error('🛡️ PROTECTIVE MODE: Data integrity violations detected');
    
    // Show warning but don't disable trading (less aggressive than full system)
    const warning = document.createElement('div');
    warning.innerHTML = `
      <div style="
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: linear-gradient(135deg, #ff8800, #cc6600);
        color: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(255, 136, 0, 0.4);
        z-index: 10000;
        max-width: 350px;
        font-family: 'Segoe UI', sans-serif;
      ">
        <div style="font-weight: bold; margin-bottom: 8px;">⚠️ Data Quality Warning</div>
        <div style="font-size: 14px; margin-bottom: 10px;">
          Data integrity issues detected. Please verify data sources.
        </div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
        ">Dismiss</button>
      </div>
    `;
    
    document.body.appendChild(warning);
    
    setTimeout(() => {
      if (warning.parentElement) {
        warning.remove();
      }
    }, 15000);
  }

  // Lightweight health check (only when requested)
  performHealthCheck() {
    const now = Date.now();
    
    // Throttle health checks to once per minute
    if (now - this.lastHealthCheck < 60000) {
      return this.lastHealthReport;
    }
    
    this.lastHealthCheck = now;
    
    const healthReport = {
      timestamp: now,
      criticalErrors: this.criticalErrors.length,
      dataViolations: this.dataViolations.length,
      wsStatus: this.wsStatus,
      overallHealth: this.calculateOverallHealth()
    };
    
    this.lastHealthReport = healthReport;
    
    console.log('⚡ LIGHTWEIGHT HEALTH:', healthReport);
    
    return healthReport;
  }

  calculateOverallHealth() {
    if (this.criticalErrors.length >= 3) return 'CRITICAL';
    if (this.dataViolations.length >= 3) return 'DEGRADED';
    if (this.wsStatus === 'DISCONNECTED') return 'DEGRADED';
    return 'HEALTHY';
  }

  // Public API
  getStatus() {
    return {
      criticalErrors: this.criticalErrors.length,
      dataViolations: this.dataViolations.length,
      wsStatus: this.wsStatus,
      health: this.calculateOverallHealth()
    };
  }

  getCriticalErrors() {
    return this.criticalErrors;
  }

  getDataViolations() {
    return this.dataViolations;
  }

  clearErrors() {
    this.criticalErrors = [];
    this.dataViolations = [];
    console.log('⚡ LIGHTWEIGHT: Errors cleared');
  }
}

// Initialize lightweight professional systems
window.lightweightProfessionalSystems = new LightweightProfessionalSystems();

// Export for global access
window.LightweightProfessionalSystems = LightweightProfessionalSystems;

// Provide simple health check command
window.checkSystemHealth = () => {
  return window.lightweightProfessionalSystems.performHealthCheck();
};

console.log('⚡ LIGHTWEIGHT SYSTEMS: Minimal overhead professional monitoring active');
