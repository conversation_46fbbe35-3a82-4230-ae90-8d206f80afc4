const fs = require('fs');
const path = require('path');

// Configuration
const ROOT_DIR = __dirname;
const OUTPUT_FILE = path.join(ROOT_DIR, 'CALL_TREE.md');
const IGNORE_DIRS = ['node_modules', '.git', 'backup', 'StarCrypt 27.06'];

// Track files and their relationships
const fileMap = new Map();
const entryPoints = [];

// Analyze a single file
function analyzeFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(ROOT_DIR, filePath);
        
        // Get file type
        const ext = path.extname(filePath).toLowerCase();
        
        // Track dependencies
        const dependencies = [];
        
        // Simple pattern matching for requires/imports
        const requirePattern = /require\(['"]([^'"\s]+)['"]\)/g;
        const importPattern = /from\s+['"]([^'"\s]+)['"]/g;
        
        let match;
        while ((match = requirePattern.exec(content)) !== null) {
            dependencies.push(match[1]);
        }
        
        while ((match = importPattern.exec(content)) !== null) {
            dependencies.push(match[1]);
        }
        
        fileMap.set(relativePath, {
            path: relativePath,
            type: ext,
            dependencies: [...new Set(dependencies)] // Remove duplicates
        });
        
        // Check if this is an entry point
        if (filePath.endsWith('index.html') || filePath.endsWith('main.js') || filePath.endsWith('app.js')) {
            entryPoints.push(relativePath);
        }
    } catch (err) {
        console.error(`Error analyzing ${filePath}:`, err.message);
    }
}

// Recursively scan directory
function scanDirectory(dir) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
            if (!IGNORE_DIRS.includes(entry.name)) {
                scanDirectory(fullPath);
            }
        } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (['.js', '.html', '.json', '.css'].includes(ext)) {
                analyzeFile(fullPath);
            }
        }
    }
}

// Generate markdown output
function generateMarkdown() {
    let output = '# StarCrypt Call Tree Analysis\n\n';
    
    // Entry Points
    output += '## Entry Points\n\n';
    entryPoints.forEach(entry => {
        output += `- ${entry}\n`;
    });
    
    // Dependencies
    output += '\n## Dependencies\n\n';
    fileMap.forEach((file, path) => {
        if (file.dependencies.length > 0) {
            output += `### ${path}\n`;
            file.dependencies.forEach(dep => {
                output += `- ${dep}\n`;
            });
            output += '\n';
        }
    });
    
    // Strategy Selector References
    output += '\n## Strategy Selector References\n\n';
    fileMap.forEach((file, path) => {
        if (file.type === '.js' || file.type === '.html') {
            try {
                const content = fs.readFileSync(path, 'utf8');
                if (content.includes('initializeStrategySelector') || 
                    content.includes('strategySelector') || 
                    content.includes('strategy-selector')) {
                    output += `- ${path} (${file.type})\n`;
                }
            } catch (e) {
                // Ignore files we can't read
            }
        }
    });
    
    return output;
}

// Main execution
console.log('Analyzing codebase...');
scanDirectory(ROOT_DIR);

// Write results to file
const markdownOutput = generateMarkdown();
fs.writeFileSync(OUTPUT_FILE, markdownOutput);
console.log(`Analysis complete! Results written to ${OUTPUT_FILE}`);
