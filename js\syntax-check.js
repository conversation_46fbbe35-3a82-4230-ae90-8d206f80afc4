const fs = require('fs');
const path = require('path');

// Path to the file we want to check
const filePath = path.join(__dirname, 'ui', 'websocket-handler.js');

try {
  // Read the file content
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Add line numbers to content for easier debugging
  const contentWithLineNumbers = content.split('\n').map((line, index) => 
    `${index + 1}: ${line}`
  ).join('\n');
  
  console.log('File content with line numbers:');
  console.log('--------------------------------');
  console.log(contentWithLineNumbers);
  console.log('--------------------------------');
  
  // Try to evaluate the file line by line
  const lines = content.split('\n');
  let accumulatedCode = '';
  
  for (let i = 0; i < lines.length; i++) {
    accumulatedCode += lines[i] + '\n';
    
    try {
      // Try to parse (not execute) the code
      Function(accumulatedCode);
    } catch (e) {
      if (e instanceof SyntaxError) {
        console.error(`\nSyntax error detected at line ${i + 1}:`);
        console.error(`Line content: "${lines[i]}"`);
        console.error(`Error: ${e.message}`);
        process.exit(1);
      }
    }
  }
  
  console.log('\nNo syntax errors found!');
} catch (e) {
  console.error('Error reading or processing the file:', e);
}
