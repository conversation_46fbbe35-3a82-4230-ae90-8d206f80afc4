// UI Event Handlers for StarCrypt
// Handles selectors, sliders, menus, and input events

(function () {
  // Track if handlers are already attached to prevent duplicates
  let handlersAttached = false;

  // Ensure global access if needed
  window.attachEventHandlers = function () {
    // Prevent duplicate attachment
    if (handlersAttached) {
      console.warn('[EventHandlers] Handlers already attached, skipping duplicate attachment');
      return;
    }

    // Use event manager if available, fallback to direct attachment
    const addListener = window.eventManager ?
      (el, event, handler) => window.eventManager.addEventListener(el, event, handler) :
      (el, event, handler) => el.addEventListener(event, handler);

    addListener(document, 'change', (e) => {
      try {
        if (e.target.classList.contains('indicator-toggle') || e.target.classList.contains('menu-indicator-toggle')) {
          const name = e.target.getAttribute('data-name')
          toggleIndicator(name, e.target.checked)
        }
        if (e.target.id === 'lowTimeframeToggle') {
          useLowTimeframes = e.target.checked
          renderIndicatorTables()
          updateAllSignalLights()
          logMessages.push(`[${new Date().toLocaleString()}] Toggled low timeframes: ${useLowTimeframes}`)
          updateLogger()
          ws.close()
          connectWebSocket()
        }
        if (e.target.id === 'tokenSelector') {
          const previousPair = currentPair
          currentPair = e.target.value

          // Show loading overlay during coin change
          const loadingOverlay = document.getElementById('loading-overlay')
          if (loadingOverlay) {
            const loadingText = loadingOverlay.querySelector('.loading-text')
            if (loadingText) {
              loadingText.textContent = `Loading ${currentPair.toUpperCase()} data...`
            }
            loadingOverlay.style.display = 'flex'
          }

          // Update charts with new pair
          updateCharts(currentTf, currentPair)

          // Symbol flash/animation handled in animations.js
          logMessages.push(`[${new Date().toLocaleString()}] Changed token pair to ${currentPair}`)
          updateLogger()

          // Send coin change to server via WebSocket
          if (typeof ensureWebSocketAndSend === 'function') {
            ensureWebSocketAndSend(JSON.stringify({
              type: 'coin_change',
              pair: currentPair,
            }))
          } else {
            // Fallback to old method
            if (ws && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'coin_change',
                pair: currentPair,
              }))
            } else {
              ws.close()
              connectWebSocket()
            }
          }

          // Update recent alerts for the new pair
          updateRecentAlerts(currentPair)

          // Hide loading overlay after timeout
          setTimeout(() => {
            if (loadingOverlay) {
              loadingOverlay.style.display = 'none'
            }
            // Force update all signal lights for the new pair
            updateAllSignalLights()
          }, 2000)
        }
        if (e.target.id === 'minConvergence') {
          signalLogicSettings.minConvergence = parseInt(e.target.value)
          updateAllSignalLights()
          logMessages.push(`[${new Date().toLocaleString()}] Updated min convergence to ${signalLogicSettings.minConvergence}`)
          updateLogger()
        }
        if (e.target.id === 'strongSignalWeight') {
          signalLogicSettings.strongSignalWeight = parseInt(e.target.value)
          updateAllSignalLights()
          logMessages.push(`[${new Date().toLocaleString()}] Updated strong signal weight to ${signalLogicSettings.strongSignalWeight}`)
          updateLogger()
        }
        if (e.target.id === 'mildSignalWeight') {
          signalLogicSettings.mildSignalWeight = parseInt(e.target.value)
          updateAllSignalLights()
          logMessages.push(`[${new Date().toLocaleString()}] Updated mild signal weight to ${signalLogicSettings.mildSignalWeight}`)
          updateLogger()
        }
        if (e.target.id === 'signalPriority') {
          signalLogicSettings.priority = e.target.value
          updateAllSignalLights()
          logMessages.push(`[${new Date().toLocaleString()}] Updated signal priority to ${signalLogicSettings.priority}`)
          updateLogger()
        }
        if (e.target.id === 'strategySelector') {
          const strategy = e.target.value
          currentStrategy = strategy
          const strategyNameElement = document.querySelector('.helper-container .section-header h3')
          if (strategyNameElement) {
            strategyNameElement.textContent = `Trading Helper: ${TRADING_STRATEGIES[strategy].name} Strategy`
          }
          const descriptionElement = document.querySelector('.strategy-description')
          if (descriptionElement) {
            descriptionElement.textContent = TRADING_STRATEGIES[strategy].description || 'No description available for this strategy.'
          }
          updateEnabledIndicatorsForStrategy(strategy)
          updateHelperText(strategy)
          updateAdmiralHelper(strategy)
          showStrategyAnimation(strategy)
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'setStrategy', strategy }))
            logMessages.push(`[${new Date().toLocaleString()}] Changed strategy to ${TRADING_STRATEGIES[strategy].name}`)
            updateLogger()
            indicatorsData = {}
            timeframeSignals = {}
            const activeTimeframes = useLowTimeframes ? LOW_TIMEFRAMES : TIMEFRAMES
            activeTimeframes.forEach(tf => {
              ws.send(JSON.stringify({ type: 'requestIndicators', pair: currentPair, timeframe: tf, strategy }))
            })
            initializeSignalLights()
            setTimeout(() => {
              logMessages.push(`[${new Date().toLocaleString()}] Forcing signal light update after strategy change`)
              updateLogger()
              updateAllSignalLights()
              // Enhanced charts handle updates automatically - no need to call updateMiniCharts
            }, 2000)
          } else {
            logMessages.push(`[${new Date().toLocaleString()}] WebSocket not connected, strategy change will apply on reconnection`)
            updateLogger()
          }
        }
      } catch (e) {
        logMessages.push(`[${new Date().toLocaleString()}] Change event error: ${e.message}`)
        updateLogger()
      }
    })
    // Use debounced input handler for better performance
    const debouncedInputHandler = window.eventManager ?
      window.eventManager.addDebouncedListener(document, 'input', (e) => {
        try {
          if (e.target.type === 'range') {
            const indicator = e.target.id.replace(/(Green|Blue|Orange|Red)/, '')
            const green = document.getElementById(`${indicator}Green`).value
            const blue = document.getElementById(`${indicator}Blue`).value
            const orange = document.getElementById(`${indicator}Orange`).value
            const red = document.getElementById(`${indicator}Red`).value
            updateSliderBar(indicator, green, blue, orange, red)
          }
        } catch (e) {
          logMessages.push(`[${new Date().toLocaleString()}] Input event error: ${e.message}`)
          updateLogger()
        }
      }, 50) : // 50ms debounce for smooth slider interaction
      addListener(document, 'input', (e) => {
        try {
          if (e.target.type === 'range') {
            const indicator = e.target.id.replace(/(Green|Blue|Orange|Red)/, '')
            const green = document.getElementById(`${indicator}Green`).value
            const blue = document.getElementById(`${indicator}Blue`).value
            const orange = document.getElementById(`${indicator}Orange`).value
            const red = document.getElementById(`${indicator}Red`).value
            updateSliderBar(indicator, green, blue, orange, red)
          }
        } catch (e) {
          logMessages.push(`[${new Date().toLocaleString()}] Input event error: ${e.message}`)
          updateLogger()
        }
      });

    // Mark handlers as attached
    handlersAttached = true;
    console.log('[EventHandlers] Event handlers attached successfully');
  }

  // Auto-attach on DOMContentLoaded (only once)
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', window.attachEventHandlers);
  } else {
    // DOM already loaded
    window.attachEventHandlers();
  }
})()
