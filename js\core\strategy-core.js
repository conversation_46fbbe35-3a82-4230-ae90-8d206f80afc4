/**
 * Strategy Core - Centralized Strategy Management
 * Handles all strategy-related operations and state management
 */

class StrategyCore {
  constructor() {
    this._initialized = false;
    this._eventHandlers = new Map();
    this.strategies = window.TRADING_STRATEGIES || {};
    this.currentStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa';
    
    // Bind methods
    this.init = this.init.bind(this);
    this.handleStrategyChange = this.handleStrategyChange.bind(this);
    this.updateStrategyUI = this.updateStrategyUI.bind(this);
    this.updateStrategyInfo = this.updateStrategyInfo.bind(this);
    this.notifyStrategyChange = this.notifyStrategyChange.bind(this);
  }

  /**
   * Initialize the strategy core
   */
  init() {
    if (this._initialized) return;
    
    // Set up event listeners
    this._setupEventListeners();
    
    // Initialize current strategy
    this.applyStrategy(this.currentStrategy, { source: 'init' });
    
    this._initialized = true;
    console.log('[StrategyCore] Initialized');
  }

  /**
   * Set up event listeners
   * @private
   */
  _setupEventListeners() {
    // Remove any existing listeners
    this._removeEventListeners();
    
    // Strategy selector changes
    document.addEventListener('change', (e) => {
      if (e.target.matches('#strategySelector, .strategy-selector')) {
        this.handleStrategyChange({
          target: { value: e.target.value },
          preventDefault: () => e.preventDefault(),
          stopPropagation: () => e.stopPropagation()
        });
      }
    }, { passive: true });
    
    // Strategy change events
    document.addEventListener('strategyChanged', (e) => {
      if (e.detail?.fromStrategyCore) return; // Prevent loops
      this.handleStrategyChange(e);
    }, { passive: true });
  }

  /**
   * Remove all event listeners
   * @private
   */
  _removeEventListeners() {
    // Remove any existing listeners
    document.removeEventListener('change', this._boundChangeHandler);
    document.removeEventListener('strategyChanged', this._boundStrategyChangeHandler);
  }

  /**
   * Handle strategy change events - COMPLETELY DISABLED
   */
  handleStrategyChange(event) {
    console.log('[StrategyCore] 🚫 COMPLETELY DISABLED - Unified Signal Commander has full control');
    return;
  }

  init() {
    console.log('[StrategyCore] 🚫 COMPLETELY DISABLED - Unified Signal Commander has full control');
    return;
  }

  applyStrategy() {
    console.log('[StrategyCore] 🚫 COMPLETELY DISABLED - Unified Signal Commander has full control');
    return;
  }

  /**
   * Apply a strategy
   * @param {string} strategyId - The strategy ID to apply
   * @param {Object} options - Additional options
   */
  applyStrategy(strategyId, options = {}) {
    try {
      if (!strategyId || !this.strategies[strategyId]) {
        console.warn(`[StrategyCore] Invalid strategy: ${strategyId}`);
        return false;
      }

      // Don't re-apply the same strategy
      if (strategyId === this.currentStrategy && !options.force) {
        return false;
      }

      console.log(`[StrategyCore] Applying strategy: ${strategyId}`, options);
      
      // Save the previous strategy
      const previousStrategy = this.currentStrategy;
      this.currentStrategy = strategyId;
      
      // Update storage
      localStorage.setItem('currentStrategy', strategyId);
      
      // Update UI
      this.updateStrategyUI();
      this.updateStrategyInfo();
      
      // Notify other components
      this.notifyStrategyChange(strategyId, previousStrategy, options.source);
      
      return true;
    } catch (error) {
      console.error('[StrategyCore] Error applying strategy:', error);
      return false;
    }
  }

  /**
   * Update UI elements for the current strategy
   */
  updateStrategyUI() {
    try {
      // Update strategy selectors
      const selectors = document.querySelectorAll('#strategySelector, .strategy-selector');
      selectors.forEach(select => {
        if (select.value !== this.currentStrategy) {
          select.value = this.currentStrategy;
        }
      });
      
      // Update any other UI elements that show the current strategy
      const strategyNameElements = document.querySelectorAll('.strategy-name, [data-strategy-name]');
      strategyNameElements.forEach(el => {
        el.textContent = this.strategies[this.currentStrategy]?.name || this.currentStrategy;
      });
    } catch (error) {
      console.error('[StrategyCore] Error updating strategy UI:', error);
    }
  }

  /**
   * Update strategy information display
   */
  updateStrategyInfo() {
    try {
      const strategy = this.strategies[this.currentStrategy];
      if (!strategy) return;
      
      // Update description
      const descElements = document.querySelectorAll('.strategy-description, .strategy-info, [data-strategy-description]');
      descElements.forEach(el => {
        el.textContent = strategy.description || 'No description available.';
      });
      
      // Update indicators
      if (Array.isArray(strategy.indicators) && window.updateEnabledIndicators) {
        window.updateEnabledIndicators(strategy.indicators);
      }
    } catch (error) {
      console.error('[StrategyCore] Error updating strategy info:', error);
    }
  }

  /**
   * Notify other components about strategy changes
   * @param {string} newStrategy - The new strategy ID
   * @param {string} previousStrategy - The previous strategy ID
   * @param {string} source - The source of the change
   */
  notifyStrategyChange(newStrategy, previousStrategy, source = 'unknown') {
    try {
      // Dispatch custom event
      const event = new CustomEvent('strategyChanged', {
        detail: {
          strategy: newStrategy,
          previous: previousStrategy,
          source: source,
          timestamp: Date.now(),
          fromStrategyCore: true
        },
        bubbles: true,
        cancelable: true
      });
      
      document.dispatchEvent(event);
      
      // Call any registered callbacks
      if (typeof window.onStrategyChanged === 'function') {
        window.onStrategyChanged(newStrategy, previousStrategy, source);
      }
      
      console.log(`[StrategyCore] Notified about strategy change: ${previousStrategy} -> ${newStrategy} (${source})`);
    } catch (error) {
      console.error('[StrategyCore] Error notifying about strategy change:', error);
    }
  }
  
  /**
   * Clean up resources
   */
  destroy() {
    this._removeEventListeners();
    this._eventHandlers.clear();
    this._initialized = false;
  }
}

// Initialize and export singleton instance
window.StrategyCore = new StrategyCore();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => window.StrategyCore.init());
} else {
  window.StrategyCore.init();
}

export default window.StrategyCore;
