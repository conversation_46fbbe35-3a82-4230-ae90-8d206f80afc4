/**
 * Strategy Handling Migration Script
 * 
 * This script migrates the strategy handling logic to use the new StrategyManager.
 * It should be included after StrategyManager.js and before any other strategy-related code.
 */

(function() {
  console.log('[Migration] Starting strategy handling migration...');
  
  // Wait for StrategyManager to be available
  const MAX_RETRIES = 10;
  const RETRY_DELAY = 100; // ms
  
  function migrateWhenReady(retryCount = 0) {
    if (window.StrategyManager) {
      console.log('[Migration] StrategyManager found, starting migration...');
      performMigration();
    } else if (retryCount < MAX_RETRIES) {
      console.log(`[Migration] Waiting for StrategyManager... (${retryCount + 1}/${MAX_RETRIES})`);
      setTimeout(() => migrateWhenReady(retryCount + 1), RETRY_DELAY);
    } else {
      console.error('[Migration] Failed to find StrategyManager after maximum retries');
    }
  }
  
  function performMigration() {
    try {
      // 1. Patch global functions to use StrategyManager
      patchGlobalFunctions();
      
      // 2. Migrate event listeners
      migrateEventListeners();
      
      // 3. Patch existing strategy-related functions
      patchExistingFunctions();
      
      console.log('[Migration] Strategy handling migration completed successfully');
    } catch (error) {
      console.error('[Migration] Error during migration:', error);
    }
  }
  
  function patchGlobalFunctions() {
    console.log('[Migration] Patching global functions...');
    
    // Replace global strategy functions with StrategyManager equivalents
    const originalFunctions = {
      applySelectedStrategy: window.applySelectedStrategy,
      updateStrategyInfoPanel: window.updateStrategyInfoPanel,
      updateIndicatorsForStrategy: window.updateIndicatorsForStrategy
    };
    
    // Store original functions in case we need them
    window._originalStrategyFunctions = originalFunctions;
    
    // Apply patches
    window.applySelectedStrategy = function(strategyId) {
      console.log('[Migration] applySelectedStrategy called, delegating to StrategyManager');
      return window.StrategyManager.applyStrategy(strategyId, { source: 'legacy' });
    };
    
    window.updateStrategyInfoPanel = function(strategy) {
      console.log('[Migration] updateStrategyInfoPanel called, delegating to StrategyManager');
      if (strategy) {
        window.StrategyManager.applyStrategy(strategy, { source: 'legacy', force: true });
      } else {
        window.StrategyManager.updateUI();
      }
      return true;
    };
    
    window.updateIndicatorsForStrategy = function(strategy) {
      console.log('[Migration] updateIndicatorsForStrategy called, delegating to StrategyManager');
      if (strategy) {
        return window.StrategyManager.applyStrategy(strategy, { 
          source: 'legacy-updateIndicators',
          force: true 
        });
      }
      return false;
    };
    
    // Add a way to access the current strategy
    window.getCurrentStrategy = function() {
      return window.StrategyManager.getCurrentStrategy();
    };
  }
  
  function migrateEventListeners() {
    console.log('[Migration] Migrating event listeners...');
    
    // Get all elements with change listeners
    const elements = document.querySelectorAll('select[id*="strategy"], .strategy-selector');
    
    elements.forEach(el => {
      // Clone the element to remove all event listeners
      const clone = el.cloneNode(true);
      el.parentNode.replaceChild(clone, el);
      
      // Add a single change handler that delegates to StrategyManager
      clone.addEventListener('change', (e) => {
        if (e.target.matches('select[id*="strategy"], .strategy-selector')) {
          window.StrategyManager.handleStrategyChange(e);
        }
      }, { passive: true });
    });
    
    // Remove any global strategy change listeners that might cause duplicates
    const oldHandler = (e) => {
      if (e.type === 'strategyChanged' && !e.detail?.fromStrategyManager) {
        e.stopImmediatePropagation();
      }
    };
    
    document.removeEventListener('strategyChanged', oldHandler, true);
    document.addEventListener('strategyChanged', oldHandler, { capture: true, passive: true });
  }
  
  function patchExistingFunctions() {
    console.log('[Migration] Patching existing functions...');
    
    // Patch menu controller if it exists
    if (window.MenuController) {
      const originalInit = window.MenuController.prototype.initializeStrategySelector;
      window.MenuController.prototype.initializeStrategySelector = function() {
        console.log('[Migration] MenuController.initializeStrategySelector intercepted');
        // Let StrategyManager handle initialization
        window.StrategyManager.init();
        
        // Call original with proper context if needed
        if (originalInit) {
          return originalInit.call(this);
        }
      };
    }
    
    // Patch any other strategy-related functions as needed
    // ...
  }
  
  // Start migration
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', migrateWhenReady);
  } else {
    migrateWhenReady();
  }
})();
