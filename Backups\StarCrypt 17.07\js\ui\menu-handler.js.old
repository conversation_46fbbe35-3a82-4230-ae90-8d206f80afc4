// js/ui/menu-handler.js
/**
 * StarCrypt Menu Handler
 * 
 * Centralized menu management for all UI menus
 */

class MenuHandler {
  constructor() {
    this.activeMenu = null;
    this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu'];
    this.initialize();
  }

  initialize() {
    // Initialize all menus
    this.setupMenuButtons();
    this.setupClickOutsideHandler();
    this.ensureMenuContainersExist();
    console.log('[MenuHandler] Initialized menu handler');
  }

  ensureMenuContainersExist() {
    // Check if each menu container exists, create if not
    this.menus.forEach(menuId => {
      let menu = document.getElementById(menuId);
      if (!menu) {
        console.log(`[MenuHandler] Creating empty menu container: ${menuId}`);
        menu = document.createElement('div');
        menu.id = menuId;
        menu.className = `menu-container ${menuId.toLowerCase().replace('menu', '-menu')}`;
        menu.style.display = 'none';
        
        // Create a simple header with close button for all menus
        const header = document.createElement('div');
        header.className = 'menu-header';
        
        const title = document.createElement('h3');
        title.textContent = menuId.replace(/([A-Z])/g, ' $1').replace('Menu', '').trim();
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-menu';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.closeAllMenus();
        });
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // Create content container
        const content = document.createElement('div');
        content.className = 'menu-content';
        content.id = `${menuId}Content`;
        
        // Add elements to menu
        menu.appendChild(header);
        menu.appendChild(content);
        
        // Append to ticker container or body if ticker not found
        const tickerContainer = document.querySelector('.ticker-container');
        if (tickerContainer) {
          tickerContainer.style.position = 'relative';
          tickerContainer.appendChild(menu);
        } else {
          document.body.appendChild(menu);
        }
      } else {
        console.log(`[MenuHandler] Found existing ${menuId} container`);
      }
    });
    
    // Check for menu buttons and log status
    const menuButtons = [
      'strategyButton', 'toggleMenuButton', 'toggleThresholdsButton', 'toggleLogicButton'
    ]
    
    menuButtons.forEach(buttonId => {
      const button = document.getElementById(buttonId)
      console.log(`[MenuHandler] Menu button ${buttonId}: ${button ? 'Found' : 'Not found'}`)
    })
  }

  setupMenuButtons() {
    // Remove any existing click handlers to prevent duplicates
    if (this.handleMenuClick) {
      document.removeEventListener('click', this.handleMenuClick);
    }
    
    // Create new click handler
    this.handleMenuClick = (e) => {
      try {
        // Handle menu button clicks
        if (e.target.closest('#toggleThresholdsButton')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('thresholdsMenu');
          return;
        }
        
        if (e.target.closest('#toggleLogicButton')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('logicMenu');
          return;
        }
        
        if (e.target.closest('#toggleMenuButton')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('indicatorMenu');
          return;
        }
        
        if (e.target.closest('#strategyButton')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('strategyMenu');
          return;
        }
        
        // Close menu when clicking outside
        if (this.activeMenu && !e.target.closest(`#${this.activeMenu}`) && !e.target.closest(`[data-menu="${this.activeMenu}"]`)) {
          this.closeAllMenus();
        }
      } catch (error) {
        console.error('[MenuHandler] Error in menu click handler:', error);
      }
    };
    
    // Add the click handler to the document
    document.addEventListener('click', this.handleMenuClick);
  }
      // Check if we're clicking on a menu button or its children
      const button = e.target.closest('[id$="Button"], [id^="toggle"]');
      if (!button) return;
      
      // Skip if the click is inside an already open menu
      if (e.target.closest('.menu-container')) {
        return;
      }
      
      try {
        // Strategy menu
        if (e.target.matches('#strategyButton, #strategyButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('strategyMenu');
          return;
        }

        // Indicator menu
        if (e.target.matches('#toggleMenuButton, #toggleMenuButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('indicatorMenu');
          return;
        }

        // Thresholds menu
        if (e.target.matches('#toggleThresholdsButton, #toggleThresholdsButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('thresholdsMenu');
          return;
        }

        // Logic menu
        if (e.target.matches('#toggleLogicButton, #toggleLogicButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleMenu('logicMenu');
          return;
        }

        // Chart visibility
        if (e.target.matches('#toggleChartButton, #toggleChartButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleChartVisibility();
          return;
        }

        // Reset layout
        if (e.target.matches('#resetLayoutButton, #resetLayoutButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.resetLayout();
          return;
        }

        // Fullscreen
        if (e.target.matches('#fullscreenButton, #fullscreenButton *')) {
          e.preventDefault();
          e.stopPropagation();
          this.toggleFullscreen();
          return;
        }
      } catch (error) {
        console.error('[MenuHandler] Error in menu button handler:', error);
      }
    };
    
    // Add the click handler to the document

  toggleMenu(menuId) {
    try {
      console.log(`[MenuHandler] Toggling menu: ${menuId}`);
      
      // Ensure menu container exists
      this.ensureMenuContainersExist();
      
      const menu = document.getElementById(menuId);
      if (!menu) {
        console.error(`[MenuHandler] Menu not found: ${menuId}`);
        return;
      }
      
      // If clicking the currently active menu button, close the menu
      if (this.activeMenu === menuId) {
        this.closeAllMenus();
        return;
      }
      
      // Close any open menu first
      this.closeAllMenus();
      
      // Open the clicked menu
      menu.style.display = 'block';
      this.activeMenu = menuId;
            console.error('[MenuHandler] initializeLogicMenu function not found');
          }
        } else if (menuId === 'indicatorMenu') {
          console.log('[MenuHandler] Showing indicator menu');
          // Add any indicator menu specific initialization here
        } else if (menuId === 'strategyMenu') {
          console.log('[MenuHandler] Showing strategy menu');
          // Add any strategy menu specific initialization here
        }
      }
      
      console.log(`[MenuHandler] Toggled menu: ${menuId}, active: ${menu.classList.contains('active')}`);
      return !isCurrentlyActive; // Return whether the menu is now open
    } catch (error) {
      console.error(`[MenuHandler] Error toggling menu ${menuId}:`, error)
    }
  }

  toggleChartVisibility() {
    const chartColumn = document.querySelector('.chart-column');
    if (chartColumn) {
        const isHidden = chartColumn.style.display === 'none';
        chartColumn.style.display = isHidden ? '' : 'none';
        console.log(`[MenuHandler] Toggled chart visibility to ${isHidden ? 'visible' : 'hidden'}`);
    } else {
        console.warn('[MenuHandler] Chart column not found.');
    }
  }

  resetLayout() {
      console.log('[MenuHandler] Resetting layout by reloading the page.');
      if (window.confirm('This will reset the chart and all indicators by reloading the page. Are you sure?')) {
          location.reload();
      }
  }

  toggleFullscreen() {
      if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen().catch(err => {
              console.error(`[MenuHandler] Error enabling full-screen: ${err.message}`);
          });
      } else {
          if (document.exitFullscreen) {
              document.exitFullscreen();
          }
      }
  }

  closeAllMenus() {
    this.menus.forEach(menuId => {
      const menu = document.getElementById(menuId);
      if (menu) {
        menu.classList.remove('active');
        menu.style.display = 'none';
      }
    });
    this.activeMenu = null;
  }

  getButtonIdForMenu(menuId) {
    switch(menuId) {
      case 'thresholdsMenu': return 'toggleThresholdsButton';
      case 'logicMenu': return 'toggleLogicButton';
      case 'indicatorMenu': return 'toggleMenuButton';
      case 'strategyMenu': return 'strategyButton';
      default: return null;
    }
  }

  setupClickOutsideHandler() {
    document.addEventListener('click', (e) => {
      try {
        // If clicking inside a menu or on a menu button, don't close
        if (e.target.closest('.menu-content') || e.target.closest('.menu-button')) {
          return
        }

        // Close all menus when clicking outside
        this.closeAllMenus()
      } catch (error) {
        console.error('[MenuHandler] Error in click outside handler:', error)
      }
    })
  }
}

// Initialize the menu handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.menuHandler = new MenuHandler();
});

// Make the MenuHandler available globally
window.MenuHandler = MenuHandler
