#!/usr/bin/env node

/**
 * CSV to JSON Converter for StarCrypt Historical Data
 * 
 * This utility converts Kraken CSV historical data files to JSON format
 * for faster loading in StarCrypt.
 * 
 * Usage:
 * node convert-csv-to-json.js
 * 
 * Place your CSV files in the 'data' folder with naming convention:
 * {pair}_{timeframe}.csv (e.g., xbtusdt_1h.csv)
 */

const fs = require('fs');
const path = require('path');

// Configuration
const DATA_DIR = path.join(__dirname, 'data');
const SUPPORTED_PAIRS = ['xbtusdt', 'ethusdt', 'ltcusdt', 'xrpusdt', 'adausdt', 'solusdt', 'dotusdt', 'dogeusdt'];
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

// Parse CSV data to OHLC format (same function as in server.js)
function parseCSVToOHLC(csvData, filename) {
  try {
    const lines = csvData.trim().split('\n');
    if (lines.length < 2) {
      console.error(`❌ ${filename}: CSV file has insufficient data`);
      return null;
    }
    
    // Get header to determine column positions
    const header = lines[0].toLowerCase().split(',').map(h => h.trim());
    console.log(`📋 ${filename}: Found columns: ${header.join(', ')}`);
    
    // Find column indices (flexible column mapping)
    const timeIndex = header.findIndex(h => h.includes('time') || h.includes('date') || h.includes('timestamp'));
    const openIndex = header.findIndex(h => h.includes('open'));
    const highIndex = header.findIndex(h => h.includes('high'));
    const lowIndex = header.findIndex(h => h.includes('low'));
    const closeIndex = header.findIndex(h => h.includes('close'));
    const volumeIndex = header.findIndex(h => h.includes('volume') || h.includes('vol'));
    
    if (timeIndex === -1 || openIndex === -1 || highIndex === -1 || lowIndex === -1 || closeIndex === -1) {
      console.error(`❌ ${filename}: Missing required columns (time, open, high, low, close)`);
      console.error(`   Available columns: ${header.join(', ')}`);
      return null;
    }
    
    console.log(`✅ ${filename}: Column mapping - Time:${timeIndex}, O:${openIndex}, H:${highIndex}, L:${lowIndex}, C:${closeIndex}, V:${volumeIndex}`);
    
    const ohlcData = [];
    let skippedRows = 0;
    
    // Process data rows
    for (let i = 1; i < lines.length; i++) {
      const row = lines[i].split(',').map(cell => cell.trim());
      
      if (row.length < Math.max(timeIndex, openIndex, highIndex, lowIndex, closeIndex) + 1) {
        skippedRows++;
        continue; // Skip incomplete rows
      }
      
      try {
        // Parse timestamp (handle various formats)
        let timestamp = row[timeIndex];
        let time;
        
        if (timestamp.includes('-') || timestamp.includes('/')) {
          // Date string format
          time = Math.floor(new Date(timestamp).getTime() / 1000);
        } else {
          // Unix timestamp (seconds or milliseconds)
          time = parseInt(timestamp);
          if (time > 1e12) {
            time = Math.floor(time / 1000); // Convert milliseconds to seconds
          }
        }
        
        const open = parseFloat(row[openIndex]);
        const high = parseFloat(row[highIndex]);
        const low = parseFloat(row[lowIndex]);
        const close = parseFloat(row[closeIndex]);
        const volume = volumeIndex !== -1 ? parseFloat(row[volumeIndex]) : 0;
        
        // Validate data
        if (isNaN(time) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
          skippedRows++;
          continue; // Skip invalid rows
        }
        
        // Create OHLC object in Kraken format
        ohlcData.push({
          time: time,
          open: open,
          high: high,
          low: low,
          close: close,
          vwap: (high + low + close) / 3, // Approximate VWAP
          volume: volume,
          count: 1
        });
      } catch (rowError) {
        skippedRows++;
        continue;
      }
    }
    
    // Sort by timestamp
    ohlcData.sort((a, b) => a.time - b.time);
    
    if (skippedRows > 0) {
      console.log(`⚠️  ${filename}: Skipped ${skippedRows} invalid rows`);
    }
    
    console.log(`✅ ${filename}: Successfully parsed ${ohlcData.length} candles`);
    return ohlcData;
    
  } catch (error) {
    console.error(`❌ ${filename}: Error parsing CSV data: ${error.message}`);
    return null;
  }
}

// Main conversion function
function convertCSVFiles() {
  console.log('🚀 StarCrypt CSV to JSON Converter');
  console.log('=====================================');
  
  // Check if data directory exists
  if (!fs.existsSync(DATA_DIR)) {
    console.error(`❌ Data directory not found: ${DATA_DIR}`);
    console.log('💡 Please create a "data" folder and place your CSV files there.');
    return;
  }
  
  // Get all CSV files in the data directory
  const files = fs.readdirSync(DATA_DIR).filter(file => file.endsWith('.csv'));
  
  if (files.length === 0) {
    console.log('❌ No CSV files found in the data directory.');
    console.log('💡 Place your CSV files with naming convention: {pair}_{timeframe}.csv');
    console.log('   Example: xbtusdt_1h.csv, ethusdt_4h.csv');
    return;
  }
  
  console.log(`📁 Found ${files.length} CSV files:`);
  files.forEach(file => console.log(`   - ${file}`));
  console.log('');
  
  let converted = 0;
  let failed = 0;
  
  // Process each CSV file
  files.forEach(csvFile => {
    try {
      const csvPath = path.join(DATA_DIR, csvFile);
      const jsonFile = csvFile.replace('.csv', '.json');
      const jsonPath = path.join(DATA_DIR, jsonFile);
      
      // Skip if JSON already exists and is newer
      if (fs.existsSync(jsonPath)) {
        const csvStats = fs.statSync(csvPath);
        const jsonStats = fs.statSync(jsonPath);
        if (jsonStats.mtime > csvStats.mtime) {
          console.log(`⏭️  ${csvFile}: JSON file already exists and is newer, skipping`);
          return;
        }
      }
      
      console.log(`🔄 Processing: ${csvFile}`);
      
      // Read and parse CSV
      const csvData = fs.readFileSync(csvPath, 'utf8');
      const ohlcData = parseCSVToOHLC(csvData, csvFile);
      
      if (ohlcData && ohlcData.length > 0) {
        // Write JSON file
        fs.writeFileSync(jsonPath, JSON.stringify(ohlcData, null, 2));
        console.log(`💾 Saved: ${jsonFile} (${ohlcData.length} candles)`);
        converted++;
      } else {
        console.error(`❌ Failed to convert: ${csvFile}`);
        failed++;
      }
      
      console.log('');
      
    } catch (error) {
      console.error(`❌ Error processing ${csvFile}: ${error.message}`);
      failed++;
    }
  });
  
  console.log('=====================================');
  console.log(`✅ Conversion complete!`);
  console.log(`   Converted: ${converted} files`);
  console.log(`   Failed: ${failed} files`);
  
  if (converted > 0) {
    console.log('');
    console.log('🎉 Your historical data is now ready for StarCrypt!');
    console.log('   Restart your StarCrypt server to use the new data.');
  }
}

// Run the converter
if (require.main === module) {
  convertCSVFiles();
}

module.exports = { parseCSVToOHLC, convertCSVFiles };
