module.exports = {
  // Kraken API Configuration
  KRAKEN: {
    BASE_URL: 'https://api.kraken.com/0/public',
    WS_URL: 'wss://ws.kraken.com',
    // Rate limiting (3 requests per second, 15 per 10-second window for public API)
    RATE_LIMIT: {
      REQUESTS_PER_INTERVAL: 3,
      INTERVAL_MS: 1000,
      MAX_RETRIES: 3,
      RETRY_DELAY: 1000,
    },
    // WebSocket configuration - Increased timeouts for stability
    WS: {
      PING_INTERVAL: 60000,    // 60 seconds (increased from 30)
      PONG_TIMEOUT: 30000,     // 30 seconds (increased from 10)
      RECONNECT_DELAY: 5000,   // 5 seconds
      MAX_RECONNECT_ATTEMPTS: 5,
    },
    // Data fetching
    FETCH: {
      COOLDOWN: 12000,         // 12 seconds between fetches for same pair/timeframe
      MAX_CANDLES: 720,        // Maximum candles to fetch per request
    },

    // 🎯 KRAKEN_PAIRS: StarCrypt pairs to Kraken WebSocket pairs mapping
    KRAKEN_PAIRS: {
      'xbtusdt': 'XBT/USDT',
      'ethusdt': 'ETH/USDT',
      'ltcusdt': 'LTC/USDT',
      'xrpusdt': 'XRP/USDT',
      'adausdt': 'ADA/USDT',
      'solusdt': 'SOL/USDT',
      'dotusdt': 'DOT/USDT',
      'bnbusdt': 'BNB/USDT',
      'avaxusdt': 'AVAX/USDT',
    },
  },
  
  // Timeframes and their corresponding Kraken intervals (in minutes)
  TIMEFRAMES: {
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '4h': 240,
    '1d': 1440,
    '1w': 10080,
  },
  
  // 🎯 STARCRYPT PAIRS: Map StarCrypt pairs to Kraken WebSocket pairs
  PAIRS: {
    // StarCrypt format → Kraken WebSocket format
    'xbtusdt': 'XBT/USDT',
    'ethusdt': 'ETH/USDT',
    'ltcusdt': 'LTC/USDT',
    'xrpusdt': 'XRP/USDT',
    'adausdt': 'ADA/USDT',
    'solusdt': 'SOL/USDT',
    'dotusdt': 'DOT/USDT',
    'bnbusdt': 'BNB/USDT',
    'avaxusdt': 'AVAX/USDT',
    // Legacy format support
    'BTC/USD': 'XBT/USD',
    'ETH/USD': 'ETH/USD',
    'XRP/USD': 'XRP/USD',
    'LTC/USD': 'LTC/USD',
    'ADA/USD': 'ADA/USD',
    'DOT/USD': 'DOT/USD',
  },


  
  // Default values
  DEFAULTS: {
    PAIR: 'BTC/USD',
    TIMEFRAME: '1h',
    STRATEGY: 'neural_network_navigator',
  },
  
  // Indicator settings
  INDICATORS: {
    RSI_PERIOD: 14,
    BB_PERIOD: 20,
    BB_STDDEV: 2,
    MACD_FAST: 12,
    MACD_SLOW: 26,
    MACD_SIGNAL: 9,
    ATR_PERIOD: 14,
    ADX_PERIOD: 14,
    STOCH_RSI_PERIOD: 14,
    WILLIAMS_R_PERIOD: 14,
    MFI_PERIOD: 14,
  },
};
