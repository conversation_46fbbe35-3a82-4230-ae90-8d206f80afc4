/**
 * WebSocket Setup
 * Initializes WebSocket connection
 */

// Add version for debugging
const WEBSOCKET_INIT_VERSION = '1.0.0'

// Track initialization state
let isInitialized = false

// Main initialization function
function initializeWebSocket() {
  // Prevent duplicate initialization
  if (isInitialized) {
    console.log('[WebSocket] Already initialized, skipping setup')
    return
  }

  console.log(`[WebSocket] Setting up WebSocket components v${WEBSOCKET_INIT_VERSION}...`)

  try {
    // Check if instances already exist
    if (window.wsManager && window.wsProcessor) {
      console.log('[WebSocket] Found existing instances, skipping setup')
      return
    }

    // Initialize WebSocketManager with default configuration
    const wsManager = new WebSocketManager({
      url: window.WEBSOCKET_URL || 'ws://localhost:8080',
      reconnect: true,
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      maxReconnectInterval: 30000,
      reconnectDecay: 1.5,
      timeout: 10000,
      debug: true,
    })

    // Initialize WebSocketProcessorV2
    const wsProcessor = new WebSocketProcessorV2({
      maxBatchSize: 10,
      maxQueueSize: 1000,
      processDelay: 10,
      maxDepth: 5,
      maxConsecutiveErrors: 10,
      errorResetTime: 60000,
    })

    // Make instances available globally
    window.wsManager = wsManager
    window.wsProcessor = wsProcessor

    // Log successful setup
    console.log('[WebSocket] Components set up successfully')
    console.log('[WebSocket] Manager:', wsManager)
    console.log('[WebSocket] Processor:', wsProcessor)

    // Dispatch custom event for other components
    const event = new CustomEvent('websocket:initialized', {
      detail: { wsManager, wsProcessor },
    })
    document.dispatchEvent(event)

    // Mark as initialized
    isInitialized = true
  } catch (error) {
    console.error('[WebSocket] Setup failed:', error)
    // Don't reload or show error overlay since the page is already working
  }
}

// Initialize when DOM is ready
function setupWebSocketInitialization() {
  // Remove existing event listeners to prevent duplicates
  document.removeEventListener('DOMContentLoaded', initializeWebSocket)

  // Add new listener
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Give a bit more time for page load
      setTimeout(initializeWebSocket, 1000)
    })
  } else {
    // DOM already loaded, initialize immediately
    setTimeout(initializeWebSocket, 1000)
  }
}

// Set up initialization
setupWebSocketInitialization()

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    initializeWebSocket,
    WEBSOCKET_INIT_VERSION,
    setupWebSocketInitialization,
  }
}
