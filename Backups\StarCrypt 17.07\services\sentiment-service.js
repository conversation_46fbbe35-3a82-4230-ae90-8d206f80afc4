const { OpenAI } = require('openai');
const fs = require('fs').promises;
const path = require('path');
let vader;

try {
    vader = require('vader-sentiment');
} catch (error) {
    console.warn('VADER sentiment analysis not available. Falling back to basic sentiment analysis.');
}

class SentimentService {
    constructor() {
        this.vader = vader?.SentimentIntensityAnalyzer ? new vader.SentimentIntensityAnalyzer() : null;
        
        // Only initialize xaiClient if API key is available
        if (process.env.XAI_API_KEY) {
            this.xaiClient = new OpenAI({
                apiKey: process.env.XAI_API_KEY,
                baseURL: "https://api.x.ai/v1"
            });
        } else {
            console.warn('XAI_API_KEY not found. Using fallback sentiment analysis.');
            this.xaiClient = null;
        }
        
        this.cacheFile = path.join(__dirname, '../data/sentiment-cache.json');
        this.cache = [];
        this.analysisInterval = null;
        this.initialize();
        this.setupScheduler();
    }

    setupScheduler() {
        // Clear any existing interval
        if (this.analysisInterval) {
            clearInterval(this.analysisInterval);
        }

        // Run analysis immediately and then every 10 minutes
        this.analyzeSentiment().catch(console.error);
        this.analysisInterval = setInterval(() => {
            this.analyzeSentiment().catch(console.error);
        }, 10 * 60 * 1000); // 10 minutes

        console.log('Sentiment analysis scheduler started');
    }

    async initialize() {
        try {
            await fs.mkdir(path.dirname(this.cacheFile), { recursive: true });
            await this.loadCache();
            setInterval(() => this.analyzeSentiment(), 10 * 60 * 1000); // Every 10 minutes
        } catch (error) {
            console.error('Failed to initialize SentimentService:', error);
        }
    }

    async loadCache() {
        try {
            const data = await fs.readFile(this.cacheFile, 'utf8');
            this.cache = JSON.parse(data);
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error('Error loading sentiment cache:', error);
            }
            this.cache = [];
        }
    }

    async saveCache() {
        try {
            // Keep only last 7 days of data
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            
            this.cache = this.cache.filter(entry => 
                new Date(entry.timestamp) > oneWeekAgo
            );
            
            await fs.writeFile(this.cacheFile, JSON.stringify(this.cache, null, 2));
        } catch (error) {
            console.error('Error saving sentiment cache:', error);
        }
    }

    async fetchSocialPosts() {
        // Replace with actual X API implementation or use a web scraping service
        // For now, we'll use a placeholder
        return [
            "Bitcoin is looking strong today!",
            "Not sure about this market movement...",
            "HODLing through the volatility!"
        ];
    }

    basicSentimentAnalysis(text) {
        try {
            if (!text) return { positive: 0, negative: 0, neutral: 1, compound: 0 };
            
            // Ensure text is a string
            const textStr = String(text || '').toLowerCase();
            if (!textStr.trim()) return { positive: 0, negative: 0, neutral: 1, compound: 0 };
            
            const positiveWords = ['bull', 'bullish', 'moon', 'mooning', 'pump', 'pumping', 'long', 'buy', 'buying', 'up', 'rise', 'rising', 'growth', 'profit', 'profitable', 'success', 'successful', 'win', 'winning', 'strong', 'strength', 'recovery', 'recovering', 'rally', 'rallied'];
            const negativeWords = ['bear', 'bearish', 'dump', 'dumping', 'short', 'sell', 'selling', 'down', 'drop', 'dropping', 'fall', 'falling', 'loss', 'losing', 'fail', 'failure', 'weak', 'weakness', 'decline', 'declining', 'crash', 'crashed', 'plummet', 'plummeting'];
            
            const words = textStr.split(/\s+/);
            let positive = 0;
            let negative = 0;
            
            for (const word of words) {
                if (positiveWords.includes(word)) positive++;
                if (negativeWords.includes(word)) negative++;
            }
            
            const total = positive + negative || 1;
            const compound = (positive - negative) / total;
            
            return {
                positive: positive / total,
                negative: negative / total,
                neutral: Math.max(0, 1 - ((positive + negative) / total)), // Ensure neutral is not negative
                compound: compound
            };
        } catch (error) {
            console.error('Error in basic sentiment analysis:', error);
            return { positive: 0, negative: 0, neutral: 1, compound: 0 };
        }
    }

    async analyzeWithXAI(posts) {
        if (!this.xaiClient) {
            console.warn('XAI client not available. Falling back to VADER or basic sentiment analysis.');
            return this.analyzeWithVader(posts);
        }
        
        try {
            const prompt = `Analyze the sentiment of these Bitcoin/BTC posts: ${JSON.stringify(posts, null, 2)}\n` +
                         `Return a JSON object with counts of positive, negative, and neutral sentiments.`;
            
            const response = await this.xaiClient.chat.completions.create({
                model: "grok-3-mini",
                messages: [{
                    role: "user",
                    content: prompt
                }],
                temperature: 0.7,
                max_tokens: 100
            });
            
            // Try to parse the JSON response
            try {
                const content = response.choices[0].message.content;
                // Handle cases where response might be wrapped in markdown code blocks
                const jsonMatch = content.match(/```(?:json)?\n([\s\S]*?)\n```/) || [null, content];
                return JSON.parse(jsonMatch[1] || jsonMatch[0]);
            } catch (parseError) {
                console.error('Failed to parse Grok response:', parseError);
                return null;
            }
        } catch (error) {
            console.error('xAI Grok analysis failed:', error);
            return null;
        }
    }

    mapToColor(sentiment) {
        if (!sentiment || typeof sentiment !== 'object') return 'gray';
        
        const total = Object.values(sentiment).reduce((sum, val) => sum + (typeof val === 'number' ? val : 0), 0);
        if (total === 0) return 'gray';
        
        const posRatio = (sentiment.positive || 0) / total;
        const negRatio = (sentiment.negative || 0) / total;
        
        if (posRatio >= 0.6) return 'darkgreen';
        if (posRatio >= 0.3) return 'lightgreen';
        if (negRatio >= 0.6) return 'darkred';
        if (negRatio >= 0.3) return 'lightred';
        return 'gray';
    }

    async analyzeWithVader(text) {
        if (!this.vader || !text) {
            // Fallback to basic sentiment analysis if VADER is not available or no text provided
            return this.basicSentimentAnalysis(text || '');
        }
        try {
            // Check if the vader instance has the polarityScores method
            if (typeof this.vader.polarity_scores === 'function') {
                return this.vader.polarity_scores(text);
            }
            // Fallback to basic analysis if the method doesn't exist
            return this.basicSentimentAnalysis(text);
        } catch (error) {
            console.error('Error in VADER sentiment analysis:', error);
            return this.basicSentimentAnalysis(text || '');
        }
    }

    async analyzeSentiment() {
        try {
            const posts = await this.fetchSocialPosts();
            let results;
            
            // If we have posts, analyze them, otherwise use a default neutral sentiment
            if (posts && posts.length > 0) {
                results = await this.analyzeWithXAI(posts);
                
                if (!results) {
                    console.log('Falling back to VADER analysis');
                    results = await this.analyzeWithVader(posts.join(' '));
                }
            } else {
                console.log('No posts to analyze, using neutral sentiment');
                results = { positive: 0, negative: 0, neutral: 1, compound: 0 };
            }
            
            const timestamp = new Date().toISOString();
            this.cache.push({ timestamp, results });
            await this.saveCache();
            
            return this.getTimeframeAnalysis();
        } catch (error) {
            console.error('Error in sentiment analysis:', error);
            return this.getTimeframeAnalysis(); // Return cached results if available
        }
    }

    getTimeframeAnalysis() {
        const now = new Date();
        const timeframes = {
            '1m': 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000,
            '1w': 7 * 24 * 60 * 60 * 1000
        };
        
        const analysis = {};
        
        for (const [tf, ms] of Object.entries(timeframes)) {
            const cutoff = new Date(now - ms);
            const relevantEntries = this.cache.filter(
                entry => new Date(entry.timestamp) > cutoff
            );
            
            const combined = relevantEntries.reduce((acc, { results }) => {
                Object.keys(acc).forEach(k => acc[k] += (results[k] || 0));
                return acc;
            }, { positive: 0, negative: 0, neutral: 0 });
            
            analysis[tf] = {
                sentiment: combined,
                color: this.mapToColor(combined)
            };
        }
        
        return analysis;
    }
}

// Singleton instance
const sentimentService = new SentimentService();
module.exports = sentimentService;
