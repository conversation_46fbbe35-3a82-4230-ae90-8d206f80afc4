/* Market Trend Visualization Styles */

/* Base Container */
.market-trend-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background: #1e1e2d;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #e0e0e0;
}

/* Trend Strength Indicator */
.trend-strength {
  margin-bottom: 25px;
}

.trend-strength h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #a0a0c0;
}

.trend-meter {
  position: relative;
  height: 30px;
  background: #2a2a3c;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.trend-bar {
  position: absolute;
  top: 0;
  height: 100%;
  width: 0;
  transition: width 0.5s ease, left 0.5s ease, background-color 0.5s ease;
  border-radius: 15px;
}

.trend-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #888;
}

.trend-value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 10;
}

/* Market Phase Indicator */
.market-phase {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 1px;
  margin: 10px 0;
  transition: all 0.3s ease;
}

.market-phase.trending-up {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.5);
}

.market-phase.trending-down {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.5);
}

.market-phase.ranging {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.5);
}

/* Key Levels */n.key-levels {
  margin-top: 25px;
}

.key-levels h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #a0a0c0;
}

.level {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 6px;
  border-radius: 6px;
  background: #2a2a3c;
  font-size: 14px;
  transition: all 0.3s ease;
}

.level.support {
  border-left: 4px solid #4caf50;
}

.level.resistance {
  border-left: 4px solid #f44336;
}

.level-value {
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .market-trend-container {
    padding: 15px;
    margin: 10px;
  }
  
  .trend-meter {
    height: 25px;
  }
  
  .trend-value {
    font-size: 12px;
  }
  
  .market-phase {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* Animation for trend changes */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.trend-update {
  animation: pulse 0.5s ease-in-out;
}

/* Tooltip Styles */n.tooltip {
  position: relative;
  display: inline-block;
  cursor: help;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 13px;
  line-height: 1.4;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}
