/**
 * @file Manages the theme toggle UI element.
 * @module ThemeToggle
 */

(function(window, document) {
  'use strict';

  class ThemeToggle {
    constructor() {
      this.toggleButton = document.getElementById('themeToggle');
      this.container = document.getElementById('themeToggleContainer');
      if (!this.toggleButton) {
        console.warn('[ThemeToggle] Theme toggle button (#themeToggle) not found.');
      }
      if (!this.container) {
        console.warn('[ThemeToggle] Theme toggle container (#themeToggleContainer) not found.');
      }
    }

    /**
     * Initializes the ThemeToggle, setting up event listeners.
     */
    init() {
      console.log('[ThemeToggle] Initializing...');
      if (this.toggleButton) {
        this.toggleButton.addEventListener('click', this.handleToggleClick.bind(this));
      }
      this.updateToggleButtonState();
    }

    /**
     * Handles the click event on the theme toggle button.
     */
    handleToggleClick() {
      if (window.ThemeManager) {
        window.ThemeManager.toggleTheme();
        this.updateToggleButtonState();
      } else {
        console.error('[ThemeToggle] ThemeManager not available.');
      }
    }

    /**
     * Updates the visual state of the toggle button based on the current theme.
     */
    updateToggleButtonState() {
      const currentTheme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
      if (this.toggleButton) {
        if (currentTheme === 'dark') {
          this.toggleButton.classList.add('active');
          // You might want to change text or icon here
          this.toggleButton.textContent = 'Dark Mode';
        } else {
          this.toggleButton.classList.remove('active');
          // You might want to change text or icon here
          this.toggleButton.textContent = 'Light Mode';
        }
      }
    }
  }

  // Expose to the global scope
  window.ThemeToggle = ThemeToggle;

})(window, document);
