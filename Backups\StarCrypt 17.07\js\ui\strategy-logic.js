// Extracted from index.html <script> block and inline JS
// <PERSON>les strategy logic, initialization, and helper functions for StarCrypt

// StarCrypt Strategy Logic Module
// Extracted and modularized from index.html



// Helper functions and update logic (extracted and attached to window for global access)
window.showStrategyAnimation = function (strategy) {
  // Store the current state of mini charts only (not indicators)
  const miniChartState = {}
  Object.keys(window.INDICATORS).forEach(type => {
    window.INDICATORS[type].forEach(ind => {
      const canvas = document.getElementById(`mini-chart-canvas-${ind}`)
      if (canvas) {
        miniChartState[ind] = {
          width: canvas.width,
          height: canvas.height,
        }
      }
    })
  })

  // Remove any existing animation containers first
  const existingContainer = document.querySelector('.strategy-switch-animation')
  if (existingContainer) {
    dismissStrategyAnimation(existingContainer)
  }

  // Create a new animation container
  const animationContainer = document.createElement('div')
  animationContainer.className = 'strategy-switch-animation'
  // Only store mini chart state, not indicator state
  animationContainer.setAttribute('data-mini-chart-state', JSON.stringify(miniChartState))
  document.body.appendChild(animationContainer)

  // Add click event listener to dismiss the animation
  animationContainer.addEventListener('click', () => {
    dismissStrategyAnimation(animationContainer)
  })

  // Get strategy details
  const strategyDetails = window.TRADING_STRATEGIES[strategy]
  if (!strategyDetails) return

  // Create animation content
  const content = document.createElement('div')
  content.className = 'strategy-switch-content'

  // Add strategy name with dynamic color
  const nameElement = document.createElement('div')
  nameElement.className = 'strategy-switch-name'
  nameElement.textContent = strategyDetails.name
  nameElement.style.color = getStrategyColor(strategy)
  content.appendChild(nameElement)

  // Add strategy description with fade-in effect
  const descriptionElement = document.createElement('div')
  descriptionElement.className = 'strategy-switch-description'
  descriptionElement.textContent = strategyDetails.description || ''
  descriptionElement.style.color = getStrategyColor(strategy)
  descriptionElement.style.opacity = '0'
  content.appendChild(descriptionElement)

  // Add click instruction
  const clickInstruction = document.createElement('div')
  clickInstruction.className = 'click-instruction'
  clickInstruction.textContent = 'Click anywhere to dismiss'
  clickInstruction.style.opacity = '0'
  content.appendChild(clickInstruction)

  // Add indicator dots
  const indicatorsContainer = document.createElement('div')
  indicatorsContainer.className = 'strategy-switch-indicators'

  // Add indicator dots with staggered animation and chevron animations
  strategyDetails.indicators.forEach((ind, index) => {
    const dotContainer = document.createElement('div')
    dotContainer.className = 'indicator-dot-container'

    // Create the indicator dot
    const dot = document.createElement('div')
    dot.className = 'strategy-indicator-dot'
    dot.style.backgroundColor = getIndicatorColor(ind)
    dot.style.animationDelay = `${index * 0.1}s`
    dot.setAttribute('data-indicator', ind)

    // Add indicator name
    const indName = document.createElement('span')
    indName.className = 'indicator-name'
    indName.textContent = ind.toUpperCase()
    indName.style.color = getIndicatorColor(ind)

    // Add chevron animation
    const chevron = document.createElement('div')
    chevron.className = 'indicator-chevron'
    chevron.style.borderColor = getIndicatorColor(ind)
    chevron.style.animationDelay = `${index * 0.1}s`

    dotContainer.appendChild(dot)
    dotContainer.appendChild(indName)
    dotContainer.appendChild(chevron)
    indicatorsContainer.appendChild(dotContainer)
  })

  content.appendChild(indicatorsContainer)
  animationContainer.innerHTML = ''
  animationContainer.appendChild(content)

  // Show animation
  animationContainer.classList.add('active')

  // Fade in description and click instruction after a short delay
  setTimeout(() => {
    descriptionElement.style.transition = 'opacity 0.5s ease'
    descriptionElement.style.opacity = '1'
    setTimeout(() => {
      clickInstruction.style.transition = 'opacity 0.5s ease'
      clickInstruction.style.opacity = '0.7'
    }, 500)
  }, 500)

  // Add pulsing glow effect to the container
  animationContainer.style.boxShadow = `0 0 20px ${getStrategyColor(strategy)}`
  animationContainer.style.transition = 'box-shadow 1s ease'

  // Hide animation after delay (auto-dismiss after 5 seconds)
  animationContainer.autoHideTimeout = setTimeout(() => {
    dismissStrategyAnimation(animationContainer)
  }, 5000)
}
window.dismissStrategyAnimation = function (container) {
  // Clear any existing timeout to prevent multiple dismissals
  if (container.autoHideTimeout) {
    clearTimeout(container.autoHideTimeout)
    container.autoHideTimeout = null
  }

  // Fade out
  container.style.opacity = '0'
  container.style.transition = 'opacity 0.5s ease, box-shadow 0.5s ease'
  container.style.boxShadow = '0 0 0 transparent'
  container.style.pointerEvents = 'none'

  // Remove after fade out
  setTimeout(() => {
    container.classList.remove('active')
    container.style.opacity = '1'

    // Remove the container from the DOM completely
    if (container.parentNode) {
      container.parentNode.removeChild(container)
    }

    // Update signal lights to ensure all indicators are properly displayed
    if (typeof updateAllSignalLights === 'function') updateAllSignalLights()

    // Restore mini chart dimensions if needed
    try {
      const savedMiniChartState = container.getAttribute('data-mini-chart-state')
      if (savedMiniChartState) {
        const miniChartState = JSON.parse(savedMiniChartState)
        Object.keys(miniChartState).forEach(ind => {
          const canvas = document.getElementById(`mini-chart-canvas-${ind}`)
          if (canvas) {
            canvas.width = miniChartState[ind].width
            canvas.height = miniChartState[ind].height
          }
        })
        // Enhanced charts handle updates automatically - no need to call updateMiniCharts
      }
    } catch (e) {
      if (typeof logMessages !== 'undefined') {
        logMessages.push(`[${new Date().toLocaleString()}] Error restoring mini chart state: ${e.message}`)
        if (typeof updateLogger === 'function') updateLogger()
      }
    }
  }, 500)
}
window.getStrategyColor = function (strategy) {
  const colors = {
    admiral_toa: '#E91E63',
    momentum_blast: '#FF5722',
    tight_convergence: '#4CAF50',
    top_bottom_feeder: '#FF9800',
    scalping_sniper: '#9C27B0',
    trend_rider: '#2196F3',
    fractal_surge: '#4CAF50',
    x_sentiment_blaster: '#FF9800',
    quantum_entropy: '#9C27B0',
    'cross-asset_nebula': '#2196F3',
    time_warp_scalper: '#00BCD4',
    neural_network_navigator: '#3F51B5',
    deep_learning_diver: '#673AB7',
    ai_pattern_prophet: '#009688',
    machine_learning_momentum: '#8BC34A',
    sentiment_analysis_surfer: '#FFEB3B',
  }
  return colors[strategy] || '#00FFFF'
}
window.getIndicatorColor = function (indicator) {
  const colors = {
    rsi: '#FF5722',
    stochRsi: '#FF9800',
    macd: '#4CAF50',
    bollingerBands: '#2196F3',
    adx: '#9C27B0',
    williamsR: '#00BCD4',
    ultimateOscillator: '#3F51B5',
    mfi: '#E91E63',
    vwap: '#009688',
    volume: '#607D8B',
    fractal: '#FFEB3B',
    ml: '#8BC34A',
    sentiment: '#FF4081',
    entropy: '#7C4DFF',
    correlation: '#00E5FF',
    time_anomaly: '#FFD600',
    atr: '#795548',
    // Additional indicators with system-wide color
    ichimoku: '#808080',
    parabolicSar: '#808080',
    obv: '#808080',
    chaikinMoneyFlow: '#808080',
    cci: '#808080',
    aroon: '#808080',
    trix: '#808080',
  }
  // Return system-wide neutral color for undefined indicators instead of white
  return colors[indicator] || '#808080'
}
window.updateStrategyMenu = function () {
  try {
    const mainStrategySelector = document.getElementById('mainStrategySelector')
    if (mainStrategySelector) {
      mainStrategySelector.value = currentStrategy
    }
    const mainStrategyInfoContent = document.getElementById('mainStrategyInfoContent')
    const mainStrategyIndicatorsContent = document.getElementById('mainStrategyIndicatorsContent')
    if (mainStrategyInfoContent && TRADING_STRATEGIES[currentStrategy]) {
      mainStrategyInfoContent.innerHTML = `<p>${TRADING_STRATEGIES[currentStrategy].description || 'No description available.'}</p>`
    }
    if (mainStrategyIndicatorsContent && TRADING_STRATEGIES[currentStrategy] && TRADING_STRATEGIES[currentStrategy].indicators) {
      mainStrategyIndicatorsContent.innerHTML = ''
      TRADING_STRATEGIES[currentStrategy].indicators.forEach(indicator => {
        const span = document.createElement('span')
        span.className = 'indicator-tag'
        span.textContent = indicator
        mainStrategyIndicatorsContent.appendChild(span)
      })
    }
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Strategy menu updated for ${TRADING_STRATEGIES[currentStrategy].name}`)
      if (typeof updateLogger === 'function') updateLogger()
    }
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] updateStrategyMenu error: ${e.message}`)
      if (typeof updateLogger === 'function') updateLogger()
    }
  }
}
window.renderLogicMenu = function () {
  try {
    const container = document.getElementById('logicControls')
    if (!container) {
      if (typeof logMessages !== 'undefined') {
        logMessages.push(`[${new Date().toLocaleString()}] Logic controls container not found`)
        if (typeof updateLogger === 'function') updateLogger()
      }
      return
    }
    const currentStrategyName = document.getElementById('currentStrategyName')
    if (currentStrategyName) {
      currentStrategyName.textContent = TRADING_STRATEGIES[currentStrategy].name
    }
    updateStrategyInfoPanel(currentStrategy)
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Rendered signal logic menu`)
      if (typeof updateLogger === 'function') updateLogger()
    }
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] renderLogicMenu error: ${e.message}`)
      if (typeof updateLogger === 'function') updateLogger()
    }
  }
}
// 🚫 DISABLED - DUPLICATE FUNCTION - Strategy updates handled by unified system in strategy-selector.js
window.updateStrategyInfoPanel = function (strategy, infoContentId = 'strategyInfoContent', indicatorsContentId = 'strategyIndicatorsContent') {
  console.log('[StrategyLogic] 🚫 BLOCKED duplicate updateStrategyInfoPanel - using unified system');
  return; // Prevent duplicate updates

  // Original code disabled to prevent duplicates:
  /*
  try {
    const strategyInfoContent = document.getElementById(infoContentId)
    const strategyIndicatorsContent = document.getElementById(indicatorsContentId)
    if (strategyInfoContent && TRADING_STRATEGIES[strategy]) {
      strategyInfoContent.innerHTML = `<p>${TRADING_STRATEGIES[strategy].description || 'No description available.'}</p>`
    }
    if (strategyIndicatorsContent && TRADING_STRATEGIES[strategy] && TRADING_STRATEGIES[strategy].indicators) {
      strategyIndicatorsContent.innerHTML = ''
      TRADING_STRATEGIES[strategy].indicators.forEach(indicator => {
        const span = document.createElement('span')
        span.className = 'indicator-tag'
        span.textContent = indicator
        strategyIndicatorsContent.appendChild(span)
      })
    }
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] updateStrategyInfoPanel error: ${e.message}`)
      if (typeof updateLogger === 'function') updateLogger()
    }
  }
  */
}
window.updateIndicatorsForStrategy = function (strategy) {
  if (!TRADING_STRATEGIES[strategy]) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Error: Strategy ${strategy} not found`)
      if (typeof updateLogger === 'function') updateLogger()
    }
    return
  }
  const previousStrategy = currentStrategy
  // Skip showing animation here to prevent duplicate animations
  // Animation is already shown in the index.html version of this function
  currentStrategy = strategy
  if (TRADING_STRATEGIES[strategy].helperText) {
    const helperContainer = document.getElementById('helper-container')
    if (helperContainer) {
      const helperContent = helperContainer.querySelector('.helper-content')
      if (helperContent) {
        helperContent.innerHTML = TRADING_STRATEGIES[strategy].helperText
      }
    }
  }
}
window.updateSignalMatrix = function () {
  try {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Updating signal matrix for ${currentStrategy} strategy`)
    }
    const momentumTable = document.getElementById('momentum-table')
    if (!momentumTable) {
      if (typeof logMessages !== 'undefined') {
        logMessages.push(`[${new Date().toLocaleString()}] updateSignalMatrix: Momentum table not found`)
        if (typeof updateLogger === 'function') updateLogger()
      }
      return
    }
    const strategyIndicators = TRADING_STRATEGIES[currentStrategy]?.indicators || []
    const activeTimeframes = useLowTimeframes ? LOW_TIMEFRAMES : TIMEFRAMES
    const allEnabledIndicators = [...INDICATORS.momentum, ...INDICATORS.trend, ...INDICATORS.volume, ...INDICATORS.ml].filter(ind => {
      const indSettings = enabledIndicators.find(i => i.name === ind)
      if (currentStrategy === 'admiral_toa') {
        return indSettings && indSettings.enabled
      }
      return indSettings && indSettings.enabled && strategyIndicators.includes(ind)
    })
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] Signal matrix updated with ${allEnabledIndicators.length} indicators for ${TRADING_STRATEGIES[currentStrategy].name} strategy`)
      if (typeof updateLogger === 'function') updateLogger()
    }
    if (typeof renderIndicatorTables === 'function') renderIndicatorTables()
    return true
  } catch (e) {
    if (typeof logMessages !== 'undefined') {
      logMessages.push(`[${new Date().toLocaleString()}] updateSignalMatrix error: ${e.message}`)
      if (typeof updateLogger === 'function') updateLogger()
    }
    return false
  }
}
// Add any other extracted logic or initialization as needed.

// Helper functions and update logic (extracted and attached to window for global access)
window.showStrategyAnimation = function (strategy) { /* ...full function from index.html... */ }
window.dismissStrategyAnimation = function (container) { /* ...full function from index.html... */ }
window.getStrategyColor = function (strategy) { /* ...full function from index.html... */ }
window.getIndicatorColor = function (indicator) { /* ...full function from index.html... */ }
window.updateStrategyMenu = function () { /* ...full function from index.html... */ }
window.renderLogicMenu = function () { /* ...full function from index.html... */ }
window.updateStrategyInfoPanel = function (strategy, infoContentId = 'strategyInfoContent', indicatorsContentId = 'strategyIndicatorsContent') { /* ...full function from index.html... */ }
window.updateIndicatorsForStrategy = function (strategy) { /* ...full function from index.html... */ }

// Add any other extracted logic or initialization as needed.

