// Animation logic for StarCrypt overlays and signal matrix
(function () {
  window.pacmanIntroAnimation = function () {
    const allIndicators = [...INDICATORS.momentum, ...INDICATORS.trend]
    const activeTimeframes = useLowTimeframes ? LOW_TIMEFRAMES : TIMEFRAMES
    const circles = []
    allIndicators.forEach(ind => {
      activeTimeframes.forEach(tf => {
        const circle = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`)
        if (circle) circles.push(circle)
      })
    })
    const pacmanFrames = [
      [
        [1, 1, 1, 0, 0, 0, 0, 2], [1, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0],
      ],
      [
        [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 0, 0, 0, 2], [0, 1, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0], [0, 1, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0],
      ],
      [
        [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 0, 0, 3], [0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 1, 1, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0],
      ],
    ]
    const lightShowFrame = [
      [4, 4, 4, 4, 4, 4, 4, 4], [4, 0, 0, 0, 0, 0, 0, 4], [4, 0, 4, 4, 4, 4, 0, 4], [4, 0, 4, 0, 0, 4, 0, 4], [4, 0, 4, 0, 0, 4, 0, 4], [4, 0, 4, 4, 4, 4, 0, 4], [4, 0, 0, 0, 0, 0, 0, 4], [4, 4, 4, 4, 4, 4, 4, 4],
    ]
    let introFrameIndex = 0
    const introAnimate = () => {
      if (introFrameIndex >= pacmanFrames.length + 5) {
        updateSignalLights()
        return
      }
      const frameData = introFrameIndex < pacmanFrames.length ? pacmanFrames[introFrameIndex] : lightShowFrame
      circles.forEach((circle, index) => {
        const row = Math.floor(index / 8)
        const col = index % 8
        if (row >= 8 || col >= 8) {
          circle.className = 'signal-circle'
          return
        }
        circle.className = 'signal-circle'
        const value = frameData[row][col]
        if (value === 1) circle.classList.add('pacman')
        else if (value === 2) circle.classList.add('dot')
        else if (value === 3) circle.classList.add('explosion')
        else if (value === 4) circle.classList.add('lightshow')
      })
      introFrameIndex++
      setTimeout(introAnimate, 300)
    }
    introAnimate()
  }
  // Example usage: window.pacmanIntroAnimation();
})()
