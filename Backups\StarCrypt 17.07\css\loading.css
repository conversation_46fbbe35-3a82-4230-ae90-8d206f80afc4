/* Loading Overlay Styles */
#loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.3s ease;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--border-color);
  border-top: 5px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

.loading-text {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin-top: 1rem;
  text-align: center;
  max-width: 80%;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: 2px;
  margin-top: 1.5rem;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  width: 0%;
  background-color: var(--accent-primary);
  transition: width 0.3s ease;
  border-radius: 2px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Logo animation */
.logo-container {
  position: relative;
  margin-bottom: 2rem;
}

.logo {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.logo::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  filter: blur(15px);
  z-index: -1;
  opacity: 0.3;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.5; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .loading-text {
    font-size: 1rem;
  }
  
  .logo {
    font-size: 2rem;
  }
}
