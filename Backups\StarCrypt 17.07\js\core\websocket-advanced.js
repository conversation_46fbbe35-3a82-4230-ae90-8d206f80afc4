// WebSocket Advanced Handler with compression and robust reconnection
class AdvancedWebSocket {
  constructor(urls, options = {}) {
    this.urls = Array.isArray(urls) ? urls : [urls]
    this.options = {
      reconnectDelay: 1000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      compression: true,
      ...options,
    }

    this.ws = null
    this.reconnectAttempts = 0
    this.heartbeatTimer = null
    this.messageQueue = []
    this.subscriptions = new Set()
    this.messageHandlers = new Map()

    this.connect()
  }

  async connect() {
    if (this.ws) {
      this.ws.onclose = null
      this.ws.close()
    }

    const url = this.getNextUrl()
    try {
      this.ws = new WebSocket(url)
      this.setupEventHandlers()
    } catch (error) {
      this.handleError('Connection error', error)
      this.scheduleReconnect()
    }
  }

  setupEventHandlers() {
    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
      this.setupHeartbeat()
      this.flushMessageQueue()
      this.resubscribeAll()
    }

    this.ws.onmessage = async (event) => {
      try {
        let data = event.data
        if (this.options.compression && typeof data === 'string' && data.startsWith('C:')) {
          data = await this.decompressMessage(data.substring(2))
        }
        const message = JSON.parse(data)
        this.processMessage(message)
      } catch (error) {
        this.handleError('Message processing error', error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket disconnected')
      this.cleanup()
      this.scheduleReconnect()
    }

    this.ws.onerror = (error) => {
      this.handleError('WebSocket error', error)
    }
  }

  async send(data) {
    const message = typeof data === 'string' ? data : JSON.stringify(data)

    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        let payload = message
        if (this.options.compression && message.length > 1000) {
          const compressed = await this.compressMessage(message)
          payload = `C:${compressed}`
        }
        this.ws.send(payload)
      } catch (error) {
        this.handleError('Send error', error)
        this.messageQueue.push(message)
      }
    } else {
      this.messageQueue.push(message)
    }
  }

  async compressMessage(message) {
    // Use Compression Streams API if available
    if (window.CompressionStream) {
      const stream = new Blob([message]).stream()
        .pipeThrough(new CompressionStream('deflate'))
      const compressed = await new Response(stream).arrayBuffer()
      return btoa(String.fromCharCode(...new Uint8Array(compressed)))
    }
    return message // Fallback to uncompressed
  }

  async decompressMessage(compressed) {
    if (window.DecompressionStream) {
      const compressedData = Uint8Array.from(atob(compressed), c => c.charCodeAt(0))
      const stream = new Blob([compressedData]).stream()
        .pipeThrough(new DecompressionStream('deflate'))
      return await new Response(stream).text()
    }
    return compressed // Fallback to uncompressed
  }

  subscribe(topic) {
    this.subscriptions.add(topic)
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.send({ type: 'subscribe', topic })
    }
  }

  unsubscribe(topic) {
    this.subscriptions.delete(topic)
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.send({ type: 'unsubscribe', topic })
    }
  }

  onMessage(type, handler) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set())
    }
    this.messageHandlers.get(type).add(handler)
    return () => this.offMessage(type, handler)
  }

  offMessage(type, handler) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      handlers.delete(handler)
    }
  }

  // ... (other methods remain the same)

  // Add remaining utility methods here
  // ...
}

// Export singleton instance
window.StarCryptWebSocket = new AdvancedWebSocket([
  'wss://api.starcrypt.io/ws',
  'wss://api-backup1.starcrypt.io/ws',
  'wss://api-backup2.starcrypt.io/ws',
], {
  compression: true,
  heartbeatInterval: 25000,
})
