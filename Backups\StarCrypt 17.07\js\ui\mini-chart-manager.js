/**
 * Mini Chart Manager - Centralized management of all mini-charts
 * Ensures consistent rendering, updates, and cleanup of mini-charts
 */

class MiniChartManager {
  constructor() {
    this.charts = {};
    this.initialized = false;
    this.chartConfig = {
      width: 207,
      height: 58,
      maxPoints: 30,
      lineWidth: 1.5,
      gridColor: 'rgba(255, 255, 255, 0.1)',
      bullishGradient: ['#00ff00', '#006400'],
      bearishGradient: ['#ff0000', '#8b0000'],
      neutralGradient: ['#808080', '#404040']
    };
  }

  /**
   * Initialize the mini chart manager - DISABLED
   */
  init() {
    if (this.initialized) return;

    console.log('[MiniChartManager] DISABLED - Enhanced charts are now primary system');
    this.initialized = true;

    // Do not create charts - enhanced charts handle this now
    // Do not set up cleanup - enhanced charts manage themselves
  }

  /**
   * Initialize charts for all required indicators - DISABLED
   */
  initializeAllCharts() {
    // DISABLED - Enhanced charts handle initialization now
    return;
  }

  /**
   * Create a new mini chart for an indicator
   * @param {string} indicator - The indicator name
   * @param {HTMLElement} [container] - Optional container element
   * @returns {Object|null} The chart instance or null if creation failed
   */
  createChart(indicator, container) {
    try {
      // Clean up existing chart if it exists
      this.destroyChart(indicator);

      // Find or create container
      let chartContainer = container || document.querySelector(`#${indicator}-chart-container`);
      if (!chartContainer) {
        const row = this.findOrCreateIndicatorRow(indicator);
        if (!row) {
          console.warn(`[MiniChartManager] Could not find or create row for ${indicator}`);
          return null;
        }
        chartContainer = this.createChartContainer(indicator, row);
      }

      // Create canvas
      const canvas = document.createElement('canvas');
      canvas.id = `${indicator}-chart`;
      canvas.width = this.chartConfig.width;
      canvas.height = this.chartConfig.height;
      canvas.style.display = 'block';
      canvas.style.width = `${this.chartConfig.width}px`;
      canvas.style.height = `${this.chartConfig.height}px`;

      // Create value display
      const valueDisplay = document.createElement('div');
      valueDisplay.className = 'indicator-value';
      valueDisplay.style.position = 'absolute';
      valueDisplay.style.top = '4px';
      valueDisplay.style.right = '8px';
      valueDisplay.style.fontSize = '10px';
      valueDisplay.style.fontFamily = 'monospace';
      valueDisplay.style.fontWeight = 'bold';
      valueDisplay.style.color = '#ffffff';
      valueDisplay.style.textShadow = '0 0 2px #000';
      valueDisplay.textContent = '--';

      // Create wrapper
      const wrapper = document.createElement('div');
      wrapper.style.position = 'relative';
      wrapper.style.width = `${this.chartConfig.width}px`;
      wrapper.style.height = `${this.chartConfig.height}px`;
      wrapper.appendChild(canvas);
      wrapper.appendChild(valueDisplay);

      // Clear and append to container
      chartContainer.innerHTML = '';
      chartContainer.appendChild(wrapper);

      // Initialize chart data
      const ctx = canvas.getContext('2d');
      const chart = {
        canvas,
        ctx,
        container: chartContainer,
        wrapper,
        valueDisplay,
        data: [],
        lastValue: null,
        lastUpdate: 0,
        render: this.renderChart.bind(this, indicator)
      };

      // Store chart instance
      this.charts[indicator] = chart;
      
      // Initial render
      chart.render();
      
      return chart;
    } catch (error) {
      console.error(`[MiniChartManager] Error creating chart for ${indicator}:`, error);
      return null;
    }
  }

  /**
   * Update a chart with new data - DISABLED
   * @param {string} indicator - The indicator name
   * @param {Object} data - The new data point
   */
  updateChart(indicator, data) {
    // DISABLED - Enhanced charts handle updates now
    return;

      // Update chart data
      if (data !== undefined && data !== null) {
        const value = typeof data === 'object' ? (data.value !== undefined ? data.value : data) : data;
        
        // Add new data point
        chart.data.push(parseFloat(value));
        chart.lastValue = value;
        chart.lastUpdate = Date.now();

        // Trim data to max points
        if (chart.data.length > this.chartConfig.maxPoints) {
          chart.data.shift();
        }

        // Update value display
        if (chart.valueDisplay) {
          chart.valueDisplay.textContent = typeof value === 'number' ? value.toFixed(2) : '--';
          
          // Update color based on trend
          if (chart.data.length > 1) {
            const prevValue = chart.data[chart.data.length - 2];
            const isBullish = value > prevValue;
            chart.valueDisplay.style.color = isBullish ? '#00ff00' : '#ff0000';
          }
        }

        // Trigger render
        chart.render();
      }
    } catch (error) {
      console.error(`[MiniChartManager] Error updating chart for ${indicator}:`, error);
    }
  }

  /**
   * Render a chart
   * @private
   */
  renderChart(indicator) {
    const chart = this.charts[indicator];
    if (!chart || !chart.ctx) return;

    const { ctx, canvas, data } = chart;
    const { width, height } = canvas;
    const len = data.length;

    if (len < 2) return;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Calculate min/max for scaling
    let min = Math.min(...data);
    let max = Math.max(...data);
    const range = Math.max(1, max - min);

    // Add padding to range
    min -= range * 0.1;
    max += range * 0.1;

    // Scale functions
    const scaleY = (val) => height - ((val - min) / (max - min)) * height;
    const scaleX = (i) => (i / (this.chartConfig.maxPoints - 1)) * width;

    // Draw grid lines
    ctx.strokeStyle = this.chartConfig.gridColor;
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let y = 0; y <= 1; y += 0.25) {
      const yPos = y * height;
      ctx.beginPath();
      ctx.moveTo(0, yPos);
      ctx.lineTo(width, yPos);
      ctx.stroke();
    }

    // Determine line color based on trend
    let gradient;
    if (len > 1) {
      const isBullish = data[len - 1] > data[len - 2];
      gradient = ctx.createLinearGradient(0, 0, 0, height);
      const colors = isBullish ? this.chartConfig.bullishGradient : 
                    (data[len - 1] < data[len - 2] ? this.chartConfig.bearishGradient : this.chartConfig.neutralGradient);
      
      gradient.addColorStop(0, colors[0]);
      gradient.addColorStop(1, colors[1]);
    } else {
      gradient = this.chartConfig.neutralGradient[0];
    }

    // Draw line
    ctx.beginPath();
    ctx.moveTo(0, scaleY(data[0]));
    
    for (let i = 1; i < len; i++) {
      ctx.lineTo(scaleX(i), scaleY(data[i]));
    }
    
    ctx.strokeStyle = gradient;
    ctx.lineWidth = this.chartConfig.lineWidth;
    ctx.lineJoin = 'round';
    ctx.stroke();

    // Create gradient for fill
    const fillGradient = ctx.createLinearGradient(0, 0, 0, height);
    fillGradient.addColorStop(0, gradient.stops ? gradient.stops[0].color.replace(')', ', 0.2)').replace('rgb', 'rgba') : 'rgba(128, 128, 128, 0.2)');
    fillGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
    
    // Fill area under line
    ctx.lineTo(scaleX(len - 1), height);
    ctx.lineTo(0, height);
    ctx.closePath();
    ctx.fillStyle = fillGradient;
    ctx.fill();
  }

  /**
   * Find or create an indicator row
   * @private
   */
  findOrCreateIndicatorRow(indicator) {
    // Try to find existing row
    let row = document.querySelector(`.indicator-row[data-indicator="${indicator}"]`);
    
    if (!row) {
      // Create new row
      row = document.createElement('div');
      row.className = 'indicator-row';
      row.dataset.indicator = indicator;
      
      // Add to momentum indicators container
      const container = document.getElementById('momentum-indicators');
      if (container) {
        container.appendChild(row);
      } else {
        console.warn('[MiniChartManager] Could not find momentum-indicators container');
        return null;
      }
    }
    
    return row;
  }

  /**
   * Create a chart container
   * @private
   */
  createChartContainer(indicator, row) {
    const container = document.createElement('div');
    container.className = 'indicator-chart';
    container.id = `${indicator}-chart-container`;
    
    // Find where to insert the chart (before signal lights)
    const signalLights = row.querySelector('.signal-lights');
    if (signalLights) {
      row.insertBefore(container, signalLights);
    } else {
      row.appendChild(container);
    }
    
    return container;
  }

  /**
   * Destroy a chart
   * @param {string} indicator - The indicator name
   */
  destroyChart(indicator) {
    const chart = this.charts[indicator];
    if (chart) {
      // Clean up canvas
      if (chart.canvas) {
        chart.canvas.width = 0;
        chart.canvas.height = 0;
      }
      
      // Remove from DOM
      if (chart.wrapper && chart.wrapper.parentNode) {
        chart.wrapper.parentNode.removeChild(chart.wrapper);
      }
      
      // Remove from charts object
      delete this.charts[indicator];
    }
  }

  /**
   * Clean up old/unused charts
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 300000; // 5 minutes
    
    Object.keys(this.charts).forEach(indicator => {
      const chart = this.charts[indicator];
      if (now - chart.lastUpdate > maxAge) {
        this.destroyChart(indicator);
      }
    });
  }

  /**
   * Get all active chart indicators
   * @returns {string[]} Array of indicator names
   */
  getActiveIndicators() {
    return Object.keys(this.charts);
  }
}

// Create and export singleton instance
window.MiniChartManager = new MiniChartManager();

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
  window.MiniChartManager.init();
});

// Export for CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = window.MiniChartManager;
}
