if (typeof window.signalMatrixModuleInitialized === 'undefined') {
    window.signalMatrixModuleInitialized = true;

/**
 * StarCrypt Signal Matrix Module
 *
 * Handles rendering and updating the signal matrix/grid, including the dedicated volume signal light row.
 * This module is responsible for displaying the current state of all indicators across different timeframes.
 * It works in conjunction with the strategy selector to show relevant indicators for the selected strategy.
 */

// Track initialization state
const signalMatrixState = {
  isInitialized: false,
  isReady: false,
  container: null,
  indicators: [],
  timeframes: [],
  strategy: null,
  lastUpdateTime: 0,
  selectedTimeframe: '1h', // Default to 1h as requested
}

// Global reference
window.signalMatrixState = signalMatrixState

// Default configuration
const DEFAULT_STRATEGY = 'admiral_toa'
const UPDATE_THROTTLE_MS = 1000 // Throttle updates to once per second

// Fix for isInitialized reference
let isInitialized = false

// Performance optimization: Cache DOM elements
const signalElementsCache = new Map()
const indicatorUpdateTimes = new Map()

// Error handling
class SignalMatrixError extends Error {
  constructor(message, code = 'SIGNAL_MATRIX_ERROR') {
    super(message)
    this.name = 'SignalMatrixError'
    this.code = code
  }
}

/**
 * Creates and initializes the signal matrix in the specified container.
 * @param {string} containerId - The ID of the container element
 * @param {string[]} [timeframes] - Array of timeframe strings (e.g., ['1m', '5m', '1h'])
 * @param {string[]} [enabledIndicators] - Array of enabled indicator names
 * @returns {boolean} True if successful, false otherwise
 */
function createSignalMatrix(containerId, timeframes, enabledIndicators = []) {
  try {
    console.log('[SignalMatrix] Creating signal matrix...')

    // Validate inputs
    if (!containerId) {
      throw new SignalMatrixError('Container ID is required', 'INVALID_CONTAINER')
    }

    // Ensure MATRIX_INDICATORS is available from global scope
    if (typeof window.MATRIX_INDICATORS === 'undefined') {
      console.warn('[SignalMatrix] window.MATRIX_INDICATORS not found, using window.INDICATORS as fallback.')
      // Fallback to window.INDICATORS if MATRIX_INDICATORS isn't defined
      window.MATRIX_INDICATORS = [...(window.INDICATORS || [])];
    }

    // Use timeframes from global config as the source of truth
    const defaultTimeframes = window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d'];
    
    // Store current timeframes, using provided argument or the default
    signalMatrixState.timeframes = Array.isArray(timeframes) && timeframes.length > 0 ? [...timeframes] : [...defaultTimeframes]

    // Validate timeframes
    if (!signalMatrixState.timeframes.length) {
      console.error('[SignalMatrix] No valid timeframes found, using default')
      signalMatrixState.timeframes = [...defaultTimeframes]
    }

    // Get or create container - handle both table and div containers
    signalMatrixState.container = document.getElementById(containerId)
    if (!signalMatrixState.container) {
      console.warn(`[SignalMatrix] Container not found: ${containerId}, creating one...`)
      signalMatrixState.container = document.createElement('div')
      signalMatrixState.container.id = containerId
      document.body.appendChild(signalMatrixState.container)
    }

    // If container is a table, we need to work with its tbody
    if (signalMatrixState.container.tagName === 'TABLE') {
      let tbody = signalMatrixState.container.querySelector('tbody')
      if (!tbody) {
        tbody = document.createElement('tbody')
        signalMatrixState.container.appendChild(tbody)
      }
      signalMatrixState.tableMode = true
      signalMatrixState.tbody = tbody
    } else {
      signalMatrixState.tableMode = false
    }

    // Log signal matrix creation attempt
    console.log('Attempting to create signal matrix. Initialization state:', signalMatrixState)

    // Only initialize once
    if (signalMatrixState.isInitialized) {
      console.log('[SignalMatrix] Already initialized, skipping creation')
      return true
    }

    signalMatrixState.isInitialized = true

    // Clear existing content and set up container
    signalMatrixState.container.innerHTML = ''
    signalMatrixState.container.className = 'signal-matrix'
    signalMatrixState.container.setAttribute('data-initialized', 'true')

    // Add loading state
    signalMatrixState.container.innerHTML = '<div class="loading">Initializing signal matrix...</div>'

    // Create the matrix structure
    const matrixWrapper = document.createElement('div')
    matrixWrapper.className = 'signal-matrix-wrapper'

    // Create header row with timeframes
    const headerRow = document.createElement('div')
    headerRow.className = 'signal-row header'

    // Add a title for the matrix
    const titleRow = document.createElement('div')
    titleRow.className = 'signal-matrix-title'
    titleRow.textContent = 'Signal Matrix'
    titleRow.title = 'Shows the current state of indicators across different timeframes'

    // Add refresh button
    const refreshButton = document.createElement('button')
    refreshButton.className = 'refresh-button'
    refreshButton.innerHTML = '🔄'
    refreshButton.title = 'Refresh signal matrix'
    refreshButton.onclick = () => updateSignalMatrix()

    titleRow.appendChild(refreshButton)
    matrixWrapper.appendChild(titleRow)

    // Add indicator label cell with tooltip
    const labelCell = document.createElement('div')
    labelCell.className = 'signal-label'
    labelCell.textContent = 'Indicators'
    labelCell.title = 'Indicator names'
    headerRow.appendChild(labelCell)

    // Add help text
    const helpText = document.createElement('div')
    helpText.className = 'help-text'
    helpText.textContent = 'Hover over cells for details. Colors indicate signal strength.'
    helpText.style.gridColumn = `1 / ${signalMatrixState.timeframes.length + 2}` // Span all columns (label + timeframes)

    // Add timeframe cells with tooltips and click handlers
    signalMatrixState.timeframes.forEach((tf, index) => {
      const cell = document.createElement('div')
      cell.className = 'signal-cell timeframe'
      cell.textContent = tf
      cell.dataset.timeframe = tf
      cell.title = `Timeframe: ${tf}`
      cell.style.gridColumn = index + 2 // +1 for the label column
      
      // Add click handler
      cell.addEventListener('click', (e) => handleTimeframeSelect(tf, e));
      
      // Highlight default timeframe
      if (tf === signalMatrixState.selectedTimeframe) {
        cell.classList.add('selected-timeframe');
      }
      
      headerRow.appendChild(cell)
    })

    // Add header row to matrix
    matrixWrapper.appendChild(headerRow)
    matrixWrapper.appendChild(helpText)

    // Add the matrix to the container
    signalMatrixState.container.appendChild(matrixWrapper); // Use signalMatrixState.container

    // Create a container for indicator rows
    const rowsContainer = document.createElement('div')
    rowsContainer.className = 'signal-rows-container'
    matrixWrapper.appendChild(rowsContainer)

    // Store reference to rows container
    signalMatrixState.container.rowsContainer = rowsContainer; // Use signalMatrixState.container

    // Create rows for each indicator
    updateIndicatorRows()

    // Initial update of the matrix
    updateSignalMatrix()
    
    // Initialize with default timeframe
    setTimeout(() => {
      updateSelectedColumnHighlight();
      syncTimeframeWithTradingView(signalMatrixState.selectedTimeframe);
    }, 100);

    // Set up auto-refresh
    setupAutoRefresh()

    console.log('[SignalMatrix] Initialized successfully')

    // Mark as initialized and ready for updates
    signalMatrixState.isInitialized = true;
    signalMatrixState.isReady = true;
    return true
  } catch (error) {
    console.error('[SignalMatrix] Initialization error:', error)
    // Don't show error UI, just log to console
    if (signalMatrixState.container) { // Use signalMatrixState.container
      signalMatrixState.container.innerHTML = ''; // Clear any existing content
      signalMatrixState.container.style.display = 'none'; // Hide the container
    }
    return false
  }
}

/**
 * Creates a row for the signal matrix
 * @param {string} indicator - The indicator name
 * @param {string[]} timeframes - Array of timeframes
 * @param {boolean} [isVolumeRow=false] - Whether this is a volume row
 * @returns {HTMLElement|null} The created row element or null if invalid
 */
function createSignalRow(indicator, timeframes, isVolumeRow = false) {
  try {
    if (!indicator || !timeframes?.length) {
      console.error('Invalid arguments for createSignalRow:', { indicator, timeframes });
      return null;
    }

    const row = document.createElement('div');
    row.className = `signal-row ${isVolumeRow ? 'volume-row' : ''}`;
    row.dataset.indicator = indicator;

    try {
      // Add indicator label
      const labelCell = document.createElement('div');
      labelCell.className = 'signal-label';
      labelCell.textContent = typeof indicator === 'string' ?
        indicator.charAt(0).toUpperCase() + indicator.slice(1) :
        String(indicator);
      row.appendChild(labelCell);

      // Add signal cells for each timeframe
      timeframes.forEach(tf => {
        try {
          const cell = document.createElement('div');
          cell.className = 'signal-cell';
          cell.dataset.timeframe = tf;

          const signal = document.createElement('div');
          signal.className = 'signal-circle';
          signal.dataset.indicator = indicator;
          signal.dataset.timeframe = tf;
          signal.dataset.state = 'neutral'; // Default state
          signal.dataset.id = `signal-${indicator}-${tf}`.toLowerCase();
          signal.id = signal.dataset.id; // Set unique ID

          // Add tooltip
          signal.title = `${indicator.toUpperCase()} (${tf}) - Click to set chart timeframe`;

          // Add ARIA attributes for accessibility
          signal.setAttribute('role', 'button'); // Changed to button as it's clickable
          signal.setAttribute('aria-live', 'polite');
          signal.setAttribute('aria-atomic', 'true');
          signal.setAttribute('tabindex', '0'); // Make it focusable

          // Add click event listener to the signal circle itself
          signal.addEventListener('click', (event) => {
            if (typeof handleTimeframeSelect === 'function') {
              handleTimeframeSelect(tf, event);
            } else {
              console.error('[SignalMatrix] handleTimeframeSelect function not found.');
            }
          });
          
          // Add keypress event listener for accessibility (Enter/Space to click)
          signal.addEventListener('keypress', (event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              if (typeof handleTimeframeSelect === 'function') {
                handleTimeframeSelect(tf, event);
              } else {
                console.error('[SignalMatrix] handleTimeframeSelect function not found.');
              }
            }
          });

          cell.appendChild(signal);
          row.appendChild(cell);

          // Register the signal element immediately if signalInitializer is available
          if (window.signalInitializer) {
            window.signalInitializer.registerSignalElement(signal);
          }
        } catch (cellError) {
          console.error(`Error creating signal cell for ${indicator} (${tf}):`, cellError);
        }
      });

      return row;
    } catch (rowError) {
      console.error(`Error creating signal row for ${indicator}:`, rowError);
      return null;
    }
  } catch (error) {
    console.error('Unexpected error in createSignalRow:', error);
    return null;
  }
}

/**
 * --- SIGNAL MATRIX UPDATE ---
 *
 * This section handles updating the signal matrix based on the current state of indicators
 * and the selected strategy. It ensures that only relevant indicators are shown and that
 * their states are accurately reflected in the UI.
 */

/**
 * Updates the signal matrix for a specific strategy
 * @param {string} strategyId - The ID of the strategy to update for
 * @param {boolean} [force=false] - Whether to force an update even if the strategy hasn't changed
 */
function updateForStrategy(strategyId, force = false) {
  try {
    if (!strategyId) {
      console.warn('[SignalMatrix] No strategy ID provided, using default')
      strategyId = DEFAULT_STRATEGY
    }

    // Store last update time
    signalMatrixState.lastUpdateTime = Date.now()
    const now = Date.now()
    
    // Ensure we're using a consistent property for the current strategy
    if (typeof signalMatrixState.currentStrategy !== 'undefined') {
      // If currentStrategy exists, sync it with strategy property for consistency
      signalMatrixState.strategy = signalMatrixState.currentStrategy;
    } else if (typeof signalMatrixState.strategy !== 'undefined') {
      // If strategy exists but currentStrategy doesn't, create it
      signalMatrixState.currentStrategy = signalMatrixState.strategy;
    }
    
    // Check throttling using strategy property
    if (!force && signalMatrixState.strategy === strategyId && (now - signalMatrixState.lastUpdateTime) < UPDATE_THROTTLE_MS) {
      console.log(`[SignalMatrix] Throttling update for ${strategyId} (last update: ${now - signalMatrixState.lastUpdateTime}ms ago)`)
      return
    }

    console.log(`[SignalMatrix] Updating for strategy: ${strategyId}${force ? ' (forced)' : ''}`)

    // Only update if the strategy has changed or if forced
    if (!force && signalMatrixState.strategy === strategyId) {
      console.log('[SignalMatrix] Strategy unchanged, skipping update');
      return;
    }

    // If strategyId is the same as current, but force is true, we still proceed
    // but we don't need to re-assign signalMatrixState.strategy
    if (signalMatrixState.strategy !== strategyId) {
      signalMatrixState.strategy = strategyId;
    }

    // Update the current strategy
    const previousStrategy = signalMatrixState.strategy;
    signalMatrixState.strategy = strategyId;
    // signalMatrixState.lastUpdateTime is already updated at the beginning of this function

    // Update the indicator rows if the strategy changed
    if (previousStrategy !== strategyId) {
      updateIndicatorRows()
    }

    // Get indicators for the current strategy
    const indicators = getStrategySpecificIndicators(strategyId)

    // Update the matrix with the new indicators
    updateMatrix(indicators)

    console.log(`[SignalMatrix] Updated for strategy: ${strategyId} with ${indicators.length} indicators`)
  } catch (error) {
    console.error('[SignalMatrix] Error updating for strategy:', error)
    // Try to recover by falling back to default strategy
    if (strategyId !== DEFAULT_STRATEGY) {
      console.log('[SignalMatrix] Falling back to default strategy')
      updateForStrategy(DEFAULT_STRATEGY, true)
    } else {
      // If we're already on the default strategy, show error
      showError('Failed to update signal matrix. Please refresh the page.')
    }
  } finally {
    // Ensure last update time is updated regardless of success or failure
    signalMatrixState.lastUpdateTime = Date.now()
  }
}

/**
 * Updates the signal matrix implementation
 * @private
 */
/**
 * Checks if the signal matrix is fully initialized and ready for updates.
 * @returns {boolean} True if the matrix is ready, false otherwise.
 */
function isSignalMatrixReady() {
  return signalMatrixState.isInitialized && signalMatrixState.isReady;
}
window.isSignalMatrixReady = isSignalMatrixReady;

function updateSignalMatrixImpl() {
  if (!signalMatrixState.container) { // Use signalMatrixState.container
    console.warn('[SignalMatrix] Container not initialized')
    return
  }

  try {
    // Get current strategy or use default
    const strategyId = window.currentStrategy || DEFAULT_STRATEGY
    const indicators = getStrategySpecificIndicators(strategyId)

    if (!indicators || !indicators.length) {
      console.warn('[SignalMatrix] No indicators found for strategy:', strategyId)
      return
    }

    // Check if we're already updating
    if (signalMatrixState.container.isUpdating) { // Use signalMatrixState.container
      console.log('[SignalMatrix] Already updating, skipping')
      return
    }

    // Set updating flag
    signalMatrixState.container.isUpdating = true; // Use signalMatrixState.container

    // Update current indicators
    currentIndicators = [...indicators]

    // Use setTimeout to prevent blocking and potential recursion
    setTimeout(() => {
      // Clear existing rows immediately for instant painting
      if (signalMatrixState.container.rowsContainer) { // Use signalMatrixState.container
        signalMatrixState.container.rowsContainer.innerHTML = ''; // Use signalMatrixState.container
      }

      // Create new rows for each indicator
      indicators.forEach(indicator => {
          try {
            const row = createSignalRow(indicator, signalMatrixState.timeframes, indicator === 'volume');
            if (row && signalMatrixState.container.rowsContainer) { // Use signalMatrixState.container
              signalMatrixState.container.rowsContainer.appendChild(row); // Use signalMatrixState.container
            }
          } catch (e) {
            console.error(`[SignalMatrix] Error creating row for ${indicator}:`, e)
          }
        })

        console.log(`[SignalMatrix] Updated matrix with ${indicators.length} indicators`)

        // Clear updating flag
        signalMatrixState.container.isUpdating = false
    }, 10) // Small delay to break potential recursion
  } catch (error) {
    console.error('[SignalMatrix] Error in update implementation:', error)
    // Ensure flag is cleared on error
    if (signalMatrixState.container) {
      signalMatrixState.container.isUpdating = false
    }
  }
}

// Debounced version of updateSignalMatrix
const debouncedUpdateSignalMatrix = debounce(updateSignalMatrixImpl, 10)

// Public function to update the signal matrix
function updateSignalMatrix() {
  debouncedUpdateSignalMatrix()
}

/**
 * Updates the signal matrix with the given indicators
 * @param {string[]} indicators - Array of indicator names to display in the matrix
 */
function updateMatrix(indicators) {
  try {
    if (!signalMatrixState.container) { // Use signalMatrixState.container
      console.warn('[SignalMatrix] Container not initialized')
      return
    }

    // Clear existing rows
    if (signalMatrixState.container.rowsContainer) {
      signalMatrixState.container.rowsContainer.innerHTML = ''
    } else {
      console.warn('[SignalMatrix] Rows container not found')
      return
    }

    // Create rows for each indicator
    indicators.forEach(indicator => {
      try {
        const row = createSignalRow(indicator, signalMatrixState.timeframes, indicator === 'volume')
        if (row && signalMatrixState.container.rowsContainer) {
          signalMatrixState.container.rowsContainer.appendChild(row)
        }
      } catch (e) {
        console.error(`[SignalMatrix] Error creating row for ${indicator}:`, e)
      }
    })

    console.log(`[SignalMatrix] Matrix updated with ${indicators.length} indicators`)
  } catch (error) {
    console.error('[SignalMatrix] Error in updateMatrix:', error)
    showError(`Failed to update matrix: ${error.message}`)
  }
}

/**
 * Simple debounce implementation
 * @param {Function} func - Function to debounce
 * @param {number} wait - Debounce delay in ms
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
  let timeout
  return function () {
    const context = this
    const args = arguments
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(context, args), wait)
  }
}

/**
 * Logs error messages to console
 * @param {string} message - Error message to log
 */
function showError(message) {
  console.error('[SignalMatrix]', message)
  // No UI updates, just log to console
}

/**
 * Gets indicators specific to a strategy
 * @param {string} strategyId - Strategy ID
 * @returns {string[]} Array of indicator names
 */
function getStrategySpecificIndicators(strategyId) {
  const strategy = window.TRADING_STRATEGIES?.[strategyId]
  if (!strategy || !strategy.indicators) {
    return []
  }

  // Return a copy of the indicators array
  return [...(strategy.indicators || [])]
}

/**
 * Initialize the signal matrix when the DOM is ready
 */
function initializeSignalMatrix() {
  if (isInitialized) {
    console.log('[SignalMatrix] Already initialized')
    return
  }

  console.log('[SignalMatrix] Initializing signal matrix...')

  // Default timeframes if not provided
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']

  // Create the signal matrix using the existing momentum-table
  createSignalMatrix('momentum-table', timeframes, [])

  // Update for the current strategy
  const strategyId = window.currentStrategy || 'admiral_toa'
  updateForStrategy(strategyId)

  console.log('[SignalMatrix] Signal matrix initialized')
}

/**
 * Initialize the signal matrix module
 */
function initializeModule() {
  try {
    // Only initialize once
    if (window.signalMatrixInitialized) {
      console.log('[SignalMatrix] Module already initialized')
      return
    }

    console.log('[SignalMatrix] Initializing module...')

    // Create debounced versions of update functions
    const updateSignalMatrixDebounced = debounce(updateSignalMatrix, 500)

    // Make functions globally available
    window.updateSignalMatrixDebounced = updateSignalMatrixDebounced
    window.updateSignalMatrix = updateSignalMatrix
    window.initializeSignalMatrix = initializeSignalMatrix
    window.updateForStrategy = updateForStrategy

    // Mark as initialized
    window.signalMatrixInitialized = true

    console.log('[SignalMatrix] Module initialized')

    // Initialize when the DOM is ready
    const init = () => {
      try {
        console.log('[SignalMatrix] DOM ready, initializing UI...')
        initializeSignalMatrix()

        // Listen for strategy changes
        window.addEventListener('strategyChanged', (event) => {
          if (event.detail?.strategyId) {
            console.log('[SignalMatrix] Detected strategy change to:', event.detail.strategyId)
            updateForStrategy(event.detail.strategyId)
          }
        })

        console.log('[SignalMatrix] UI initialization complete');
        updateSelectedColumnHighlight(); // Apply initial highlight for default timeframe
      } catch (error) {
        console.error('[SignalMatrix] Error during UI initialization:', error)
      }
    }

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init)
    } else {
      // DOM already loaded, initialize with a small delay
      setTimeout(init, 100)
    }
  } catch (error) {
    console.error('[SignalMatrix] Failed to initialize module:', error)
  }
}

/**
 * Set up automatic refresh of the signal matrix
 */
/**
 * Updates the indicator rows in the matrix based on the current strategy
 */
function updateIndicatorRows() {
  if (!signalMatrixState.container || !signalMatrixState.container.rowsContainer) {
    console.warn('[SignalMatrix] Rows container not available')
    return
  }

  try {
    // Clear existing rows
    signalMatrixState.container.rowsContainer.innerHTML = ''

    // Get indicators for the current strategy
    const strategyId = window.currentStrategy || DEFAULT_STRATEGY
    const indicators = getStrategySpecificIndicators(strategyId)

    if (!indicators?.length) {
      console.warn('[SignalMatrix] No indicators found for strategy:', strategyId)
      const noIndicators = document.createElement('div')
      noIndicators.className = 'no-indicators'
      noIndicators.textContent = 'No indicators available for the current strategy'
      signalMatrixContainer.rowsContainer.appendChild(noIndicators)
      return
    }

    // Store current indicators
    signalMatrixState.indicators = Array.isArray(indicators) ? [...indicators] : [...window.MATRIX_INDICATORS]
    signalMatrixState.strategy = strategyId

    // Create rows for each indicator
    indicators.forEach(indicator => {
      try {
        const row = createSignalRow(indicator, signalMatrixState.timeframes, indicator === 'volume')
        if (row) {
          signalMatrixState.container.rowsContainer.appendChild(row)
        }
      } catch (e) {
        console.error(`[SignalMatrix] Error creating row for ${indicator}:`, e)
      }
    })

    console.log(`[SignalMatrix] Created ${indicators.length} indicator rows`)
  } catch (error) {
    console.error('[SignalMatrix] Error updating indicator rows:', error)
    throw error
  }
}

/**
 * Set up automatic refresh of the signal matrix
 */
function setupAutoRefresh() {
  try {
    // Clear any existing interval
    if (window.signalMatrixRefreshInterval) {
      clearInterval(window.signalMatrixRefreshInterval)
    }

    // Instead of auto-refresh, listen for SignalManager updates
    if (window.StarCrypt && window.StarCrypt.SignalManager) {
      const signalManager = window.StarCrypt.SignalManager
      if (typeof signalManager.updateSignal === 'function') {
        // Listen for signal updates
        signalManager.addEventListener('update', () => {
          try {
            updateSignalMatrix()
          } catch (error) {
            console.error('[SignalMatrix] Error updating from SignalManager:', error)
          }
        })
      }
    }
  } catch (error) {
    console.error('[SignalMatrix] Error setting up auto-refresh:', error)
  }
}

/**
 * Handles timeframe selection
 * @param {string} timeframe - The selected timeframe
 * @param {Event} [event] - The click event
 */
function handleTimeframeSelect(timeframe, event) {
  tooltip.textContent = `Signal: ${signalStatus}, Last Updated: ${new Date().toISOString()}`;
  try {
    if (this.currentTimeframe !== timeframe) {
      console.log(`[SignalMatrix] Timeframe selected: ${timeframe}`);
      this.currentTimeframe = timeframe;
      this.updateSelectedColumnHighlight(timeframe);
      this.syncTimeframeWithTradingView(timeframe); // Ensure direct call to sync function
      // Dispatch event for other components if needed, but keep it minimal
      document.dispatchEvent(new CustomEvent('timeframeChanged', { detail: { timeframe } }));
    }
    // Handle click event if provided, with robust selector for signal circles
    if (event) {
      const signalCircle = event.target.closest('.signal-circle');
      if (signalCircle) {
        const indicator = signalCircle.dataset.ind || signalCircle.dataset.indicator;
        console.log(`[SignalMatrix] Signal clicked for indicator: ${indicator}, timeframe: ${timeframe}`);
        // Avoid redundant events; rely on syncTimeframeWithTradingView for TradingView update
      }
    }
  } catch (error) {
    console.error('[SignalMatrix] Error in handleTimeframeSelect:', error);
  }
}

/**
 * Updates the visual highlight for the selected column
 */
function updateSelectedColumnHighlight() {
  const { selectedTimeframe } = signalMatrixState;
  console.log(`[SignalMatrix] updateSelectedColumnHighlight called for timeframe: ${selectedTimeframe}`);
  if (!selectedTimeframe) {
    console.warn('[SignalMatrix] No selectedTimeframe in state for highlight.');
    return;
  }

  const mainContainerSelector = '.indicators-section.cosmic-indicators';

  // Remove glow from all signal circles
  const allCircles = document.querySelectorAll(`${mainContainerSelector} .signal-circle`);
  // console.log(`[SignalMatrix] Found ${allCircles.length} total signal circles to clear glow.`);
  allCircles.forEach(circle => {
    circle.classList.remove('golden-pulse-glow');
  });

  // Remove old highlight class from all cells (if it was used)
  const allSignalCells = document.querySelectorAll(`${mainContainerSelector} .signal-cell:not(.timeframe)`);
  allSignalCells.forEach(cell => {
    cell.classList.remove('highlight-column'); 
  });

  // Add golden pulse glow to signal circles in the selected timeframe column
  const columnCellsToHighlight = document.querySelectorAll(`${mainContainerSelector} .signal-cell[data-timeframe="${selectedTimeframe}"]`);
  console.log(`[SignalMatrix] Found ${columnCellsToHighlight.length} cells in column for timeframe '${selectedTimeframe}' to apply glow.`);

  columnCellsToHighlight.forEach((cell, index) => {
    const circleInCell = cell.querySelector('.signal-circle');
    if (circleInCell) {
      circleInCell.classList.add('golden-pulse-glow');
      // console.log(`[SignalMatrix] Applied .golden-pulse-glow to circle in cell ${index + 1} (Timeframe: ${selectedTimeframe})`);
    } else {
      // console.warn(`[SignalMatrix] No .signal-circle found in cell ${index + 1} for timeframe '${selectedTimeframe}'. Cell content:`, cell.innerHTML);
    }
  });
  console.log(`[SignalMatrix] Finished applying .golden-pulse-glow for timeframe: ${selectedTimeframe}`);
}

/**
 * Syncs the selected timeframe with TradingView chart
 * @param {string} timeframe - The selected timeframe
 */
function syncTimeframeWithTradingView(timeframe) {
  if (!window.tvWidget) {
    console.error('[SignalMatrix] TradingView widget (window.tvWidget) not found. Cannot sync timeframe.');
    return;
  }

  // Ensure the widget is fully loaded and ready for interaction
  window.tvWidget.onChartReady(() => {
    try {
      console.log(`[SignalMatrix] Attempting to set TradingView timeframe to: ${timeframe}`);
      // Use the official API to set the chart's resolution (timeframe)
      window.tvWidget.setResolution(timeframe, () => {
        console.log(`[SignalMatrix] Successfully set TradingView timeframe to: ${timeframe}`);
      });
    } catch (error) {
      console.error(`[SignalMatrix] Error setting TradingView timeframe to ${timeframe}:`, error);
    }
  });
}

// Auto-initialize the module when the script loads
if (typeof window !== 'undefined') {
  initializeModule()
}
}
