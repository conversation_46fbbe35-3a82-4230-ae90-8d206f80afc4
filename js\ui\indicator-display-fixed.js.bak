/* =========================================================
   StarCrypt Indicator Display System - CLEAN IMPLEMENTATION
   This implementation fixes the following issues:
   1. Prevents [object Object] in the UI
   2. Ensures exactly 7 signal lights per indicator row
   3. <PERSON><PERSON><PERSON> handles neutral state (always gray, never blank)
   4. Prevents memory leaks and duplicate event handlers
   5. Optimizes performance with controlled refresh rates
 ========================================================= */

// Global tracking for initialization
window.indicatorsInitialized = false

// Function to get proper signal color with 5-color logic
function getSignalColor(signal, strength = 0.5) {
  // Force neutral to be gray, never blank
  if (!signal || signal === 'neutral') {
    return '#808080' // Medium gray for neutral
  }

  // 5-color logic implementation
  if (signal === 'buy') {
    return strength > 0.6 ? '#00FF00' : '#00AAFF' // Strong vs mild buy
  } else if (signal === 'sell') {
    return strength > 0.6 ? '#FF0000' : '#FFA500' // Strong vs mild sell
  }

  // Fallback
  return '#808080'
}

// Create signal lights for all indicators
function createAllIndicatorLights() {
  console.log('[IndicatorDisplay] Creating signal lights for all indicators')

  // Get all required indicators based on current strategy
  const indicators = getAllRequiredIndicators()
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

  // Process each indicator row
  indicators.forEach(indicator => {
    try {
      // Skip invalid indicators
      if (typeof indicator !== 'string') {
        console.error('[IndicatorDisplay] Invalid indicator:', indicator)
        return
      }

      // Find or create the indicator row
      const row = getOrCreateIndicatorRow(indicator)
      if (!row) return

      // Create signal lights cell
      let signalLightsCell = row.querySelector('.signal-lights-cell');
      if (!signalLightsCell) {
        signalLightsCell = document.createElement('div'); // Use div for cell
        signalLightsCell.className = 'signal-lights-cell';
        row.appendChild(signalLightsCell);
      } else {
        // Clear existing lights to prevent duplicates
        signalLightsCell.innerHTML = ''
      }

      // Create container for all lights
      const lightsContainer = document.createElement('div')
      lightsContainer.className = 'signal-lights-container'
      signalLightsCell.appendChild(lightsContainer)

      // Create 7 lights (one for each timeframe)
      timeframes.forEach(tf => {
        const light = document.createElement('div')
        // CRITICAL: use signal-circle class and grey-light instead of signal-light
        light.className = 'signal-circle grey-light'

        // CRITICAL: These data attributes are required for update-signal-lights.js to find the elements
        light.setAttribute('data-indicator', indicator)
        light.setAttribute('data-ind', indicator) // Critical - this is what update-signal-lights.js looks for
        light.setAttribute('data-timeframe', tf)
        light.setAttribute('data-tf', tf) // Critical - this is what update-signal-lights.js looks for

        light.id = `${indicator}-${tf}-signal`
        light.setAttribute('data-tooltip', `${indicator.toUpperCase()} (${tf}): No data`)
        light.title = `${tf.toUpperCase()} Signal`

        lightsContainer.appendChild(light)
      })
    } catch (error) {
      console.error(`[IndicatorDisplay] Error creating lights for ${indicator}:`, error)
    }
  })
}

// Create or get indicator row
function getOrCreateIndicatorRow(indicator) {
  // Find momentum indicators container
  const momentumIndicators = document.getElementById('momentum-indicators');
  if (!momentumIndicators) {
    console.error('[IndicatorDisplay] Momentum indicators container not found');
    return null;
  }

  // Look for existing row with proper data attribute
  let row = momentumIndicators.querySelector(`.indicator-row[data-indicator="${indicator}"]`);

  // Create new row if needed
  if (!row) {
    row = document.createElement('div'); // Use div for row
    row.setAttribute('data-indicator', indicator);
    row.setAttribute('data-ind', indicator); // Critical for updates
    row.id = `indicator-row-${indicator}`; // Add unique ID
    row.className = 'indicator-row signal-row'; // Important to include both classes

    // Create indicator name cell
    const nameCell = document.createElement('div'); // Use div for cell
    nameCell.className = 'indicator-name signal-name'; // Include both classes for compatibility
    nameCell.setAttribute('data-indicator', indicator); // Add data attribute to cell
    nameCell.textContent = indicator.toUpperCase();

    // Add mini-chart container
    const miniChartCell = document.createElement('div'); // Use div for cell
    miniChartCell.className = 'mini-chart-cell';

    // Create chart container
    const chartContainer = document.createElement('div');
    chartContainer.className = 'mini-chart-container';
    chartContainer.id = `${indicator}-chart-container`;
    miniChartCell.appendChild(chartContainer);

    // Add cells to row
    row.appendChild(nameCell);
    row.appendChild(miniChartCell);

    // Add row to container
    momentumIndicators.appendChild(row);
  }

  return row
}

// Create mini charts for all indicators
function createAllMiniCharts() {
  console.log('[IndicatorDisplay] Creating mini charts for all indicators')

  // Get all required indicators
  const indicators = getAllRequiredIndicators()

  // Create charts with slight delays to avoid rendering issues
  let delay = 0
  indicators.forEach(indicator => {
    setTimeout(() => {
      createMiniChart(indicator)
    }, delay)
    delay += 30 // Small delay between each chart creation
  })
}

// Enhanced chart data creation with multiple lines and professional styling
function createEnhancedChartData(indicator) {
  const timeLabels = Array.from({length: 24}, (_, i) => {
    const date = new Date()
    date.setHours(date.getHours() - (23 - i))
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })
  })

  // Get indicator color
  const indicatorColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF'

  // Create gradient colors
  const primaryColor = indicatorColor
  const secondaryColor = adjustColorOpacity(primaryColor, 0.6)
  const tertiaryColor = adjustColorOpacity(primaryColor, 0.3)

  // Base dataset configuration
  const baseDataset = {
    borderWidth: 2,
    pointRadius: 0,
    pointHoverRadius: 4,
    pointHoverBorderWidth: 2,
    tension: 0.4, // Smooth curves
    fill: true,
  }

  // Create datasets based on indicator type
  if (indicator === 'macd') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'MACD Line',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 2),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.3),
          yAxisID: 'y',
        },
        {
          ...baseDataset,
          label: 'Signal Line',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 1.5),
          borderColor: secondaryColor,
          backgroundColor: createGradient(secondaryColor, 0.2),
          yAxisID: 'y',
        },
        {
          ...baseDataset,
          label: 'Histogram',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 0.5),
          borderColor: tertiaryColor,
          backgroundColor: createGradient(tertiaryColor, 0.4),
          type: 'bar',
          yAxisID: 'y',
        }
      ]
    }
  } else if (indicator === 'bollingerBands') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'Upper Band',
          data: Array(24).fill(0).map(() => 80 + Math.random() * 15),
          borderColor: adjustColorOpacity(primaryColor, 0.8),
          backgroundColor: 'transparent',
          borderDash: [5, 5],
        },
        {
          ...baseDataset,
          label: 'Middle Band (SMA)',
          data: Array(24).fill(0).map(() => 50 + (Math.random() - 0.5) * 20),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.2),
        },
        {
          ...baseDataset,
          label: 'Lower Band',
          data: Array(24).fill(0).map(() => 20 + Math.random() * 15),
          borderColor: adjustColorOpacity(primaryColor, 0.8),
          backgroundColor: 'transparent',
          borderDash: [5, 5],
        }
      ]
    }
  } else if (['rsi', 'stochRsi', 'mfi', 'williamsR'].includes(indicator)) {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: indicator.toUpperCase(),
          data: Array(24).fill(0).map(() => 30 + Math.random() * 40),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.3),
        },
        {
          ...baseDataset,
          label: 'Overbought (70)',
          data: Array(24).fill(70),
          borderColor: 'rgba(255, 0, 0, 0.5)',
          backgroundColor: 'transparent',
          borderDash: [3, 3],
          pointRadius: 0,
          pointHoverRadius: 0,
        },
        {
          ...baseDataset,
          label: 'Oversold (30)',
          data: Array(24).fill(30),
          borderColor: 'rgba(0, 255, 0, 0.5)',
          backgroundColor: 'transparent',
          borderDash: [3, 3],
          pointRadius: 0,
          pointHoverRadius: 0,
        }
      ]
    }
  } else {
    // Default single line with enhanced styling
    return {
      labels: timeLabels,
      datasets: [{
        ...baseDataset,
        label: indicator.toUpperCase(),
        data: Array(24).fill(0).map(() => 30 + Math.random() * 40),
        borderColor: primaryColor,
        backgroundColor: createGradient(primaryColor, 0.3),
      }]
    }
  }
}

// Enhanced chart options with professional styling
function createEnhancedChartOptions(indicator) {
  return {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 750,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 10, 20, 0.95)',
        titleColor: '#00FFFF',
        bodyColor: '#FFFFFF',
        borderColor: '#00FFFF',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: function(context) {
            return `${indicator.toUpperCase()} - ${context[0].label}`
          },
          label: function(context) {
            const value = typeof context.parsed.y === 'number' ? context.parsed.y.toFixed(2) : 'N/A'
            return `${context.dataset.label}: ${value}`
          }
        }
      }
    },
    scales: {
      x: {
        display: false,
        grid: {
          display: false
        }
      },
      y: {
        display: false,
        grid: {
          display: false
        },
        min: getYAxisMin(indicator),
        max: getYAxisMax(indicator),
      }
    },
    elements: {
      point: {
        hoverBackgroundColor: '#00FFFF',
        hoverBorderColor: '#FFFFFF'
      }
    }
  }
}

// Helper function to create gradient
function createGradient(color, opacity) {
  // This will be replaced with actual gradient when canvas context is available
  return adjustColorOpacity(color, opacity)
}

// Helper function to adjust color opacity
function adjustColorOpacity(color, opacity) {
  // Convert hex to rgba
  if (color.startsWith('#')) {
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  return color
}

// Helper function to get Y-axis minimum
function getYAxisMin(indicator) {
  if (['rsi', 'stochRsi', 'mfi'].includes(indicator)) return 0
  if (indicator === 'williamsR') return -100
  if (indicator === 'adx') return 0
  return undefined // Auto-scale
}

// Helper function to get Y-axis maximum
function getYAxisMax(indicator) {
  if (['rsi', 'stochRsi', 'mfi', 'adx'].includes(indicator)) return 100
  if (indicator === 'williamsR') return 0
  return undefined // Auto-scale
}

// Create mini chart for a specific indicator
function createMiniChart(indicator) {
  try {
    // Find the chart container
    const chartContainer = document.getElementById(`${indicator}-chart-container`)
    if (!chartContainer) {
      console.warn(`[IndicatorDisplay] Chart container not found for ${indicator}`)
      return
    }

    // Clear any existing content
    chartContainer.innerHTML = ''

    // Create canvas element
    const canvas = document.createElement('canvas')
    canvas.id = `${indicator}-chart`
    canvas.width = 120
    canvas.height = 40
    chartContainer.appendChild(canvas)

    // Get canvas context
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error(`[IndicatorDisplay] Could not get context for ${indicator} chart`)
      return
    }

    // Enhanced professional chart data with multiple lines and gradients
    const data = createEnhancedChartData(indicator)

    // Create actual gradients for the canvas
    data.datasets.forEach(dataset => {
      if (dataset.backgroundColor && typeof dataset.backgroundColor === 'string' && dataset.backgroundColor.includes('rgba')) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 40)
        const color = dataset.backgroundColor
        gradient.addColorStop(0, color)
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)')
        dataset.backgroundColor = gradient
      }
    })

    // Enhanced professional chart options
    const options = createEnhancedChartOptions(indicator)

    // Create chart with enhanced type detection
    const chartType = data.datasets.some(d => d.type === 'bar') ? 'line' : 'line'
    const chart = new Chart(ctx, {
      type: chartType,
      data,
      options,
    })

    // Store chart in global charts object
    window.indicatorCharts = window.indicatorCharts || {}
    window.indicatorCharts[indicator] = chart
  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating chart for ${indicator}:`, error)
    // Create fallback visualization when chart fails
    createFallbackVisualization(indicator)
  }
}

// Create fallback visualization when chart data is N/A or invalid
function createFallbackVisualization(indicator) {
  try {
    const chartContainer = document.getElementById(`${indicator}-chart-container`)
    if (!chartContainer) return

    // Clear container
    chartContainer.innerHTML = ''

    // Create fallback visual element
    const fallbackDiv = document.createElement('div')
    fallbackDiv.className = 'chart-fallback'
    fallbackDiv.style.cssText = `
      width: 100%;
      height: 40px;
      background: linear-gradient(45deg,
        rgba(0, 255, 255, 0.1) 0%,
        rgba(0, 255, 255, 0.05) 50%,
        rgba(0, 255, 255, 0.1) 100%);
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    `

    // Add animated bars to simulate activity
    for (let i = 0; i < 8; i++) {
      const bar = document.createElement('div')
      bar.style.cssText = `
        width: 2px;
        height: ${10 + Math.random() * 20}px;
        background: rgba(0, 255, 255, 0.6);
        margin: 0 2px;
        animation: pulse ${1 + Math.random()}s ease-in-out infinite alternate;
        animation-delay: ${i * 0.1}s;
      `
      fallbackDiv.appendChild(bar)
    }

    // Add status text
    const statusText = document.createElement('div')
    statusText.textContent = 'Awaiting Data'
    statusText.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: rgba(0, 255, 255, 0.8);
      font-size: 8px;
      font-family: 'Orbitron', monospace;
      text-shadow: 0 0 4px rgba(0, 255, 255, 0.5);
      pointer-events: none;
    `
    fallbackDiv.appendChild(statusText)

    chartContainer.appendChild(fallbackDiv)

    // Add CSS animation if not already present
    if (!document.getElementById('fallback-animations')) {
      const style = document.createElement('style')
      style.id = 'fallback-animations'
      style.textContent = `
        @keyframes pulse {
          0% { opacity: 0.3; transform: scaleY(0.5); }
          100% { opacity: 1; transform: scaleY(1); }
        }
      `
      document.head.appendChild(style)
    }

  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating fallback visualization for ${indicator}:`, error)
  }
}

// Update all indicators
function updateAllIndicators() {
  try {
    // Skip if not initialized yet
    if (!window.indicatorsInitialized) return

    console.log('[IndicatorDisplay] Updating all indicators')

    // Get indicators and timeframes
    const indicators = getAllRequiredIndicators()
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

    // Start with current timeframe for immediate feedback
    const currentTf = window.currentTf || '1h'
    indicators.forEach(indicator => {
      updateIndicator(indicator, currentTf)
    })

    // Then update other timeframes with delays
    let delay = 100
    timeframes.forEach(tf => {
      if (tf === currentTf) return // Skip current timeframe

      setTimeout(() => {
        indicators.forEach(indicator => {
          updateIndicator(indicator, tf)
        })
      }, delay)
      delay += 50
    })
  } catch (error) {
    console.error('[IndicatorDisplay] Error updating indicators:', error)
  }
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe) {
  try {
    // Get signal data
    const data = getIndicatorData(indicator, timeframe)
    if (!data) return

    // Update signal light
    updateSignalLight(indicator, timeframe, data)

    // Update chart if it's the current timeframe
    if (timeframe === window.currentTf) {
      updateChart(indicator, data)
    }
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating ${indicator} for ${timeframe}:`, error)
  }
}

// Get indicator data based on server response or simulation
function getIndicatorData(indicator, timeframe) {
  // Try to use real server data if available
  if (window.indicatorData &&
      window.indicatorData[timeframe] &&
      window.indicatorData[timeframe][indicator]) {
    return window.indicatorData[timeframe][indicator]
  }

  // Otherwise generate simulated data
  return {
    value: 50 + (Math.random() * 50 - 25), // 25-75 range
    signal: Math.random() > 0.7 ? (Math.random() > 0.5 ? 'buy' : 'sell') : 'neutral',
    strength: Math.random(),
    change: Math.random() * 10 - 5,
  }
}

// Update signal light for an indicator - ensure compatibility with update-signal-lights.js
function updateSignalLight(indicator, timeframe, data) {
  // Find the signal light using selector that matches what update-signal-lights.js expects
  const light = document.querySelector(`.signal-circle[data-ind="${indicator}"][data-tf="${timeframe}"]`) ||
                document.getElementById(`${indicator}-${timeframe}-signal`)
  if (!light) return

  // Make sure the light has the correct classes and data attributes
  light.classList.add('signal-circle') // Ensure it has signal-circle class
  light.setAttribute('data-ind', indicator) // Critical for updates
  light.setAttribute('data-tf', timeframe) // Critical for updates

  // Calculate signal color
  const color = getSignalColor(data.signal, data.strength)

  // Set the appropriate color class
  light.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

  // Add the appropriate color class based on signal
  if (data.signal === 'buy' && data.strength > 0.6) {
    light.classList.add('green-light')
  } else if (data.signal === 'buy') {
    light.classList.add('blue-light')
  } else if (data.signal === 'sell' && data.strength > 0.6) {
    light.classList.add('red-light')
  } else if (data.signal === 'sell') {
    light.classList.add('orange-light')
  } else {
    light.classList.add('grey-light')
  }

  // Apply color as inline style as well for backwards compatibility
  light.style.backgroundColor = color || '#808080'

  // Safely format the value for display
  let displayValue = 'N/A'
  try {
    const numValue = parseFloat(data.value)
    displayValue = !isNaN(numValue) ? numValue.toFixed(1) : 'N/A'
  } catch (e) {
    console.warn(`Error formatting value for ${indicator}:`, data.value, e)
  }

  // Update tooltip with safe value
  const tooltipText = `${timeframe.toUpperCase()} ${indicator.toUpperCase()}: ${data.signal || 'N/A'} (${displayValue})`
  light.setAttribute('data-tooltip', tooltipText)
  light.title = tooltipText

  // Add pulse effect for strong signals
  if (data.strength > 0.7) {
    light.classList.add('pulse')
  } else {
    light.classList.remove('pulse')
  }
}

// Enhanced chart update function for professional multi-line charts
function updateChart(indicator, data) {
  try {
    // Find chart
    const chart = window.indicatorCharts && window.indicatorCharts[indicator]
    if (!chart) {
      console.warn(`[IndicatorDisplay] Chart not found for ${indicator}, creating fallback`)
      createFallbackVisualization(indicator)
      return
    }

    // Handle different data types and update accordingly
    if (indicator === 'macd' && data.macd !== undefined) {
      updateMACDChart(chart, data)
    } else if (indicator === 'bollingerBands' && data.upper !== undefined) {
      updateBollingerBandsChart(chart, data)
    } else if (['rsi', 'stochRsi', 'mfi', 'williamsR'].includes(indicator)) {
      updateOscillatorChart(chart, data, indicator)
    } else {
      updateGenericChart(chart, data, indicator)
    }

    // Update chart with smooth animation
    chart.update('none') // Use 'none' for better performance with real-time data
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating chart for ${indicator}:`, error)
    createFallbackVisualization(indicator)
  }
}

// Update MACD chart with multiple lines
function updateMACDChart(chart, data) {
  const macdValue = data.macd || data.value || 0
  const signalValue = data.signal || data.signalLine || 0
  const histogram = data.histogram || (macdValue - signalValue)

  // Update MACD line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(macdValue)
    chart.data.datasets[0].data.shift()
  }

  // Update Signal line (dataset 1)
  if (chart.data.datasets[1]) {
    chart.data.datasets[1].data.push(signalValue)
    chart.data.datasets[1].data.shift()
  }

  // Update Histogram (dataset 2)
  if (chart.data.datasets[2]) {
    chart.data.datasets[2].data.push(histogram)
    chart.data.datasets[2].data.shift()
  }
}

// Update Bollinger Bands chart
function updateBollingerBandsChart(chart, data) {
  const upper = data.upper || 0
  const middle = data.middle || data.sma || data.value || 0
  const lower = data.lower || 0

  // Update Upper Band (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(upper)
    chart.data.datasets[0].data.shift()
  }

  // Update Middle Band (dataset 1)
  if (chart.data.datasets[1]) {
    chart.data.datasets[1].data.push(middle)
    chart.data.datasets[1].data.shift()
  }

  // Update Lower Band (dataset 2)
  if (chart.data.datasets[2]) {
    chart.data.datasets[2].data.push(lower)
    chart.data.datasets[2].data.shift()
  }
}

// Update oscillator charts (RSI, Stoch RSI, etc.)
function updateOscillatorChart(chart, data, indicator) {
  const value = data.value || 50

  // Update main oscillator line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal strength
    const color = getEnhancedSignalColor(data.signal, data.strength, indicator)
    chart.data.datasets[0].borderColor = color.border
    chart.data.datasets[0].backgroundColor = color.background
  }

  // Overbought and oversold lines (datasets 1 and 2) remain static
}

// Update generic single-line charts
function updateGenericChart(chart, data, indicator) {
  const value = data.value || 50

  // Update main line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal
    const color = getEnhancedSignalColor(data.signal, data.strength, indicator)
    chart.data.datasets[0].borderColor = color.border
    chart.data.datasets[0].backgroundColor = color.background
  }
}

// Get enhanced signal colors for charts
function getEnhancedSignalColor(signal, strength, indicator) {
  const baseColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF'

  if (signal === 'buy') {
    const intensity = strength > 0.7 ? 1 : 0.7
    return {
      border: `rgba(0, 255, 0, ${intensity})`,
      background: `rgba(0, 255, 0, ${intensity * 0.3})`
    }
  } else if (signal === 'sell') {
    const intensity = strength > 0.7 ? 1 : 0.7
    return {
      border: `rgba(255, 0, 0, ${intensity})`,
      background: `rgba(255, 0, 0, ${intensity * 0.3})`
    }
  } else {
    return {
      border: baseColor,
      background: adjustColorOpacity(baseColor, 0.3)
    }
  }
}

// Get all required indicators for current strategy
function getAllRequiredIndicators() {
  try {
    // Get current strategy
    const strategy = window.currentStrategy || 'admiral_toa'

    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[strategy]
    if (!strategyDetails) {
      console.warn(`[IndicatorDisplay] Strategy not found: ${strategy}`)
      return window.enabledIndicators || []
    }

    // Return strategy indicators or fallback to default enabled indicators
    return strategyDetails.indicators || window.enabledIndicators || []
  } catch (error) {
    console.error('[IndicatorDisplay] Error getting required indicators:', error)
    return []
  }
}

// Cleanup existing intervals to prevent memory leaks
function cleanupExistingHandlers() {
  // Clear any existing indicator update intervals
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval)
    window.indicatorUpdateInterval = null
  }

  // Clear any existing chart instances
  if (window.indicatorCharts) {
    Object.values(window.indicatorCharts).forEach(chart => {
      try {
        chart.destroy()
      } catch (e) {
        // Ignore errors during cleanup
      }
    })
    window.indicatorCharts = {}
  }
}

// Initialize the indicator display system
function initializeIndicatorDisplay() {
  try {
    console.log('[IndicatorDisplay] Initializing indicator display system')

    // Clean up any existing handlers to prevent duplicates
    cleanupExistingHandlers()

    // Delay to ensure all necessary components are loaded
    setTimeout(() => {
      // Create indicator rows and signal lights
      createAllIndicatorLights()

      // Create mini charts
      createAllMiniCharts()

      // Set up update interval - store reference for cleanup
      window.indicatorUpdateInterval = setInterval(updateAllIndicators, 5000)

      // Mark as initialized
      window.indicatorsInitialized = true

      // Do initial update
      updateAllIndicators()

      console.log('[IndicatorDisplay] Indicator display system initialized')
    }, 1000)
  } catch (error) {
    console.error('[IndicatorDisplay] Error initializing indicator display:', error)
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initializeIndicatorDisplay)

// Create enhanced chart data with multiple lines and gradients
function createEnhancedChartData(indicator) {
  const timeLabels = Array(24).fill(0).map((_, i) => `${i}:00`)
  const primaryColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF'
  const secondaryColor = adjustColorOpacity(primaryColor, 0.7)
  const tertiaryColor = adjustColorOpacity(primaryColor, 0.4)
  const baseDataset = {
    fill: true,
    pointRadius: 0,
    pointHoverRadius: 0,
    pointHoverBackgroundColor: '#00FFFF',
    pointHoverBorderColor: '#FFFFFF',
    pointBackgroundColor: 'transparent',
    pointBorderColor: 'transparent',
  }

  if (indicator === 'macd') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'MACD',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 10),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.3),
        },
        {
          ...baseDataset,
          label: 'Signal',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 5),
          borderColor: secondaryColor,
          backgroundColor: 'transparent',
        },
        {
          ...baseDataset,
          label: 'Histogram',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 0.5),
          borderColor: tertiaryColor,
          backgroundColor: createGradient(tertiaryColor, 0.4),
          type: 'bar',
          yAxisID: 'y',
        }
      ]
    }
  } else if (indicator === 'bollingerBands') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'Upper Band',
          data: Array(24).fill(0).map(() => 80 + Math.random() * 15),
          borderColor: adjustColorOpacity(primaryColor, 0.8),
          backgroundColor: 'transparent',
          borderDash: [5, 5],
        },
        {
          ...baseDataset,
          label: 'Middle Band (SMA)',
          data: Array(24).fill(0).map(() => 50 + (Math.random() - 0.5) * 20),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.2),
        },
        {
          ...baseDataset,
          label: 'Lower Band',
          data: Array(24).fill(0).map(() => 20 + Math.random() * 15),
          borderColor: adjustColorOpacity(primaryColor, 0.8),
          backgroundColor: 'transparent',
          borderDash: [5, 5],
        }
      ]
    }
  } else if (['rsi', 'stochRsi', 'mfi', 'williamsR'].includes(indicator)) {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: indicator.toUpperCase(),
          data: Array(24).fill(0).map(() => 30 + Math.random() * 40),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.3),
        },
        {
          ...baseDataset,
          label: 'Overbought (70)',
          data: Array(24).fill(70),
          borderColor: 'rgba(255, 0, 0, 0.5)',
          backgroundColor: 'transparent',
          borderDash: [3, 3],
          pointRadius: 0,
          pointHoverRadius: 0,
        },
        {
          ...baseDataset,
          label: 'Oversold (30)',
          data: Array(24).fill(30),
          borderColor: 'rgba(0, 255, 0, 0.5)',
          backgroundColor: 'transparent',
          borderDash: [3, 3],
          pointRadius: 0,
          pointHoverRadius: 0,
        }
      ]
    }
  } else {
    // Default single line with enhanced styling
    return {
      labels: timeLabels,
      datasets: [{
        ...baseDataset,
        label: indicator.toUpperCase(),
        data: Array(24).fill(0).map(() => 30 + Math.random() * 40),
        borderColor: primaryColor,
        backgroundColor: createGradient(primaryColor, 0.3),
      }]
    }
  }
}

// Enhanced chart options with professional styling
function createEnhancedChartOptions(indicator) {
  return {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 750,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 10, 20, 0.95)',
        titleColor: '#00FFFF',
        bodyColor: '#FFFFFF',
        borderColor: '#00FFFF',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: function(context) {
            return `${indicator.toUpperCase()} - ${context[0].label}`
          },
          label: function(context) {
            const value = typeof context.parsed.y === 'number' ? context.parsed.y.toFixed(2) : 'N/A'
            return `${context.dataset.label}: ${value}`
          }
        }
      }
    },
    scales: {
      x: {
        display: false,
        grid: {
          display: false
        }
      },
      y: {
        display: false,
        grid: {
          display: false
        },
        min: getYAxisMin(indicator),
        max: getYAxisMax(indicator),
      }
    },
    elements: {
      point: {
        hoverBackgroundColor: '#00FFFF',
        hoverBorderColor: '#FFFFFF'
      }
    }
  }
}

// Helper function to create gradient
function createGradient(color, opacity) {
  // This will be replaced with actual gradient when canvas context is available
  return adjustColorOpacity(color, opacity)
}

// Helper function to adjust color opacity
function adjustColorOpacity(color, opacity) {
  // Convert hex to rgba
  if (color.startsWith('#')) {
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  return color
}

// Helper function to get Y-axis minimum
function getYAxisMin(indicator) {
  if (['rsi', 'stochRsi', 'mfi'].includes(indicator)) return 0
  if (indicator === 'williamsR') return -100
  if (indicator === 'adx') return 0
  return undefined // Auto-scale
}

// Helper function to get Y-axis maximum
function getYAxisMax(indicator) {
  if (['rsi', 'stochRsi', 'mfi', 'adx'].includes(indicator)) return 100
  if (indicator === 'williamsR') return 0
  return undefined // Auto-scale
}

// Create mini chart for a specific indicator
function createMiniChart(indicator) {
  try {
    // Find the chart container
    const chartContainer = document.getElementById(`${indicator}-chart-container`)
    if (!chartContainer) {
      console.warn(`[IndicatorDisplay] Chart container not found for ${indicator}`)
      return
    }

    // Clear any existing content
    chartContainer.innerHTML = ''

    // Create canvas element
    const canvas = document.createElement('canvas')
    canvas.id = `${indicator}-chart`
    canvas.width = 120
    canvas.height = 40
    chartContainer.appendChild(canvas)

    // Get canvas context
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error(`[IndicatorDisplay] Could not get context for ${indicator} chart`)
      return
    }

    // Enhanced professional chart data with multiple lines and gradients
    const data = createEnhancedChartData(indicator)

    // Create actual gradients for the canvas
    data.datasets.forEach(dataset => {
      if (dataset.backgroundColor && typeof dataset.backgroundColor === 'string' && dataset.backgroundColor.includes('rgba')) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 40)
        const color = dataset.backgroundColor
        gradient.addColorStop(0, color)
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)')
        dataset.backgroundColor = gradient
      }
    })

    // Enhanced professional chart options
    const options = createEnhancedChartOptions(indicator)

    // Create chart with enhanced type detection
    const chartType = data.datasets.some(d => d.type === 'bar') ? 'line' : 'line'
    const chart = new Chart(ctx, {
      type: chartType,
      data,
      options,
    })

    // Store chart in global charts object
    window.indicatorCharts = window.indicatorCharts || {}
    window.indicatorCharts[indicator] = chart
  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating chart for ${indicator}:`, error)
    // Create fallback visualization when chart fails
    createFallbackVisualization(indicator)
  }
}

// Create fallback visualization when chart data is N/A or invalid
function createFallbackVisualization(indicator) {
  try {
    const chartContainer = document.getElementById(`${indicator}-chart-container`)
    if (!chartContainer) return

    // Clear container
    chartContainer.innerHTML = ''

    // Create fallback visual element
    const fallbackDiv = document.createElement('div')
    fallbackDiv.className = 'chart-fallback'
    fallbackDiv.style.cssText = `
      width: 100%;
      height: 40px;
      background: linear-gradient(45deg,
        rgba(0, 255, 255, 0.1) 0%,
        rgba(0, 255, 255, 0.05) 50%,
        rgba(0, 255, 255, 0.1) 100%);
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    `

    // Add animated bars to simulate activity
    for (let i = 0; i < 8; i++) {
      const bar = document.createElement('div')
      bar.style.cssText = `
        width: 2px;
        height: ${10 + Math.random() * 20}px;
        background: rgba(0, 255, 255, 0.6);
        margin: 0 2px;
        animation: pulse ${1 + Math.random()}s ease-in-out infinite alternate;
        animation-delay: ${i * 0.1}s;
      `
      fallbackDiv.appendChild(bar)
    }

    // Add status text
    const statusText = document.createElement('div')
    statusText.textContent = 'Awaiting Data'
    statusText.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: rgba(0, 255, 255, 0.8);
      font-size: 8px;
      font-family: 'Orbitron', monospace;
      text-shadow: 0 0 4px rgba(0, 255, 255, 0.5);
      pointer-events: none;
    `
    fallbackDiv.appendChild(statusText)

    chartContainer.appendChild(fallbackDiv)

    // Add CSS animation if not already present
    if (!document.getElementById('fallback-animations')) {
      const style = document.createElement('style')
      style.id = 'fallback-animations'
      style.textContent = `
        @keyframes pulse {
          0% { opacity: 0.3; transform: scaleY(0.5); }
          100% { opacity: 1; transform: scaleY(1); }
        }
      `
      document.head.appendChild(style)
    }

  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating fallback visualization for ${indicator}:`, error)
  }
}

// Update all indicators
function updateAllIndicators() {
  try {
    // Skip if not initialized yet
    if (!window.indicatorsInitialized) return

    console.log('[IndicatorDisplay] Updating all indicators')

    // Get indicators and timeframes
    const indicators = getAllRequiredIndicators()
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

    // Start with current timeframe for immediate feedback
    const currentTf = window.currentTf || '1h'
    indicators.forEach(indicator => {
      updateIndicator(indicator, currentTf)
    })

    // Then update other timeframes with delays
    let delay = 100
    timeframes.forEach(tf => {
      if (tf === currentTf) return // Skip current timeframe

      setTimeout(() => {
        indicators.forEach(indicator => {
          updateIndicator(indicator, tf)
        })
      }, delay)
      delay += 50
    })
  } catch (error) {
    console.error('[IndicatorDisplay] Error updating indicators:', error)
  }
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe) {
  try {
    // Get signal data
    const data = getIndicatorData(indicator, timeframe)
    if (!data) return

    // Update signal light
    updateSignalLight(indicator, timeframe, data)

    // Update chart if it's the current timeframe
    if (timeframe === window.currentTf) {
      updateChart(indicator, data)
    }
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating ${indicator} for ${timeframe}:`, error)
  }
}

// Get indicator data based on server response or simulation
function getIndicatorData(indicator, timeframe) {
  // Try to use real server data if available
  if (window.indicatorData &&
      window.indicatorData[timeframe] &&
      window.indicatorData[timeframe][indicator]) {
    return window.indicatorData[timeframe][indicator]
  }

  // Otherwise generate simulated data
  return {
    value: 50 + (Math.random() * 50 - 25), // 25-75 range
    signal: Math.random() > 0.7 ? (Math.random() > 0.5 ? 'buy' : 'sell') : 'neutral',
    strength: Math.random(),
    change: Math.random() * 10 - 5,
  }
}

// Update signal light for an indicator - ensure compatibility with update-signal-lights.js
function updateSignalLight(indicator, timeframe, data) {
  // Find the signal light using selector that matches what update-signal-lights.js expects
  const light = document.querySelector(`.signal-circle[data-ind="${indicator}"][data-tf="${timeframe}"]`) ||
                document.getElementById(`${indicator}-${timeframe}-signal`)
  if (!light) return

  // Make sure the light has the correct classes and data attributes
  light.classList.add('signal-circle') // Ensure it has signal-circle class
  light.setAttribute('data-ind', indicator) // Critical for updates
  light.setAttribute('data-tf', timeframe) // Critical for updates

  // Calculate signal color
  const color = getSignalColor(data.signal, data.strength)

  // Set the appropriate color class
  light.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

  // Add the appropriate color class based on signal
  if (data.signal === 'buy' && data.strength > 0.6) {
    light.classList.add('green-light')
  } else if (data.signal === 'buy') {
    light.classList.add('blue-light')
  } else if (data.signal === 'sell' && data.strength > 0.6) {
    light.classList.add('red-light')
  } else if (data.signal === 'sell') {
    light.classList.add('orange-light')
  } else {
    light.classList.add('grey-light')
  }

  // Apply color as inline style as well for backwards compatibility
  light.style.backgroundColor = color || '#808080'

  // Safely format the value for display
  let displayValue = 'N/A'
  try {
    const numValue = parseFloat(data.value)
    displayValue = !isNaN(numValue) ? numValue.toFixed(1) : 'N/A'
  } catch (e) {
    console.warn(`Error formatting value for ${indicator}:`, data.value, e)
  }

  // Update tooltip with safe value
  const tooltipText = `${timeframe.toUpperCase()} ${indicator.toUpperCase()}: ${data.signal || 'N/A'} (${displayValue})`
  light.setAttribute('data-tooltip', tooltipText)
  light.title = tooltipText

  // Add pulse effect for strong signals
  if (data.strength > 0.7) {
    light.classList.add('pulse')
  } else {
    light.classList.remove('pulse')
  }
}

// Enhanced chart update function for professional multi-line charts
function updateChart(indicator, data) {
  try {
    // Find chart
    const chart = window.indicatorCharts && window.indicatorCharts[indicator]
    if (!chart) {
      console.warn(`[IndicatorDisplay] Chart not found for ${indicator}, creating fallback`)
      createFallbackVisualization(indicator)
      return
    }

    // Handle different data types and update accordingly
    if (indicator === 'macd' && data.macd !== undefined) {
      updateMACDChart(chart, data)
    } else if (indicator === 'bollingerBands' && data.upper !== undefined) {
      updateBollingerBandsChart(chart, data)
    } else if (['rsi', 'stochRsi', 'mfi', 'williamsR'].includes(indicator)) {
      updateOscillatorChart(chart, data, indicator)
    } else {
      updateGenericChart(chart, data, indicator)
    }

    // Update chart with smooth animation
    chart.update('none') // Use 'none' for better performance with real-time data
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating chart for ${indicator}:`, error)
    createFallbackVisualization(indicator)
  }
}

// Update MACD chart with multiple lines
function updateMACDChart(chart, data) {
  const macdValue = data.macd || data.value || 0
  const signalValue = data.signal || data.signalLine || 0
  const histogram = data.histogram || (macdValue - signalValue)

  // Update MACD line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(macdValue)
    chart.data.datasets[0].data.shift()
  }

  // Update Signal line (dataset 1)
  if (chart.data.datasets[1]) {
    chart.data.datasets[1].data.push(signalValue)
    chart.data.datasets[1].data.shift()
  }

  // Update Histogram (dataset 2)
  if (chart.data.datasets[2]) {
    chart.data.datasets[2].data.push(histogram)
    chart.data.datasets[2].data.shift()
  }
}

// Update Bollinger Bands chart
function updateBollingerBandsChart(chart, data) {
  const upper = data.upper || 0
  const middle = data.middle || data.sma || data.value || 0
  const lower = data.lower || 0

  // Update Upper Band (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(upper)
    chart.data.datasets[0].data.shift()
  }

  // Update Middle Band (dataset 1)
  if (chart.data.datasets[1]) {
    chart.data.datasets[1].data.push(middle)
    chart.data.datasets[1].data.shift()
  }

  // Update Lower Band (dataset 2)
  if (chart.data.datasets[2]) {
    chart.data.datasets[2].data.push(lower)
    chart.data.datasets[2].data.shift()
  }
}

// Update oscillator charts (RSI, Stoch RSI, etc.)
function updateOscillatorChart(chart, data, indicator) {
  const value = data.value || 50

  // Update main oscillator line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal strength
    const color = getEnhancedSignalColor(data.signal, data.strength, indicator)
    chart.data.datasets[0].borderColor = color.border
    chart.data.datasets[0].backgroundColor = color.background
  }

  // Overbought and oversold lines (datasets 1 and 2) remain static
}

// Update generic single-line charts
function updateGenericChart(chart, data, indicator) {
  const value = data.value || 50

  // Update main line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal
    const color = getEnhancedSignalColor(data.signal, data.strength, indicator)
    chart.data.datasets[0].borderColor = color.border
    chart.data.datasets[0].backgroundColor = color.background
  }
}

// Get enhanced signal colors for charts
function getEnhancedSignalColor(signal, strength, indicator) {
  const baseColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF'

  if (signal === 'buy') {
    const intensity = strength > 0.7 ? 1 : 0.7
    return {
      border: `rgba(0, 255, 0, ${intensity})`,
      background: `rgba(0, 255, 0, ${intensity * 0.3})`
    }
  } else if (signal === 'sell') {
    const intensity = strength > 0.7 ? 1 : 0.7
    return {
      border: `rgba(255, 0, 0, ${intensity})`,
      background: `rgba(255, 0, 0, ${intensity * 0.3})`
    }
  } else {
    return {
      border: baseColor,
      background: adjustColorOpacity(baseColor, 0.3)
    }
  }
}

// Get all required indicators for current strategy
function getAllRequiredIndicators() {
  try {
    // Get current strategy
    const strategy = window.currentStrategy || 'admiral_toa'

    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[strategy]
    if (!strategyDetails) {
      console.warn(`[IndicatorDisplay] Strategy not found: ${strategy}`)
      return window.enabledIndicators || []
    }

    // Return strategy indicators or fallback to default enabled indicators
    return strategyDetails.indicators || window.enabledIndicators || []
  } catch (error) {
    console.error('[IndicatorDisplay] Error getting required indicators:', error)
    return []
  }
}

// Cleanup existing intervals to prevent memory leaks
function cleanupExistingHandlers() {
  // Clear any existing indicator update intervals
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval)
    window.indicatorUpdateInterval = null
  }

  // Clear any existing chart instances
  if (window.indicatorCharts) {
    Object.values(window.indicatorCharts).forEach(chart => {
      try {
        chart.destroy()
      } catch (e) {
        // Ignore errors during cleanup
      }
    })
    window.indicatorCharts = {}
  }
}

// Initialize the indicator display system
function initializeIndicatorDisplay() {
  try {
    console.log('[IndicatorDisplay] Initializing indicator display system')

    // Clean up any existing handlers to prevent duplicates
    cleanupExistingHandlers()

    // Delay to ensure all necessary components are loaded
    setTimeout(() => {
      // Create indicator rows and signal lights
      createAllIndicatorLights()

      // Create mini charts
      createAllMiniCharts()

      // Set up update interval - store reference for cleanup
      window.indicatorUpdateInterval = setInterval(updateAllIndicators, 5000)

      // Mark as initialized
      window.indicatorsInitialized = true

      // Do initial update
      updateAllIndicators()

      console.log('[IndicatorDisplay] Indicator display system initialized')
    }, 1000)
  } catch (error) {
    console.error('[IndicatorDisplay] Error initializing indicator display:', error)
  }
}

    // Fallback: Initialize directly if StrategyManager is not available
    console.log('[IndicatorDisplay] Initializing without StrategyManager');
    initializeIndicatorDisplay();
  }
});

// Handle strategy changes
document.addEventListener('strategyChanged', (event) => {
  if (!event.detail?.fromStrategyManager) return; // Only handle events from StrategyManager
  
  console.log('[IndicatorDisplay] Strategy changed, updating indicators...');
  
  // Clean up existing indicators
  if (window.indicatorsInitialized) {
    cleanupExistingHandlers();
  }
  
  // Reinitialize with new strategy
  initializeIndicatorDisplay();
});

// Export functions
window.initializeIndicatorDisplay = initializeIndicatorDisplay;
window.updateAllIndicators = updateAllIndicators;
window.createAllIndicatorLights = createAllIndicatorLights;
window.createAllMiniCharts = createAllMiniCharts;
window.cleanupIndicatorDisplay = cleanupExistingHandlers; // Make cleanup available globally
