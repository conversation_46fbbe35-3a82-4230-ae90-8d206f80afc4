const tf = require('@tensorflow/tfjs-node')
const fs = require('fs').promises
const path = require('path')
const marketData = require('./market-data')
const AIEngine = require('./ai-engine')

class Backtester {
  constructor() {
    this.results = []
    this.equityCurve = []
    this.trades = []
    this.initialBalance = 10000 // Starting balance in USD
    this.commission = 0.001 // 0.1% commission per trade
    this.slippage = 0.0005 // 0.05% slippage
  }

  // Run backtest on historical data
  async runBacktest(strategy, pair, timeframe, startDate, endDate) {
    try {
      console.log(`Starting backtest for ${pair} ${timeframe} from ${startDate} to ${endDate}`)

      // Load historical data
      const ohlcv = await this.loadHistoricalData(pair, timeframe, startDate, endDate)
      if (!ohlcv || ohlcv.length < 100) {
        throw new Error('Insufficient historical data')
      }

      // Process data
      const processedData = marketData.processOHLCV(ohlcv)

      // Initialize backtest state
      let balance = this.initialBalance
      let position = null
      let entryPrice = 0
      let entryTime = null
      let maxDrawdown = 0
      let peak = this.initialBalance
      let totalTrades = 0
      let winningTrades = 0

      // Prepare data for strategy
      const { X, y } = marketData.createLSTMDataset(processedData.closes)

      // Train model if needed
      if (!strategy.model) {
        console.log('Training model...')
        const { model, history } = await AIEngine.trainModel({
          X: X.arraySync(),
          y: y.arraySync(),
        })
        strategy.model = model
      }

      // Run backtest
      for (let i = 100; i < processedData.closes.length - 1; i++) {
        const currentData = this.getSlice(processedData, i)
        const prediction = await AIEngine.predict(strategy.model, currentData)

        // Generate signal based on prediction
        const signal = this.generateSignal(prediction, strategy)

        // Execute trades based on signal
        const tradeResult = this.executeTrade(
          signal,
          processedData.closes[i],
          processedData.timestamps[i],
          position,
          balance,
          entryPrice,
          entryTime,
        )

        // Update state
        if (tradeResult.positionChanged) {
          position = tradeResult.newPosition
          balance = tradeResult.newBalance
          entryPrice = tradeResult.newEntryPrice
          entryTime = tradeResult.newEntryTime

          if (tradeResult.trade) {
            this.trades.push(tradeResult.trade)
            totalTrades++
            if (tradeResult.trade.profit > 0) winningTrades++
          }

          // Update max drawdown
          if (balance > peak) peak = balance
          const drawdown = (peak - balance) / peak
          if (drawdown > maxDrawdown) maxDrawdown = drawdown
        }

        // Record equity curve
        this.equityCurve.push({
          timestamp: processedData.timestamps[i],
          balance,
          position,
          price: processedData.closes[i],
        })
      }

      // Close any open position at the end
      if (position !== null) {
        const closePrice = processedData.closes[processedData.closes.length - 1]
        const exitValue = position === 'LONG' ? closePrice : 2 * entryPrice - closePrice // For SHORT
        const pnl = position === 'LONG' ?
          (closePrice - entryPrice) / entryPrice :
          (entryPrice - closePrice) / entryPrice

        const trade = {
          entryTime,
          exitTime: processedData.timestamps[processedData.timestamps.length - 1],
          entryPrice,
          exitPrice: closePrice,
          position,
          pnl,
          commission: this.commission * 2, // Entry and exit
          slippage: this.slippage * 2, // Entry and exit
          netPnl: pnl - (this.commission * 2) - (this.slippage * 2),
        }

        balance *= (1 + trade.netPnl)
        this.trades.push(trade)
      }

      // Calculate performance metrics
      const performance = this.calculatePerformanceMetrics(
        this.equityCurve,
        this.trades,
        maxDrawdown,
        totalTrades,
        winningTrades,
      )

      // Save results
      const result = {
        strategy: strategy.name,
        pair,
        timeframe,
        startDate,
        endDate,
        initialBalance: this.initialBalance,
        finalBalance: balance,
        performance,
        trades: this.trades,
        equityCurve: this.equityCurve,
        timestamp: new Date().toISOString(),
      }

      this.results.push(result)
      return result
    } catch (error) {
      console.error('Backtest failed:', error)
      throw error
    }
  }

  // Generate trading signal from AI prediction
  generateSignal(prediction, strategy) {
    const { prediction: pred, confidence } = prediction
    const { entryThreshold = 0.6, exitThreshold = 0.4 } = strategy.params || {}

    if (pred >= entryThreshold && confidence >= 0.5) {
      return { action: 'BUY', confidence: pred }
    } else if (pred <= exitThreshold && confidence >= 0.5) {
      return { action: 'SELL', confidence: 1 - pred }
    }

    return { action: 'HOLD', confidence: 0 }
  }

  // Execute trade based on signal
  executeTrade(signal, price, timestamp, currentPosition, currentBalance, entryPrice, entryTime) {
    const result = {
      positionChanged: false,
      newPosition: currentPosition,
      newBalance: currentBalance,
      newEntryPrice: entryPrice,
      newEntryTime: entryTime,
      trade: null,
    }

    // Apply slippage
    const adjustedPrice = signal.action === 'BUY' ?
      price * (1 + this.slippage) :
      price * (1 - this.slippage)

    // Execute trade based on signal and current position
    if (signal.action === 'BUY' && currentPosition !== 'LONG') {
      // Close SHORT if open
      if (currentPosition === 'SHORT') {
        const pnl = (entryPrice - adjustedPrice) / entryPrice // Profit if price went down
        const commission = this.commission * 2 // Close and open
        const netPnl = pnl - commission - (this.slippage * 2)

        result.trade = {
          entryTime,
          exitTime: timestamp,
          entryPrice,
          exitPrice: adjustedPrice,
          position: 'SHORT',
          pnl,
          commission,
          slippage: this.slippage * 2,
          netPnl,
        }

        result.newBalance = currentBalance * (1 + netPnl)
      }

      // Open LONG
      result.newPosition = 'LONG'
      result.newEntryPrice = adjustedPrice
      result.newEntryTime = timestamp
      result.positionChanged = true
    } else if (signal.action === 'SELL' && currentPosition !== 'SHORT') {
      // Close LONG if open
      if (currentPosition === 'LONG') {
        const pnl = (adjustedPrice - entryPrice) / entryPrice // Profit if price went up
        const commission = this.commission * 2 // Close and open
        const netPnl = pnl - commission - (this.slippage * 2)

        result.trade = {
          entryTime,
          exitTime: timestamp,
          entryPrice,
          exitPrice: adjustedPrice,
          position: 'LONG',
          pnl,
          commission,
          slippage: this.slippage * 2,
          netPnl,
        }

        result.newBalance = currentBalance * (1 + netPnl)
      }

      // Open SHORT
      result.newPosition = 'SHORT'
      result.newEntryPrice = adjustedPrice
      result.newEntryTime = timestamp
      result.positionChanged = true
    }

    return result
  }

  // Calculate performance metrics
  calculatePerformanceMetrics(equityCurve, trades, maxDrawdown, totalTrades, winningTrades) {
    if (trades.length === 0) {
      return {
        totalReturn: 0,
        annualizedReturn: 0,
        sharpeRatio: 0,
        sortinoRatio: 0,
        maxDrawdown,
        winRate: 0,
        profitFactor: 0,
        averageWin: 0,
        averageLoss: 0,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
      }
    }

    // Calculate returns
    const initialBalance = this.initialBalance
    const finalBalance = equityCurve[equityCurve.length - 1].balance
    const totalReturn = (finalBalance - initialBalance) / initialBalance

    // Calculate time period in years
    const startTime = new Date(equityCurve[0].timestamp)
    const endTime = new Date(equityCurve[equityCurve.length - 1].timestamp)
    const years = (endTime - startTime) / (1000 * 60 * 60 * 24 * 365.25)
    const annualizedReturn = Math.pow(1 + totalReturn, 1 / years) - 1

    // Calculate daily returns for risk metrics
    const dailyReturns = []
    for (let i = 1; i < equityCurve.length; i++) {
      const ret = (equityCurve[i].balance - equityCurve[i - 1].balance) / equityCurve[i - 1].balance
      dailyReturns.push(ret)
    }

    // Calculate Sharpe ratio (assuming 0% risk-free rate)
    const avgReturn = dailyReturns.reduce((a, b) => a + b, 0) / dailyReturns.length
    const stdDev = Math.sqrt(
      dailyReturns.map(x => Math.pow(x - avgReturn, 2)).reduce((a, b) => a + b, 0) / dailyReturns.length,
    )
    const sharpeRatio = stdDev !== 0 ? (avgReturn / stdDev) * Math.sqrt(252) : 0 // Annualized

    // Calculate Sortino ratio (only downside deviation)
    const downsideReturns = dailyReturns.filter(r => r < 0)
    const downsideDev = downsideReturns.length > 0 ?
      Math.sqrt(
        downsideReturns
          .map(r => Math.pow(r, 2))
          .reduce((a, b) => a + b, 0) / downsideReturns.length,
      ) :
      0
    const sortinoRatio = downsideDev !== 0 ? (avgReturn / downsideDev) * Math.sqrt(252) : 0

    // Calculate trade statistics
    const winningTradesList = trades.filter(t => t.pnl > 0)
    const losingTradesList = trades.filter(t => t.pnl <= 0)
    const totalWins = winningTradesList.reduce((sum, t) => sum + t.pnl, 0)
    const totalLosses = Math.abs(losingTradesList.reduce((sum, t) => sum + t.pnl, 0))

    const winRate = winningTrades / totalTrades
    const profitFactor = totalLosses !== 0 ? totalWins / totalLosses : Infinity
    const averageWin = winningTradesList.length > 0 ?
      winningTradesList.reduce((sum, t) => sum + t.pnl, 0) / winningTradesList.length :
      0
    const averageLoss = losingTradesList.length > 0 ?
      losingTradesList.reduce((sum, t) => sum + t.pnl, 0) / losingTradesList.length :
      0

    return {
      totalReturn,
      annualizedReturn,
      sharpeRatio,
      sortinoRatio,
      maxDrawdown,
      winRate,
      profitFactor,
      averageWin,
      averageLoss,
      totalTrades,
      winningTrades,
      losingTrades: totalTrades - winningTrades,
    }
  }

  // Helper method to get a slice of data
  getSlice(data, index, window = 100) {
    const slice = {}
    for (const key in data) {
      if (Array.isArray(data[key])) {
        slice[key] = data[key].slice(Math.max(0, index - window), index)
      } else if (typeof data[key] === 'object') {
        slice[key] = {}
        for (const subKey in data[key]) {
          if (Array.isArray(data[key][subKey])) {
            slice[key][subKey] = data[key][subKey].slice(Math.max(0, index - window), index)
          } else {
            slice[key][subKey] = data[key][subKey]
          }
        }
      } else {
        slice[key] = data[key]
      }
    }
    return slice
  }

  // Load historical data (implement according to your data source)
  async loadHistoricalData(pair, timeframe, startDate, endDate) {
    // This is a placeholder - implement according to your data source
    // You might load from a database, CSV, or API
    console.log(`Loading historical data for ${pair} ${timeframe} from ${startDate} to ${endDate}`)

    // Example implementation:
    try {
      // Your implementation here
      return [] // Return OHLCV data
    } catch (error) {
      console.error('Error loading historical data:', error)
      throw error
    }
  }

  // Save backtest results to file
  async saveResults(filename = 'backtest_results.json') {
    try {
      const dir = path.join(__dirname, '../../backtest_results')
      await fs.mkdir(dir, { recursive: true })

      const filepath = path.join(dir, filename)
      await fs.writeFile(filepath, JSON.stringify(this.results, null, 2))
      console.log(`Backtest results saved to ${filepath}`)
      return filepath
    } catch (error) {
      console.error('Error saving backtest results:', error)
      throw error
    }
  }

  // Load backtest results from file
  async loadResults(filename = 'backtest_results.json') {
    try {
      const filepath = path.join(__dirname, '../../backtest_results', filename)
      const data = await fs.readFile(filepath, 'utf8')
      this.results = JSON.parse(data)
      console.log(`Loaded ${this.results.length} backtest results from ${filepath}`)
      return this.results
    } catch (error) {
      console.error('Error loading backtest results:', error)
      throw error
    }
  }

  // Generate a report of backtest results
  generateReport() {
    if (this.results.length === 0) {
      return 'No backtest results available'
    }

    let report = '=== Backtest Report ===\n\n'

    this.results.forEach((result, index) => {
      const perf = result.performance

      report += `Strategy: ${result.strategy}\n`
      report += `Pair: ${result.pair} | Timeframe: ${result.timeframe}\n`
      report += `Period: ${new Date(result.startDate).toISOString().split('T')[0]} to ${new Date(result.endDate).toISOString().split('T')[0]}\n`
      report += `Initial Balance: $${result.initialBalance.toFixed(2)}\n`
      report += `Final Balance: $${result.finalBalance.toFixed(2)}\n`
      report += `Total Return: ${(perf.totalReturn * 100).toFixed(2)}%\n`
      report += `Annualized Return: ${(perf.annualizedReturn * 100).toFixed(2)}%\n`
      report += `Sharpe Ratio: ${perf.sharpeRatio.toFixed(2)}\n`
      report += `Sortino Ratio: ${perf.sortinoRatio.toFixed(2)}\n`
      report += `Max Drawdown: ${(perf.maxDrawdown * 100).toFixed(2)}%\n`
      report += `Win Rate: ${(perf.winRate * 100).toFixed(2)}%\n`
      report += `Profit Factor: ${perf.profitFactor.toFixed(2)}\n`
      report += `Total Trades: ${perf.totalTrades}\n`
      report += `Winning Trades: ${perf.winningTrades}\n`
      report += `Losing Trades: ${perf.losingTrades}\n\n`
    })

    return report
  }
}

// Export singleton instance
module.exports = new Backtester()
