/**
 * StarCrypt System Enhancement Final
 * Comprehensive system optimization and enhancement suite
 */

class SystemEnhancementFinal {
  constructor() {
    this.enhancementActive = false;
    this.optimizationLevel = 'maximum';
    this.performanceMetrics = new Map();
    
    this.init();
  }

  init() {
    console.log('🚀 SYSTEM ENHANCEMENT FINAL: Initializing comprehensive optimization suite...');
    
    this.applyFinalEnhancements();
    this.optimizePerformance();
    this.enhanceUserExperience();
    this.setupAdvancedMonitoring();
    
    this.enhancementActive = true;
    console.log('✅ SYSTEM ENHANCEMENT FINAL: All enhancements applied successfully');
  }

  applyFinalEnhancements() {
    // 🎯 THRESHOLD SLIDERS FINAL ENHANCEMENT
    this.enhanceThresholdSliders();
    
    // 🎯 SIGNAL LOGIC POSITIONING FINAL FIX
    this.enhanceSignalLogicPositioning();
    
    // 🎯 MINI CHARTS DATA VISUALIZATION ENHANCEMENT
    this.enhanceMiniChartsVisualization();
    
    // 🎯 TIMEFRAME SYSTEM OPTIMIZATION
    this.optimizeTimeframeSystem();
    
    // 🎯 TOOLTIP SYSTEM COORDINATION
    this.coordinateTooltipSystems();
  }

  enhanceThresholdSliders() {
    console.log('🎚️ FINAL ENHANCEMENT: Optimizing threshold sliders...');
    
    // Add dynamic scaling based on viewport
    const style = document.createElement('style');
    style.textContent = `
      /* FINAL THRESHOLD SLIDER ENHANCEMENTS */
      .sliders-container {
        transform: scale(1);
        transform-origin: top left;
        transition: transform 0.3s ease;
      }
      
      @media (max-width: 1200px) {
        .sliders-container {
          transform: scale(0.9);
        }
      }
      
      @media (max-width: 900px) {
        .sliders-container {
          transform: scale(0.8);
        }
      }
      
      /* Enhanced thumb interactions */
      .slider-thumb {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      .slider-thumb:hover {
        filter: brightness(1.2) drop-shadow(0 0 10px currentColor);
      }
      
      .slider-thumb.dragging {
        filter: brightness(1.4) drop-shadow(0 0 15px currentColor);
        transform: translateX(-50%) scale(1.3);
      }
      
      /* Smooth track updates */
      .slider-track {
        transition: background 0.3s ease;
      }
    `;
    document.head.appendChild(style);
  }

  enhanceSignalLogicPositioning() {
    console.log('🎯 FINAL ENHANCEMENT: Perfecting signal logic positioning...');
    
    // Ensure signal logic menu always opens in the correct position
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const target = mutation.target;
          if (target.id === 'logicMenu' && target.style.display === 'block') {
            this.ensureCorrectMenuPositioning(target);
          }
        }
      });
    });

    const logicMenu = document.getElementById('logicMenu');
    if (logicMenu) {
      observer.observe(logicMenu, { attributes: true });
    }
  }

  ensureCorrectMenuPositioning(menu) {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const momentumContainer = document.querySelector('.momentum-indicators');
    
    if (momentumContainer) {
      const momentumRect = momentumContainer.getBoundingClientRect();
      
      // Perfect positioning algorithm
      const leftPosition = Math.max(
        momentumRect.right + 30,
        viewportWidth * 0.65
      );
      
      const topPosition = Math.max(
        100,
        momentumRect.top,
        viewportHeight * 0.15
      );
      
      menu.style.left = `${leftPosition}px`;
      menu.style.top = `${topPosition}px`;
      menu.style.transform = 'none';
    }
  }

  enhanceMiniChartsVisualization() {
    console.log('📊 FINAL ENHANCEMENT: Optimizing mini chart visualization...');
    
    // Enhance mini chart update frequency
    if (window.enhancedMiniCharts) {
      const originalUpdate = window.enhancedMiniCharts.updateCharts;
      window.enhancedMiniCharts.updateCharts = function() {
        // Throttle updates to prevent performance issues
        if (!this.lastUpdate || Date.now() - this.lastUpdate > 1000) {
          originalUpdate.call(this);
          this.lastUpdate = Date.now();
        }
      };
    }
    
    // Add chart animation enhancements
    const chartStyle = document.createElement('style');
    chartStyle.textContent = `
      .enhanced-chart-container {
        transition: all 0.3s ease;
      }
      
      .enhanced-chart-container:hover {
        transform: scale(1.02);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
      }
      
      .mini-chart-canvas {
        transition: opacity 0.3s ease;
      }
      
      .chart-value-display {
        animation: valueUpdate 0.5s ease when data changes;
      }
      
      @keyframes valueUpdate {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); color: #00FF00; }
        100% { transform: scale(1); }
      }
    `;
    document.head.appendChild(chartStyle);
  }

  optimizeTimeframeSystem() {
    console.log('⏱️ FINAL ENHANCEMENT: Optimizing timeframe system...');
    
    // Add intelligent timeframe management
    if (window.timeframeCheckboxFixes) {
      const originalHandleChange = window.timeframeCheckboxFixes.handleCheckboxChange;
      window.timeframeCheckboxFixes.handleCheckboxChange = function(checkbox) {
        // Add validation and optimization
        const timeframe = checkbox.dataset.timeframe;
        
        // Check server capacity before adding timeframes
        if (checkbox.checked && this.activeTimeframes.length >= 5) {
          const confirmation = confirm(`Adding ${timeframe} will increase data load. Continue?`);
          if (!confirmation) {
            checkbox.checked = false;
            return;
          }
        }
        
        return originalHandleChange.call(this, checkbox);
      };
    }
  }

  coordinateTooltipSystems() {
    console.log('💬 FINAL ENHANCEMENT: Coordinating tooltip systems...');
    
    // Ensure tooltips don't conflict
    if (window.dualTooltipSystem && window.tooltipFixes) {
      const originalShow = window.tooltipFixes.showTooltip;
      window.tooltipFixes.showTooltip = function(...args) {
        // Hide dual tooltips when showing main tooltip
        if (window.dualTooltipSystem) {
          window.dualTooltipSystem.hideAllTooltips();
        }
        return originalShow.apply(this, args);
      };
    }
  }

  optimizePerformance() {
    console.log('⚡ FINAL ENHANCEMENT: Applying performance optimizations...');
    
    // Optimize animation frames
    this.optimizeAnimationFrames();
    
    // Optimize memory usage
    this.optimizeMemoryUsage();
    
    // Optimize event listeners
    this.optimizeEventListeners();
  }

  optimizeAnimationFrames() {
    // Throttle requestAnimationFrame calls
    const originalRAF = window.requestAnimationFrame;
    let rafQueue = [];
    let rafRunning = false;
    
    window.requestAnimationFrame = function(callback) {
      rafQueue.push(callback);
      
      if (!rafRunning) {
        rafRunning = true;
        originalRAF(() => {
          const callbacks = rafQueue.splice(0);
          rafRunning = false;
          
          // Execute callbacks in batches
          callbacks.forEach(cb => {
            try {
              cb();
            } catch (error) {
              console.warn('RAF callback error:', error);
            }
          });
        });
      }
    };
  }

  optimizeMemoryUsage() {
    // Clean up old data periodically
    setInterval(() => {
      this.performMemoryCleanup();
    }, 60000); // Every minute
  }

  performMemoryCleanup() {
    const now = Date.now();
    const maxAge = 300000; // 5 minutes
    
    // Clean up old WebSocket timestamps
    if (window.wsDataTimestamps) {
      for (const timeframe in window.wsDataTimestamps) {
        for (const indicator in window.wsDataTimestamps[timeframe]) {
          if (now - window.wsDataTimestamps[timeframe][indicator] > maxAge) {
            delete window.wsDataTimestamps[timeframe][indicator];
          }
        }
      }
    }
    
    // Clean up old error logs
    if (window.errorRecoverySystem && window.errorRecoverySystem.errorHistory) {
      window.errorRecoverySystem.errorHistory = window.errorRecoverySystem.errorHistory
        .filter(error => now - error.timestamp < maxAge);
    }
  }

  optimizeEventListeners() {
    // Use passive listeners where possible
    const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove'];
    
    passiveEvents.forEach(eventType => {
      const originalAddEventListener = EventTarget.prototype.addEventListener;
      EventTarget.prototype.addEventListener = function(type, listener, options) {
        if (passiveEvents.includes(type) && typeof options !== 'object') {
          options = { passive: true };
        }
        return originalAddEventListener.call(this, type, listener, options);
      };
    });
  }

  enhanceUserExperience() {
    console.log('🎨 FINAL ENHANCEMENT: Enhancing user experience...');
    
    // Add smooth transitions
    this.addSmoothTransitions();
    
    // Add keyboard shortcuts
    this.addKeyboardShortcuts();
    
    // Add visual feedback
    this.addVisualFeedback();
  }

  addSmoothTransitions() {
    const transitionStyle = document.createElement('style');
    transitionStyle.textContent = `
      /* SMOOTH TRANSITIONS FOR ALL INTERACTIVE ELEMENTS */
      .signal-circle, .menu-button, .threshold-slider, .timeframe-button {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      .menu-container {
        transition: opacity 0.3s ease, transform 0.3s ease;
      }
      
      .menu-container.opening {
        animation: menuSlideIn 0.4s ease-out;
      }
      
      @keyframes menuSlideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `;
    document.head.appendChild(transitionStyle);
  }

  addKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+T = Toggle timeframes menu
      if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        const timeframesBtn = document.getElementById('toggleTimeframesButton');
        if (timeframesBtn) timeframesBtn.click();
        e.preventDefault();
      }
      
      // Ctrl+Shift+L = Toggle light logic menu
      if (e.ctrlKey && e.shiftKey && e.key === 'L') {
        const lightLogicBtn = document.getElementById('toggleLightLogicButton');
        if (lightLogicBtn) lightLogicBtn.click();
        e.preventDefault();
      }
      
      // Ctrl+Shift+S = Toggle signal logic menu
      if (e.ctrlKey && e.shiftKey && e.key === 'S') {
        const signalLogicBtn = document.getElementById('toggleLogicButton');
        if (signalLogicBtn) signalLogicBtn.click();
        e.preventDefault();
      }
    });
  }

  addVisualFeedback() {
    // Add click feedback to all buttons
    document.addEventListener('click', (e) => {
      if (e.target.matches('button, .menu-button, .signal-circle')) {
        e.target.style.transform = 'scale(0.95)';
        setTimeout(() => {
          e.target.style.transform = '';
        }, 150);
      }
    });
  }

  setupAdvancedMonitoring() {
    console.log('📊 FINAL ENHANCEMENT: Setting up advanced monitoring...');
    
    // Monitor system performance
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 5000);
  }

  updatePerformanceMetrics() {
    const metrics = {
      timestamp: Date.now(),
      memory: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 'Unknown',
      signalLights: document.querySelectorAll('.signal-circle').length,
      connectedLights: document.querySelectorAll('.signal-circle[data-admiral-connected="true"]').length,
      activeTimeframes: window.timeframeCheckboxFixes ? window.timeframeCheckboxFixes.activeTimeframes.length : 0,
      websocketStatus: window.ws ? window.ws.readyState : 'Unknown'
    };
    
    this.performanceMetrics.set('current', metrics);
    
    // Log performance summary every minute
    if (metrics.timestamp % 60000 < 5000) {
      console.log('📊 PERFORMANCE SUMMARY:', metrics);
    }
  }

  // Public API
  getPerformanceMetrics() {
    return this.performanceMetrics.get('current');
  }

  getSystemStatus() {
    return {
      enhancementActive: this.enhancementActive,
      optimizationLevel: this.optimizationLevel,
      performance: this.getPerformanceMetrics()
    };
  }
}

// Initialize system enhancement
window.systemEnhancementFinal = new SystemEnhancementFinal();

// Export for global access
window.SystemEnhancementFinal = SystemEnhancementFinal;

// Add to global controls
if (window.starCryptControls) {
  window.starCryptControls.getSystemStatus = () => window.systemEnhancementFinal.getSystemStatus();
  window.starCryptControls.getPerformance = () => window.systemEnhancementFinal.getPerformanceMetrics();
}

console.log('🚀 SYSTEM ENHANCEMENT FINAL: Complete optimization suite loaded and active');
