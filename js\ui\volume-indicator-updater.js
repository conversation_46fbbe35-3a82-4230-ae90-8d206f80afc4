"use strict";

// Store last volume status to prevent unnecessary updates
let lastVolumeStatus = null;

window.updateVolumeStatusDisplay = function(volumeData) {
    const volumeElement = document.getElementById('oracle-matrix-volume-status');
    if (!volumeElement) {
        console.warn('Volume status element (oracle-matrix-volume-status) not found.');
        return;
    }

    if (!volumeData || typeof volumeData !== 'object') {
        const newStatus = 'Volume N/A';
        if (lastVolumeStatus !== newStatus) {
            volumeElement.textContent = newStatus;
            volumeElement.className = 'volume-status'; // Reset to base class
            lastVolumeStatus = newStatus;
        }
        return;
    }

    // The server's calculateVolumeSpike function provides 'spike', 'avg', 'current', 'signalClass'
    // We can derive the ratio if needed, but 'spike' and 'signalClass' are more direct for UI states.
    const ratio = volumeData.current / volumeData.avg; // Calculate ratio for finer-grained conditions

    let textContent = 'Volume N/A';
    let newClassName = 'volume-status'; // Start with base class

    if (volumeData.spike === true) { // ratio > 1.5
        textContent = 'Volume Spike Detected!';
        newClassName += ' volume-spike';
    } else if (volumeData.signalClass === 'neutral') {
        if (ratio > 1.0) { // 1.0 < ratio <= 1.5
            textContent = 'Volume Above Average';
            newClassName += ' volume-high';
        } else { // 0.7 <= ratio <= 1.0
            textContent = 'Volume Normal';
            newClassName += ' volume-normal';
        }
    } else if (volumeData.signalClass === 'mild-buy') { // 0.3 <= ratio < 0.7
        textContent = 'Volume Low';
        newClassName += ' volume-low';
    } else if (volumeData.signalClass === 'degen-buy') { // ratio < 0.3
        textContent = 'Volume Very Low';
        newClassName += ' volume-very-low';
    } else {
        // Fallback for any other unhandled signalClass or if data is incomplete
        textContent = `Volume: ${volumeData.value ? volumeData.value.toFixed(2) + '%' : 'N/A'}`;
    }

    // Only update if the status has actually changed
    const currentStatus = `${textContent}|${newClassName}`;
    if (lastVolumeStatus !== currentStatus) {
        volumeElement.textContent = textContent;
        volumeElement.className = newClassName;
        lastVolumeStatus = currentStatus;
        console.log(`🔊 Volume status updated: ${textContent}`);
    }
};

// Test function to verify volume status updates
window.testVolumeStatus = function() {
    console.log('Testing volume status updates...');
    
    // Test different volume states
    const testCases = [
        { current: 200, avg: 100, spike: true, signalClass: 'neutral' },
        { current: 120, avg: 100, spike: false, signalClass: 'neutral' },
        { current: 80, avg: 100, spike: false, signalClass: 'neutral' },
        { current: 60, avg: 100, spike: false, signalClass: 'mild-buy' },
        { current: 20, avg: 100, spike: false, signalClass: 'degen-buy' }
    ];

    let index = 0;
    const interval = setInterval(() => {
        if (index >= testCases.length) {
            clearInterval(interval);
            console.log('Volume status test completed');
            return;
        }
        
        const testCase = testCases[index];
        console.log(`Testing volume status:`, testCase);
        window.updateVolumeStatusDisplay(testCase);
        index++;
    }, 2000);
};

// Auto-test DISABLED - was causing blips mode
// if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
//     document.addEventListener('DOMContentLoaded', () => {
//         console.log('Running volume status self-test...');
//         setTimeout(window.testVolumeStatus, 2000);
//     });
// }

console.log('[VolumeIndicatorUpdater] 🚫 Auto-test disabled to prevent blips mode');
