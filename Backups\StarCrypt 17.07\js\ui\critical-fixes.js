/**
 * Critical Fixes for StarCrypt Issues
 * Addresses menu opening, hover cursors, mini charts, and threshold sliders
 */

class CriticalFixes {
  constructor() {
    this.init();
  }

  init() {
    console.log('[CriticalFixes] Applying critical fixes...');
    
    // Fix menu opening issues
    this.fixMenuOpening();
    
    // Fix hover cursor issues
    this.fixHoverCursors();
    
    // Fix mini chart styling
    this.fixMiniCharts();
    
    // Fix threshold menu layout
    this.fixThresholdMenu();
    
    // Fix starfield animation size
    this.fixStarfieldSize();
    
    // Fix indicator menu toggles
    this.fixIndicatorMenuToggles();

    // Check duplicate functions
    this.checkDuplicateFunctions();

    console.log('[CriticalFixes] Critical fixes applied successfully');
  }

  fixMenuOpening() {
    console.log('[CriticalFixes] Fixing menu opening issues...');
    
    // Ensure signal logic button works
    const signalLogicBtn = document.getElementById('toggleLogicButton');
    if (signalLogicBtn) {
      // Remove existing listeners
      signalLogicBtn.replaceWith(signalLogicBtn.cloneNode(true));
      const newSignalLogicBtn = document.getElementById('toggleLogicButton');
      
      newSignalLogicBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('[CriticalFixes] Signal Logic button clicked');

        // Close other menus first
        this.closeAllMenus();

        // Toggle signal logic menu
        const menu = document.getElementById('logicMenu');
        if (menu) {
          if (menu.classList.contains('active')) {
            menu.classList.remove('active');
          } else {
            // Position menu using standard system
            this.positionMenu(menu, newSignalLogicBtn);
            menu.classList.add('active');
            if (typeof renderLogicMenu === 'function') {
              renderLogicMenu();
            }
          }
        }
      });
    }

    // Ensure light logic button works
    const lightLogicBtn = document.getElementById('toggleLightLogicButton');
    if (lightLogicBtn) {
      // Remove existing listeners
      lightLogicBtn.replaceWith(lightLogicBtn.cloneNode(true));
      const newLightLogicBtn = document.getElementById('toggleLightLogicButton');
      
      newLightLogicBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        console.log('[CriticalFixes] Light Logic button clicked');

        // Close other menus first
        this.closeAllMenus();

        // Toggle light logic menu
        const menu = document.getElementById('lightLogicMenu');
        if (menu) {
          if (menu.classList.contains('active')) {
            menu.classList.remove('active');
          } else {
            // Position menu using standard system
            this.positionMenu(menu, newLightLogicBtn);
            menu.classList.add('active');
            if (typeof renderLightLogicMenu === 'function') {
              renderLightLogicMenu();
            }
          }
        }
      });
    }
  }

  closeAllMenus() {
    const menus = ['logicMenu', 'strategyMenu', 'indicatorMenu', 'lightLogicMenu', 'thresholdsMenu', 'timeframesMenu'];
    menus.forEach(menuId => {
      const menu = document.getElementById(menuId);
      if (menu) {
        menu.classList.remove('active');
      }
    });
  }

  positionMenu(menu, button) {
    if (!menu || !button) return;

    const buttonRect = button.getBoundingClientRect();

    // Position the menu below the button
    menu.style.position = 'fixed';
    menu.style.top = `${buttonRect.bottom + window.scrollY}px`;

    // 🚀 AGGRESSIVE MENU POSITIONING - MOVE 150PX+ RIGHT TO KEEP MOMENTUM-INDICATORS VISIBLE
    const momentumContainer = document.querySelector('.momentum-indicators');
    let leftPosition = buttonRect.left + window.scrollX;

    if (momentumContainer) {
      const momentumRect = momentumContainer.getBoundingClientRect();
      // 💥 FORCE POSITION MENU TO THE RIGHT OF MOMENTUM-INDICATORS WITH 150PX+ GAP
      leftPosition = Math.max(leftPosition + 150, momentumRect.right + 150);
      console.log(`[CriticalFixes] 🎯 AGGRESSIVE positioning: momentum right edge ${momentumRect.right}, menu left ${leftPosition}`);
    } else {
      // 🔥 FALLBACK: FORCE MOVE 450PX TO THE RIGHT FOR MAXIMUM CLEARANCE
      leftPosition += 450;
      console.log(`[CriticalFixes] 🚀 FALLBACK positioning: moved ${leftPosition}px right`);
    }

    menu.style.left = `${leftPosition}px`;
    menu.style.zIndex = '1000';

    // Force reflow to ensure menu dimensions are calculated
    void menu.offsetHeight;

    // Get menu dimensions after rendering
    const menuRect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

    // Adjust if menu goes off right side of screen
    if (menuRect.right > viewportWidth) {
      menu.style.left = `${Math.max(10, viewportWidth - menuRect.width - 10)}px`;
    }

    // Adjust if menu goes off bottom of screen
    if (menuRect.bottom > viewportHeight) {
      menu.style.top = `${Math.max(10, buttonRect.top - menuRect.height)}px`;
    }
  }

  fixHoverCursors() {
    console.log('[CriticalFixes] Fixing hover cursor issues...');
    
    // Add CSS to fix cursor inconsistencies
    const style = document.createElement('style');
    style.textContent = `
      /* Fix hover cursors - only actionable elements should show pointer */
      .signal-light, .circle, .indicator-circle {
        cursor: pointer !important;
      }
      
      .signal-light:hover, .circle:hover, .indicator-circle:hover {
        cursor: pointer !important;
      }
      
      /* Remove question mark cursors from non-actionable elements */
      .indicator-label, .timeframe-header, .indicator-name {
        cursor: default !important;
      }
      
      /* Ensure buttons have proper cursor */
      button, .menu-button, .apply-button {
        cursor: pointer !important;
      }
      
      /* Fix mini chart hover */
      .mini-chart {
        cursor: default !important;
      }
      
      .mini-chart:hover {
        cursor: default !important;
      }
    `;
    document.head.appendChild(style);
  }

  fixMiniCharts() {
    console.log('[CriticalFixes] Fixing mini chart styling...');
    
    // Enhanced mini chart styling
    const miniChartStyle = document.createElement('style');
    miniChartStyle.textContent = `
      /* Enhanced Mini Chart Styling */
      .mini-chart {
        position: relative;
        width: 185px;
        height: 42px;
        background: rgba(0, 10, 20, 0.8) !important;
        border: 1px solid rgba(0, 255, 255, 0.3) !important;
        border-radius: 4px !important;
        overflow: hidden;
        margin: 2px 0;
      }
      
      .mini-chart canvas {
        width: 100% !important;
        height: 100% !important;
        display: block !important;
        background: transparent !important;
      }
      
      .mini-chart .live-readout {
        position: absolute;
        top: 2px;
        right: 4px;
        font-size: 11px !important;
        font-weight: bold !important;
        background: rgba(0, 0, 0, 0.7) !important;
        padding: 2px 4px !important;
        border-radius: 2px !important;
        z-index: 10;
      }
      
      .mini-chart .indicator-label {
        position: absolute;
        bottom: 2px;
        left: 4px;
        font-size: 9px !important;
        color: rgba(255, 255, 255, 0.7) !important;
        text-transform: uppercase;
        background: rgba(0, 0, 0, 0.5);
        padding: 1px 3px;
        border-radius: 2px;
      }
      
      .mini-chart .grid-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: 
          linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 20px 10px;
        pointer-events: none;
        opacity: 0.3;
      }
    `;
    document.head.appendChild(miniChartStyle);
    
    // Force update all mini charts
    setTimeout(() => {
      this.updateAllMiniCharts();
    }, 1000);
  }

  updateAllMiniCharts() {
    // DISABLED - Enhanced charts handle updates now
    console.log('[CriticalFixes] DISABLED - Mini chart updates handled by enhanced charts');
  }

  fixThresholdMenu() {
    console.log('[CriticalFixes] Fixing threshold menu layout...');
    
    // Apply the backup threshold slider styles
    const thresholdStyle = document.createElement('style');
    thresholdStyle.textContent = `
      /* Threshold Menu Layout Fix - From Backup */
      .thresholds-menu-content {
        max-height: 60vh !important;
        overflow-y: auto;
        padding: 10px;
      }
      
      .threshold-item {
        margin-bottom: 15px;
        background: rgba(0, 20, 40, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 6px;
        padding: 8px;
      }
      
      .threshold-name {
        font-size: 12px !important;
        color: #00d4ff;
        margin-bottom: 5px;
        height: auto !important;
        line-height: 1.2;
      }
      
      .slider-container {
        position: relative;
        height: 20px !important;
        margin-bottom: 8px;
      }
      
      .slider-bar {
        position: relative;
        z-index: 1;
        width: 100%;
        height: 20px;
        display: flex;
        border-radius: 5px;
        overflow: hidden;
      }
      
      .threshold-slider {
        position: absolute;
        width: 100%;
        height: 20px;
        top: 0;
        left: 0;
        margin: 0;
        opacity: 0.01;
        cursor: pointer;
      }
      
      .threshold-slider.green { z-index: 11; }
      .threshold-slider.blue { z-index: 12; }
      .threshold-slider.orange { z-index: 13; }
      .threshold-slider.red { z-index: 14; }
      
      .threshold-labels {
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-top: 5px;
      }
      
      .threshold-label {
        font-size: 10px !important;
        margin: 1px 2px;
        color: #CCCCCC;
        display: flex;
        align-items: center;
      }
      
      .color-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 3px;
      }
      
      .color-dot.green { background-color: #00FF00; box-shadow: 0 0 3px #00FF00; }
      .color-dot.blue { background-color: #0088FF; box-shadow: 0 0 3px #0088FF; }
      .color-dot.grey { background-color: #808080; box-shadow: 0 0 3px #808080; }
      .color-dot.orange { background-color: #FFA500; box-shadow: 0 0 3px #FFA500; }
      .color-dot.red { background-color: #FF0000; box-shadow: 0 0 3px #FF0000; }
    `;
    document.head.appendChild(thresholdStyle);
  }

  fixStarfieldSize() {
    console.log('[CriticalFixes] Fixing starfield animation size...');
    
    const starfieldStyle = document.createElement('style');
    starfieldStyle.textContent = `
      /* Starfield Animation Size Fix */
      #starfield-canvas {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: -1 !important;
        pointer-events: none !important;
      }
      
      .starfield-container {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: -1 !important;
        pointer-events: none !important;
        overflow: hidden !important;
      }
    `;
    document.head.appendChild(starfieldStyle);
    
    // Update starfield canvas if it exists
    const starfieldCanvas = document.getElementById('starfield-canvas');
    if (starfieldCanvas) {
      starfieldCanvas.width = window.innerWidth;
      starfieldCanvas.height = window.innerHeight;
      starfieldCanvas.style.width = '100vw';
      starfieldCanvas.style.height = '100vh';
    }
  }

  fixIndicatorMenuToggles() {
    console.log('[CriticalFixes] Fixing indicator menu toggle issues...');
    
    const toggleStyle = document.createElement('style');
    toggleStyle.textContent = `
      /* Fix Indicator Menu Toggle Alignment */
      .indicator-toggle {
        position: relative;
        width: 40px !important;
        height: 20px !important;
        margin: 0 5px;
      }
      
      .indicator-toggle input[type="checkbox"] {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        opacity: 0 !important;
        cursor: pointer !important;
        z-index: 10 !important;
      }
      
      .indicator-toggle .slider {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #333;
        border-radius: 20px;
        transition: 0.3s;
        cursor: pointer;
      }
      
      .indicator-toggle .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: 50%;
        transition: 0.3s;
      }
      
      .indicator-toggle input:checked + .slider {
        background-color: #00d4ff;
      }
      
      .indicator-toggle input:checked + .slider:before {
        transform: translateX(20px);
      }
      
      /* Fix Apply Button */
      .apply-indicators-button {
        width: 100% !important;
        padding: 10px !important;
        margin-top: 15px !important;
        background: linear-gradient(135deg, #00d4ff, #0099cc) !important;
        border: none !important;
        color: #000 !important;
        font-weight: bold !important;
        border-radius: 6px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
      }
      
      .apply-indicators-button:hover {
        background: linear-gradient(135deg, #00b8e6, #0088bb) !important;
        transform: translateY(-1px) !important;
      }
    `;
    document.head.appendChild(toggleStyle);
  }

  // Check if refresh indicators and force update lights are duplicates
  checkDuplicateFunctions() {
    console.log('[CriticalFixes] Analyzing Refresh Indicators vs Force Update Lights...');

    const refreshBtn = document.getElementById('refreshIndicatorsButton');
    const forceUpdateBtn = document.getElementById('forceUpdateLightsButton');

    if (refreshBtn && forceUpdateBtn) {
      console.log('[CriticalFixes] ANALYSIS COMPLETE:');
      console.log('- Refresh Indicators: Calls requestAllIndicators() - Requests fresh data from server via WebSocket');
      console.log('- Force Update Lights: Calls forceUpdateSignalLights() - Recalculates colors using existing data');
      console.log('- CONCLUSION: These are NOT duplicates - they serve different purposes');

      // Add distinctive tooltips to clarify their purposes
      refreshBtn.title = 'Refresh Indicators: Request fresh data from server for all timeframes';
      forceUpdateBtn.title = 'Force Update Lights: Recalculate signal colors using current data';

      // Add visual distinction
      refreshBtn.style.borderLeft = '3px solid #00ff88'; // Green for data refresh
      forceUpdateBtn.style.borderLeft = '3px solid #ffa502'; // Orange for visual update

      // Log detailed functionality when clicked
      refreshBtn.addEventListener('click', () => {
        console.log('[CriticalFixes] 🔄 Refresh Indicators: Requesting fresh data from server...');
      });

      forceUpdateBtn.addEventListener('click', () => {
        console.log('[CriticalFixes] 💡 Force Update Lights: Recalculating signal colors...');
      });

      return {
        areDuplicates: false,
        refreshFunction: 'requestAllIndicators() - Fetches new data from server',
        forceUpdateFunction: 'forceUpdateSignalLights() - Recalculates colors from existing data',
        recommendation: 'Keep both buttons - they serve different purposes'
      };
    }

    return { areDuplicates: false, error: 'Buttons not found' };
  }
}

// Initialize critical fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.criticalFixes = new CriticalFixes();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CriticalFixes;
}
