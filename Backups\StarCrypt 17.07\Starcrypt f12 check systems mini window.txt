// 🚀 DEGEN ARMY TACTICAL DISPLAY
function createTacticalDisplay() {
  const panel = document.createElement('div');
  panel.id = 'degen-tactical-display';
  panel.style.cssText = `
    position: fixed; top: 10px; right: 10px; width: 300px; height: 200px;
    background: rgba(0, 20, 40, 0.9); border: 2px solid #00ffff;
    color: #00ffff; font-family: monospace; font-size: 10px;
    padding: 10px; z-index: 9999; overflow-y: auto;
    border-radius: 8px; box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  `;
  
  function updateDisplay() {
    const mlSystem = window.MLHistoricalAnalysis || window.mlHistoricalAnalysis;
    panel.innerHTML = `
      <div style="color: #ffff00; font-weight: bold;">🚀 DEGEN TACTICAL STATUS</div>
      <div>ML System: ${mlSystem ? '✅ ONLINE' : '❌ OFFLINE'}</div>
      <div>Selected Lights: ${mlSystem?.selectedLights?.size || 0}</div>
      <div>Fallback Selections: ${document.querySelectorAll('.signal-circle.selected').length}</div>
      <div>Admiral Mode: ${document.querySelector('[data-admiral-mode]') ? '✅ ACTIVE' : '❌ INACTIVE'}</div>
      <div>Analyze Button: ${document.getElementById('analyzeConvergence')?.disabled ? '❌ DISABLED' : '✅ ARMED'}</div>
      <div style="margin-top: 10px; color: #00ff00;">Last Update: ${new Date().toLocaleTimeString()}</div>
    `;
  }
  
  document.body.appendChild(panel);
  setInterval(updateDisplay, 1000);
  updateDisplay();
  
  console.log('🚀 TACTICAL DISPLAY DEPLOYED - Check top-right corner');
}

createTacticalDisplay();