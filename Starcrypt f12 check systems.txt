// 🚀 DEGEN ARMY CONDENSED DIAGNOSTIC - SCOTTIE'S THERMAL SCAN
function scottyDiagnostic() {
  console.log('🔧 SCOTTIE: Checking forward shields and thermal reactors...');
  
  const diagnostics = {
    // THERMAL REACTOR STATUS
    mlSystems: {
      MLHistoricalAnalysis: !!window.MLHistoricalAnalysis,
      mlHistoricalAnalysis: !!window.mlHistoricalAnalysis,
      selectedLightsSize: window.MLHistoricalAnalysis?.selectedLights?.size || window.mlHistoricalAnalysis?.selectedLights?.size || 0
    },
    
    // FORWARD SHIELD STATUS
    admiralMode: document.querySelector('[data-admiral-mode]')?.textContent || 'UNKNOWN',
    selectedSignals: document.querySelectorAll('.signal-circle.selected, .signal-light.selected').length,
    analyzeButton: {
      exists: !!document.getElementById('analyzeConvergence'),
      disabled: document.getElementById('analyzeConvergence')?.disabled,
      style: document.getElementById('analyzeConvergence')?.style.background
    },
    
    // SYSTEM SYNC STATUS
    fallbackSelections: document.getElementById('selectedLightsDisplay')?.textContent?.includes('selections') || false,
    liveAnalysis: document.getElementById('mlInsightsText')?.textContent?.includes('LIVE ANALYSIS') || false,
    
    // CRITICAL ERRORS
    lastErrors: console.error.toString().includes('selectedLights') ? 'ML_SELECTION_ERROR' : 'NO_CRITICAL_ERRORS'
  };
  
  console.log('🔧 SCOTTIE DIAGNOSTIC COMPLETE:', JSON.stringify(diagnostics, null, 2));
  return diagnostics;
}

// Run diagnostic
scottyDiagnostic();