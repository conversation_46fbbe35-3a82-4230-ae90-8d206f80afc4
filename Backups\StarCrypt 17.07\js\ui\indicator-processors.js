// Advanced Indicator Processors for StarCrypt
// Ensures all indicators have proper data, mini-charts, and 5-color logic

// Data models for advanced indicators
const INDICATOR_MODELS = {
  volume: {
    title: 'Volume Analysis',
    description: 'Volume represents the total amount of trading activity. Increasing volume often confirms price movements.',
    chartConfig: {
      type: 'bar',
      options: {
        scales: {
          y: { beginAtZero: true },
        },
        backgroundColor: context => {
          const value = context.raw
          return value > 0 ? 'rgba(0, 255, 0, 0.6)' : 'rgba(255, 0, 0, 0.6)'
        },
      },
    },
    colorLogic: (value) => {
      if (value > 80) return '#00FF00' // Function to convert signal to color with reliable 5-color logic
      if (value > 60) return '#00AAFF' // Mild buy
      if (value < 20) return '#FF0000' // Strong sell - decreasing volume bearish
      if (value < 40) return '#FFA500' // Mild sell
      return '#808080' // Neutral - consistent gray color
    },
    tooltip: (value, change) => `Volume: ${value.toFixed(2)}%${change > 0 ? ' (↑)' : ' (↓)'}`,
    simulateData: (timeframe, index) => {
      // Base volatility on timeframe (higher on smaller timeframes)
      const volatilityFactor = timeframe === '1m' ? 3 :
        timeframe === '5m' ? 2.5 :
          timeframe === '15m' ? 2 :
            timeframe === '1h' ? 1.5 :
              timeframe === '4h' ? 1.2 :
                timeframe === '1d' ? 1 : 0.8

      // Generate random volume with some trending bias
      const baseline = 50 + (Math.sin(Date.now() / 10000000 + index) * 30)
      const change = (Math.random() * 30 - 15) * volatilityFactor
      const value = Math.max(0, Math.min(100, baseline + change))

      return {
        value,
        change,
        normalized: value / 100,
        color: INDICATOR_MODELS.volume.colorLogic(value),
        signal: value > 60 ? 'buy' : value < 40 ? 'sell' : 'neutral',
        signalStrength: Math.abs(value - 50) / 50,
      }
    },
  },

  ml: {
    title: 'Machine Learning',
    description: 'AI-powered prediction model based on pattern recognition and statistical analysis.',
    chartConfig: {
      type: 'line',
      options: {
        elements: {
          line: {
            tension: 0.4,
          },
        },
      },
    },
    colorLogic: (value) => {
      if (value > 75) return '#00FF00' // Strong buy - high ML confidence bullish
      if (value > 60) return '#0000FF' // Mild buy
      if (value < 25) return '#FF0000' // Strong sell - high ML confidence bearish
      if (value < 40) return '#FFA500' // Mild sell
      return '#808080' // Neutral
    },
    tooltip: (value) => `ML Prediction: ${value.toFixed(1)}% confidence`,
    simulateData: (timeframe, index) => {
      // Make ML more accurate on higher timeframes
      const accuracyFactor = timeframe === '1m' ? 0.7 :
        timeframe === '5m' ? 0.75 :
          timeframe === '15m' ? 0.8 :
            timeframe === '1h' ? 0.85 :
              timeframe === '4h' ? 0.9 :
                timeframe === '1d' ? 0.95 : 0.97

      // Generate prediction with higher certainty on higher timeframes
      const trend = Math.sin(Date.now() / 20000000 + index) * 40 + 50
      const noise = (Math.random() * 50 - 25) * (1 - accuracyFactor)
      const value = Math.max(0, Math.min(100, trend + noise))

      return {
        value,
        normalized: value / 100,
        color: INDICATOR_MODELS.ml.colorLogic(value),
        signal: value > 60 ? 'buy' : value < 40 ? 'sell' : 'neutral',
        signalStrength: Math.abs(value - 50) / 50,
        confidence: accuracyFactor * (1 + Math.abs(value - 50) / 100),
      }
    },
  },

  sentiment: {
    title: 'Sentiment Analysis',
    description: 'Analysis of market sentiment from social media, news, and trading activity.',
    chartConfig: {
      type: 'line',
      options: {
        scales: {
          y: {
            suggestedMin: -100,
            suggestedMax: 100,
          },
        },
      },
    },
    colorLogic: (value) => {
      if (value > 75) return '#00FF00' // Strong buy - very positive sentiment
      if (value > 60) return '#0000FF' // Mild buy - positive sentiment
      if (value < 25) return '#FF0000' // Strong sell - very negative sentiment
      if (value < 40) return '#FFA500' // Mild sell - negative sentiment
      return '#808080' // Neutral sentiment
    },
    tooltip: (value) => `Market Sentiment: ${value > 75 ? 'Very Positive' :
      value > 60 ? 'Positive' :
        value < 25 ? 'Very Negative' :
          value < 40 ? 'Negative' : 'Neutral'} (${value.toFixed(1)}%)`,
    simulateData: (timeframe, index) => {
      // Sentiment tends to be more volatile on shorter timeframes
      const volatilityFactor = timeframe === '1m' ? 2.5 :
        timeframe === '5m' ? 2 :
          timeframe === '15m' ? 1.7 :
            timeframe === '1h' ? 1.5 :
              timeframe === '4h' ? 1.2 :
                timeframe === '1d' ? 1 : 0.8

      // Generate sentiment with some randomness
      const baseValue = Math.sin(Date.now() / 15000000 + index * 1.5) * 40 + 50
      const noise = (Math.random() * 40 - 20) * volatilityFactor
      const value = Math.max(0, Math.min(100, baseValue + noise))

      return {
        value,
        normalized: value / 100,
        color: INDICATOR_MODELS.sentiment.colorLogic(value),
        signal: value > 60 ? 'buy' : value < 40 ? 'sell' : 'neutral',
        signalStrength: Math.abs(value - 50) / 50,
      }
    },
  },

  entropy: {
    title: 'Market Entropy',
    description: 'Measure of market randomness and chaos. Higher entropy indicates less predictable markets.',
    chartConfig: {
      type: 'line',
      options: {
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
          },
        },
        elements: {
          line: {
            backgroundColor: 'rgba(128, 0, 128, 0.2)',
          },
        },
      },
    },
    colorLogic: (value) => {
      if (value > 80) return '#FF0000' // Strong sell - very chaotic market
      if (value > 65) return '#FFA500' // Mild sell - chaotic market
      if (value < 20) return '#00FF00' // Strong buy - very orderly market
      if (value < 35) return '#0000FF' // Mild buy - orderly market
      return '#808080' // Neutral entropy
    },
    tooltip: (value) => `Market Entropy: ${value > 80 ? 'Extremely Chaotic' :
      value > 65 ? 'Chaotic' :
        value < 20 ? 'Highly Structured' :
          value < 35 ? 'Structured' : 'Average'} (${value.toFixed(1)}%)`,
    simulateData: (timeframe, index) => {
      // Entropy tends to be higher on shorter timeframes
      const entropyBias = timeframe === '1m' ? 20 :
        timeframe === '5m' ? 15 :
          timeframe === '15m' ? 10 :
            timeframe === '1h' ? 5 :
              timeframe === '4h' ? 0 :
                timeframe === '1d' ? -5 : -10

      // Generate base entropy with randomness
      const baseValue = 50 + entropyBias + Math.sin(Date.now() / 25000000 + index * 0.5) * 15
      const noise = Math.random() * 30 - 15
      const value = Math.max(0, Math.min(100, baseValue + noise))

      return {
        value,
        normalized: value / 100,
        color: INDICATOR_MODELS.entropy.colorLogic(value),
        signal: value < 35 ? 'buy' : value > 65 ? 'sell' : 'neutral',
        signalStrength: Math.abs(value - 50) / 50,
      }
    },
  },

  correlation: {
    title: 'Market Correlation',
    description: 'Measures correlation between this asset and related markets. High correlation may indicate strong trends.',
    chartConfig: {
      type: 'line',
      options: {
        scales: {
          y: {
            min: -100,
            max: 100,
          },
        },
      },
    },
    colorLogic: (value) => {
      if (value > 75) return '#00FF00' // Strong buy - high positive correlation
      if (value > 55) return '#0000FF' // Mild buy - moderate positive correlation
      if (value < 25) return '#FF0000' // Strong sell - high negative correlation
      if (value < 45) return '#FFA500' // Mild sell - moderate negative correlation
      return '#808080' // Neutral correlation
    },
    tooltip: (value) => `Market Correlation: ${value > 75 ? 'Strong Positive' :
      value > 55 ? 'Positive' :
        value < 25 ? 'Strong Negative' :
          value < 45 ? 'Negative' : 'Neutral'} (${value.toFixed(1)}%)`,
    simulateData: (timeframe, index) => {
      // Higher timeframes have more stable correlations
      const stabilityFactor = timeframe === '1m' ? 2.5 :
        timeframe === '5m' ? 2 :
          timeframe === '15m' ? 1.7 :
            timeframe === '1h' ? 1.5 :
              timeframe === '4h' ? 1.2 :
                timeframe === '1d' ? 1 : 0.8

      // Generate correlation with cyclical pattern and noise
      const baseValue = Math.cos(Date.now() / 30000000 + index * 0.7) * 40 + 50
      const noise = (Math.random() * 30 - 15) * stabilityFactor
      const value = Math.max(0, Math.min(100, baseValue + noise))

      return {
        value,
        normalized: (value - 50) / 50, // Normalized to -1 to 1 range
        color: INDICATOR_MODELS.correlation.colorLogic(value),
        signal: value > 60 ? 'buy' : value < 40 ? 'sell' : 'neutral',
        signalStrength: Math.abs(value - 50) / 50,
      }
    },
  },

  time_anomaly: {
    title: 'Time Anomaly',
    description: 'Detection of abnormal patterns in time series data that may indicate upcoming market shifts.',
    chartConfig: {
      type: 'line',
      options: {
        scales: {
          y: {
            beginAtZero: true,
          },
        },
        elements: {
          line: {
            borderColor: 'rgba(255, 99, 71, 0.8)',
            backgroundColor: 'rgba(255, 99, 71, 0.2)',
          },
        },
      },
    },
    colorLogic: (value) => {
      if (value > 80) return '#FF0000' // Strong sell - major anomaly detected
      if (value > 65) return '#FFA500' // Mild sell - moderate anomaly detected
      if (value < 20) return '#00FF00' // Strong buy - extremely normal pattern
      if (value < 35) return '#0000FF' // Mild buy - normal pattern
      return '#808080' // Neutral - no significant anomaly
    },
    tooltip: (value) => `Time Anomaly: ${value > 80 ? 'Major Anomaly' :
      value > 65 ? 'Moderate Anomaly' :
        value < 20 ? 'Extremely Normal' :
          value < 35 ? 'Normal' : 'Slight Variance'} (${!isNaN(parseFloat(value)) ? parseFloat(value).toFixed(1) : 'N/A'}%)`,
    simulateData: (timeframe, index) => {
      // Generate cyclical pattern with occasional spikes
      const cycleBase = Math.sin(Date.now() / 40000000 + index) * 20 + 30

      // Add random spikes (anomalies) with greater likelihood on shorter timeframes
      const anomalyThreshold = timeframe === '1m' ? 0.85 :
        timeframe === '5m' ? 0.9 :
          timeframe === '15m' ? 0.93 :
            timeframe === '1h' ? 0.95 :
              timeframe === '4h' ? 0.97 :
                timeframe === '1d' ? 0.98 : 0.99

      const hasAnomaly = Math.random() > anomalyThreshold
      const anomalyMagnitude = hasAnomaly ? Math.random() * 50 + 30 : 0
      const value = Math.min(100, cycleBase + anomalyMagnitude)

      return {
        value,
        normalized: value / 100,
        color: INDICATOR_MODELS.time_anomaly.colorLogic(value),
        signal: value < 35 ? 'buy' : value > 65 ? 'sell' : 'neutral',
        signalStrength: Math.abs(value - 50) / 50,
        hasAnomaly,
      }
    },
  },
}

// Initialize advanced indicators
function initializeAdvancedIndicators() {
  console.log('[IndicatorProcessors] Initializing advanced indicators')

  // Create mini charts and signal lights for all indicators when DOM is loaded
  // but don't run any heavy operations immediately
  document.addEventListener('DOMContentLoaded', () => {
    // Initialize the system when the page is loaded
    setTimeout(() => {
      try {
        // Create mini charts with a delay to improve initial loading performance
        createMiniChartsForAdvancedIndicators()

        // Update only the current timeframe initially for better performance
        updateCurrentTimeframeIndicators()

        // Schedule updates for other timeframes with delays to reduce initial lag
        setTimeout(() => updateAllAdvancedIndicators(), 3000)

        // Set up interval to update indicators less frequently (10 seconds instead of 5)
        const updateInterval = setInterval(updateAllAdvancedIndicators, 10000)

        // Store interval ID for potential cleanup
        window.indicatorUpdateInterval = updateInterval

        console.log('[IndicatorProcessors] Successfully initialized advanced indicators')
      } catch (err) {
        console.error('[IndicatorProcessors] Error initializing indicators:', err)
      }
    }, 1000)

    // Add these functions to the global window object so they can be called from elsewhere
    window.updateAllAdvancedIndicators = updateAllAdvancedIndicators
    window.updateCurrentTimeframeIndicators = updateCurrentTimeframeIndicators
    window.createMiniChartForIndicator = createMiniChartForIndicator
  })
}

// Create mini charts for all required indicators - DISABLED
function createMiniChartsForAdvancedIndicators() {
  try {
    console.log('[IndicatorProcessors] DISABLED - Enhanced charts are now primary system')
    const indicators = getAllRequiredIndicators()

    // First ensure all indicators have 7 timeframe signal lights
    ensureAllIndicatorsHaveTimeframeLights()

    // Enhanced charts handle mini chart creation now
    // Do not create Chart.js charts that override enhanced charts
  } catch (err) {
    console.error('[IndicatorProcessors] Error creating mini charts:', err)
  }
}

// Ensure each indicator has all 7 timeframe signal lights
function ensureAllIndicatorsHaveTimeframeLights() {
  try {
    console.log('[IndicatorProcessors] Setting up timeframe lights for all indicators')
    const indicators = getAllRequiredIndicators()
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

    indicators.forEach(indicator => {
      const row = document.querySelector(`tr[data-indicator="${indicator}"]`)
      if (row) {
        // Get or create signal lights cell
        let signalLightsCell = row.querySelector('.signal-lights-cell')
        if (!signalLightsCell) {
          signalLightsCell = document.createElement('td')
          signalLightsCell.className = 'signal-lights-cell'
          row.appendChild(signalLightsCell)
        }

        // Clear existing lights
        signalLightsCell.innerHTML = ''

        // Create container for all timeframe lights
        const lightsContainer = document.createElement('div')
        lightsContainer.className = 'signal-lights-container'
        signalLightsCell.appendChild(lightsContainer)

        // Create 7 signal lights - one for each timeframe
        timeframes.forEach(tf => {
          const light = document.createElement('div')
          light.className = 'signal-light'
          light.dataset.timeframe = tf
          light.dataset.indicator = indicator
          light.title = `${tf.toUpperCase()} Signal`
          light.style.backgroundColor = '#999' // Neutral color until updated
          lightsContainer.appendChild(light)
        })
      }
    })

    return true
  } catch (err) {
    console.error('[IndicatorProcessors] Error setting up timeframe lights:', err)
    return false
  }
}

// Create a mini chart for a specific indicator
function createMiniChartForIndicator(indicator) {
  if (!INDICATOR_MODELS[indicator]) {
    console.error(`[IndicatorProcessors] Unknown indicator: ${indicator}`)
    return
  }

  try {
    // Get or create the mini chart container
    let miniChartContainer = document.getElementById(`mini-chart-${indicator}`)

    // If the container doesn't exist, create it and insert into the correct row
    if (!miniChartContainer) {
      const indicatorRow = document.querySelector(`tr[data-indicator="${indicator}"]`)

      if (indicatorRow) {
        // Create mini chart container
        miniChartContainer = document.createElement('div')
        miniChartContainer.id = `mini-chart-${indicator}`
        miniChartContainer.className = 'mini-chart-container'
        miniChartContainer.style.position = 'relative'
        miniChartContainer.style.width = '150px'
        miniChartContainer.style.height = '60px'
        miniChartContainer.style.marginTop = '10px'

        // Create canvas element
        const canvas = document.createElement('canvas')
        canvas.id = `mini-chart-canvas-${indicator}`
        canvas.width = 150
        canvas.height = 60
        miniChartContainer.appendChild(canvas)

        // Add mini chart to the indicator row (in the first cell)
        const firstCell = indicatorRow.querySelector('td')
        if (firstCell) {
          firstCell.appendChild(miniChartContainer)
        }

        // Initialize the chart with empty data
        const ctx = canvas.getContext('2d')
        const chartConfig = INDICATOR_MODELS[indicator].chartConfig || { type: 'line' }

        // Default data
        const chartData = {
          labels: Array(24).fill(''),
          datasets: [{
            label: INDICATOR_MODELS[indicator].title,
            data: Array(24).fill(null),
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderWidth: 1,
            pointRadius: 0,
            pointHoverRadius: 3,
          }],
        }

        // Merge default options with indicator-specific options
        const chartOptions = {
          responsive: true,
          maintainAspectRatio: false,
          animation: false,
          plugins: {
            legend: { display: false },
            tooltip: { enabled: true },
          },
          scales: {
            x: { display: false },
            y: { display: false },
          },
          ...chartConfig.options,
        }

        // Create chart
        const chart = new Chart(ctx, {
          type: chartConfig.type || 'line',
          data: chartData,
          options: chartOptions,
        })

        // Store chart instance in window object
        window.charts = window.charts || {}
        window.charts[indicator] = chart
      } else {
        console.warn(`[IndicatorProcessors] Indicator row not found for ${indicator}`)
      }
    }
  } catch (error) {
    console.error(`[IndicatorProcessors] Error creating mini chart for ${indicator}:`, error.message)
  }
}

// Update only indicators for the current timeframe
function updateCurrentTimeframeIndicators() {
  try {
    console.log('[IndicatorProcessors] Updating indicators for current timeframe')
    const currentTimeframe = window.currentTf || '1h'
    const indicators = getAllRequiredIndicators()

    // Update all indicators but only for the current timeframe
    indicators.forEach(indicator => {
      updateIndicatorDisplay(indicator, currentTimeframe)
    })

    return true
  } catch (err) {
    console.error('[IndicatorProcessors] Error updating current timeframe indicators:', err)
    return false
  }
}

// Update all indicators based on current strategy and timeframe
function updateAllAdvancedIndicators() {
  try {
    console.log('[IndicatorProcessors] Updating all advanced indicators')
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
    const indicators = getAllRequiredIndicators()

    // First update the current timeframe for immediate feedback
    updateCurrentTimeframeIndicators()

    // Then update other timeframes one by one with small delays
    const currentTf = window.currentTf || '1h'
    let delay = 0

    timeframes.forEach(timeframe => {
      // Skip current timeframe as it was already updated
      if (timeframe === currentTf) return

      // Add 50ms delay between each timeframe update
      delay += 50
      setTimeout(() => {
        indicators.forEach(indicator => {
          updateIndicatorDisplay(indicator, timeframe)
        })
      }, delay)
    })

    return true
  } catch (err) {
    console.error('[IndicatorProcessors] Error updating indicators:', err.message || err, err.stack || 'No stack trace')
    return false
  }
}

// WebSocket connection manager with retry and error handling
let wsReconnectAttempts = 0
const MAX_RECONNECT_ATTEMPTS = 10
const RECONNECT_INTERVAL = 5000 // 5 seconds
const MESSAGE_QUEUE = []
let isProcessingQueue = false

// Process messages from the queue
function processMessageQueue() {
  if (isProcessingQueue || MESSAGE_QUEUE.length === 0) return

  isProcessingQueue = true

  try {
    while (MESSAGE_QUEUE.length > 0) {
      const { event, timestamp } = MESSAGE_QUEUE.shift()
      const now = Date.now()

      // Skip processing if message is older than 10 seconds
      if (now - timestamp > 10000) {
        console.log('[IndicatorProcessors] Skipping stale message from queue')
        continue
      }

      try {
        const data = JSON.parse(event.data)

        // Handle different message types
        switch (data.type) {
          case 'indicators':
            console.log('[IndicatorProcessors] Processing queued indicator updates')
            processIndicatorUpdates(data.data)
            break

          case 'error':
            console.error('[IndicatorProcessors] Queued server error:', data.message)
            break
        }
      } catch (error) {
        console.error('[IndicatorProcessors] Error processing queued message:', error)
      }
    }
  } finally {
    isProcessingQueue = false
  }
}

// Add message to queue for processing
function queueMessage(event) {
  MESSAGE_QUEUE.push({ event, timestamp: Date.now() })
  if (!isProcessingQueue) {
    // Process queue on next tick to avoid blocking
    setTimeout(processMessageQueue, 0)
  }
}

// WebSocket connection manager with retry and error handling
function ensureWebSocketConnection() {
  try {
    // Check if WebSocket is already connecting or open
    if (window.ws) {
      const state = window.ws.readyState
      if (state === WebSocket.CONNECTING) {
        console.log('[IndicatorProcessors] WebSocket already connecting...')
        return
      }
      if (state === WebSocket.OPEN) {
        console.log('[IndicatorProcessors] WebSocket already connected')
        return
      }

      // Clean up existing connection
      try {
        window.ws.onopen = null
        window.ws.onclose = null
        window.ws.onerror = null
        window.ws.onmessage = null
        window.ws.close()
      } catch (e) {
        console.warn('[IndicatorProcessors] Error cleaning up existing WebSocket:', e)
      }
    }

    // Reset reconnect attempts if we have a successful connection
    if (wsReconnectAttempts > 0) {
      console.log(`[IndicatorProcessors] Reconnection attempt ${wsReconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS}`)
    }

    // Check max reconnect attempts
    if (wsReconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      console.error('[IndicatorProcessors] Max reconnection attempts reached. Please refresh the page to try again.')
      return
    }

    console.log('[IndicatorProcessors] Establishing new WebSocket connection...')

    // Create new WebSocket connection with cache-busting query parameter
    const pair = window.currentPair || 'xbtusdt'
    const wsUrl = `ws://${window.location.hostname}:8080/ws?pair=${pair}&_=${Date.now()}`
    window.ws = new WebSocket(wsUrl)

    // Set up event handlers with proper binding
    window.ws.onopen = function () {
      console.log('[IndicatorProcessors] WebSocket connection established')
      wsReconnectAttempts = 0 // Reset reconnect counter on successful connection

      // Clear any existing reconnect timeout
      if (window.wsReconnectTimeout) {
        clearTimeout(window.wsReconnectTimeout)
        window.wsReconnectTimeout = null
      }

      // Subscribe to indicator updates
      try {
        const subscription = {
          type: 'subscribe',
          pairs: [pair],
          timeframes: ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],
        }
        window.ws.send(JSON.stringify(subscription))
        console.log('[IndicatorProcessors] Sent subscription:', subscription)
      } catch (e) {
        console.error('[IndicatorProcessors] Error sending subscription:', e)
      }

      // Process any queued messages
      if (MESSAGE_QUEUE.length > 0) {
        console.log(`[IndicatorProcessors] Processing ${MESSAGE_QUEUE.length} queued messages`)
        processMessageQueue()
      }

      // Trigger initial indicator update with a small delay
      setTimeout(updateCurrentTimeframeIndicators, 1000)
    }

    // Handle incoming WebSocket messages
    window.ws.onmessage = function (event) {
      try {
        const data = JSON.parse(event.data)

        // Handle different message types
        switch (data.type) {
          case 'indicators':
            console.log('[IndicatorProcessors] Received indicator updates')
            processIndicatorUpdates(data.data)
            break

          case 'error':
            console.error('[IndicatorProcessors] Server error:', data.message)
            break

          case 'pong':
            // Handle pong response to keepalive
            break

          case 'connection_established':
            console.log('[IndicatorProcessors] Connection confirmed by server:', data.message)
            break

          default:
            console.log('[IndicatorProcessors] Received message:', data)
        }
      } catch (error) {
        console.error('[IndicatorProcessors] Error processing WebSocket message:', error, event.data)
      }
    }

    window.ws.onerror = function (error) {
      console.error('[IndicatorProcessors] WebSocket error:', error)
      // Close the connection to trigger onclose handler
      if (window.ws) {
        window.ws.close()
      }
    }

    window.ws.onclose = function (event) {
      console.log(`[IndicatorProcessors] WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason || 'No reason provided'}`)

      // Clean up
      window.ws.onopen = null
      window.ws.onclose = null
      window.ws.onerror = null
      window.ws.onmessage = null

      // Don't spam reconnect attempts
      if (!window.wsReconnectTimeout) {
        wsReconnectAttempts++
        const delay = Math.min(RECONNECT_INTERVAL * Math.pow(1.5, wsReconnectAttempts - 1), 30000) // Cap at 30s
        console.log(`[IndicatorProcessors] Attempting to reconnect in ${delay}ms...`)

        window.wsReconnectTimeout = setTimeout(() => {
          window.wsReconnectTimeout = null
          ensureWebSocketConnection()
        }, delay)
      }
    }
  } catch (error) {
    console.error('[IndicatorProcessors] Error in WebSocket connection:', error)

    // Schedule reconnection attempt
    if (!window.wsReconnectTimeout) {
      wsReconnectAttempts++
      const delay = Math.min(RECONNECT_INTERVAL * Math.pow(1.5, wsReconnectAttempts - 1), 30000)
      console.log(`[IndicatorProcessors] Error occurred, will retry in ${delay}ms...`)

      window.wsReconnectTimeout = setTimeout(() => {
        window.wsReconnectTimeout = null
        ensureWebSocketConnection()
      }, delay)
    }

    return false
  }
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe, index) {
  try {
    // Generate indicator data
    const indicatorData = INDICATOR_MODELS[indicator].simulateData(timeframe, index)

    // Update signal light
    updateSignalLight(indicator, timeframe, indicatorData)

    // Update mini chart if it's the current timeframe
    if (timeframe === window.currentTf) {
      updateMiniChart(indicator, indicatorData)
    }
  } catch (error) {
    console.error(`[IndicatorProcessors] Error updating ${indicator} for ${timeframe}:`, error.message)
  }
}

// Process indicator updates from WebSocket
function processIndicatorUpdates(indicatorsData) {
  if (!indicatorsData || typeof indicatorsData !== 'object') {
    console.error('[IndicatorProcessors] Invalid indicators data:', indicatorsData)
    return
  }

  // Process each indicator update
  Object.entries(indicatorsData).forEach(([indicator, timeframes]) => {
    if (!timeframes || typeof timeframes !== 'object') return

    Object.entries(timeframes).forEach(([timeframe, indicatorData]) => {
      try {
        // Update the signal light
        updateSignalLight(indicator, timeframe, indicatorData)

        // Update the enhanced mini chart if it exists
        if (window.enhancedMiniCharts && window.enhancedMiniCharts.updateEnhancedMiniChart) {
          window.enhancedMiniCharts.updateEnhancedMiniChart(indicator, timeframe, indicatorData)
        }
      } catch (error) {
        console.error(`[IndicatorProcessors] Error updating ${indicator} for ${timeframe}:`, error)
      }
    })
  })
}

// Update signal light for an indicator
function updateSignalLight(indicator, timeframe, data) {
  try {
    // Validate parameters to prevent 'indicator is not defined' errors
    if (indicator === undefined || indicator === null) {
      console.error('[IndicatorProcessors] Error: indicator is not defined');
      return;
    }
    
    if (typeof timeframe !== 'string' || !timeframe) {
      console.error('[IndicatorProcessors] Error: invalid timeframe for indicator', indicator);
      return;
    }
    console.log(`[IndicatorProcessors] Updating signal light for ${indicator} (${timeframe}):`, data)

    // Find the signal element
    const signalElement = document.getElementById(`${indicator}-${timeframe}-signal`)
    if (!signalElement) {
      console.warn(`[IndicatorProcessors] Signal element not found for ${indicator}-${timeframe}-signal`)
      return
    }

    // Clear existing classes and styles
    signalElement.className = 'signal-circle'
    signalElement.style.removeProperty('background-color')
    signalElement.style.removeProperty('animation')

    // Default values
    let signalClass = 'neutral'
    let signalStrength = 0.5
    let tooltip = `${String(indicator).toUpperCase()} (${timeframe})`
    let value = null

    // Process the data to get signal information
    if (data) {
      // Handle different data formats
      if (data.signal) {
        signalClass = data.signal.toLowerCase()
      } else if (data.value !== undefined) {
        // Determine signal class based on value if no explicit signal
        value = parseFloat(data.value)
        if (!isNaN(value)) {
          if (value > 60) signalClass = 'buy'
          else if (value < 40) signalClass = 'sell'
          else signalClass = 'neutral'
        }
      }

      // Calculate signal strength (0-1)
      if (data.strength !== undefined) {
        signalStrength = Math.min(1, Math.max(0, parseFloat(data.strength) || 0.5))
      } else if (value !== null) {
        signalStrength = Math.abs((value - 50) / 50)
      }

      // Build tooltip
      if (data.value !== undefined && data.value !== null) {
        tooltip += `\nValue: ${parseFloat(data.value).toFixed(2)}`
      }

      if (data.change !== undefined) {
        const change = parseFloat(data.change)
        tooltip += `\nChange: ${change >= 0 ? '+' : ''}${change.toFixed(2)}`
      }

      tooltip += `\nSignal: ${signalClass} (${Math.round(signalStrength * 100)}% strength)`

      if (data.timestamp) {
        const timestamp = new Date(data.timestamp)
        tooltip += `\nUpdated: ${timestamp.toLocaleTimeString()}`
      }
    } else {
      tooltip += '\nNo data available'
      signalClass = 'no-data'
    }

    // Set the signal class and tooltip
    signalElement.classList.add(signalClass)
    signalElement.title = tooltip

    // Set the appropriate color based on signal and strength
    const color = getSignalColor(signalClass, signalStrength)
    signalElement.style.backgroundColor = color

    // Add a subtle pulse animation to show the update
    signalElement.style.animation = 'pulse 0.5s'
    const animationEndHandler = () => {
      signalElement.style.animation = ''
      signalElement.removeEventListener('animationend', animationEndHandler)
    }
    signalElement.addEventListener('animationend', animationEndHandler)
  } catch (error) {
    console.error(`[IndicatorProcessors] Error in updateSignalLight for ${indicator} (${timeframe}):`, error)
  }
}

// Update mini chart for an indicator - DISABLED
function updateMiniChart(indicator, data) {
  // DISABLED - Enhanced charts handle updates now
  return;
}

// Create mini charts for all required indicators - DISABLED (duplicate function)
function createMiniChartsForAdvancedIndicators() {
  // DISABLED - Enhanced charts handle creation now
  return;
}

// Initialize on script load
initializeAdvancedIndicators()

// Export functions for external use
window.INDICATOR_MODELS = INDICATOR_MODELS
window.updateAllAdvancedIndicators = updateAllAdvancedIndicators
window.createMiniChartForIndicator = createMiniChartForIndicator
