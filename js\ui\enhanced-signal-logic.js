/**
 * Enhanced Signal Logic System for StarCrypt
 * Provides comprehensive signal logic wiring, convergence analysis, and enhanced visual effects
 */

class EnhancedSignalLogic {
  constructor() {
    this.isInitialized = false;
    this.currentLogicType = 'standard';
    this.convergenceSettings = {
      minConvergence: 3,
      strongSignalWeight: 2,
      mildSignalWeight: 1,
      priority: 'balanced',
      convergenceThreshold: 0.7,
      divergenceThreshold: 0.3
    };
    
    this.lightLogicSettings = {
      style: 'vibeflow',
      intensity: 3,
      enableDotMatrix: true,
      enableChevrons: true,
      enablePulse: true,
      enableGlow: true
    };
    
    this.signalState = new Map();
    this.convergenceHistory = [];
    this.lastUpdate = 0;
    this.updateInterval = 100; // ms
    
    this.init();
  }
  
  init() {
    this.loadSettings();
    this.setupEventListeners();
    this.initializeSignalWiring();
    this.isInitialized = true;
    console.log('🔌 Enhanced Signal Logic System initialized');
  }
  
  loadSettings() {
    // Load convergence settings
    const savedConvergence = localStorage.getItem('enhancedConvergenceSettings');
    if (savedConvergence) {
      try {
        Object.assign(this.convergenceSettings, JSON.parse(savedConvergence));
      } catch (e) {
        console.warn('Failed to load convergence settings');
      }
    }
    
    // Load light logic settings
    const savedLightLogic = localStorage.getItem('enhancedLightLogicSettings');
    if (savedLightLogic) {
      try {
        Object.assign(this.lightLogicSettings, JSON.parse(savedLightLogic));
      } catch (e) {
        console.warn('Failed to load light logic settings');
      }
    }
  }
  
  saveSettings() {
    localStorage.setItem('enhancedConvergenceSettings', JSON.stringify(this.convergenceSettings));
    localStorage.setItem('enhancedLightLogicSettings', JSON.stringify(this.lightLogicSettings));
  }
  
  setupEventListeners() {
    // Listen for signal updates
    document.addEventListener('signalUpdate', (e) => {
      this.processSignalUpdate(e.detail);
    });
    
    // Listen for strategy changes
    document.addEventListener('strategyChanged', (e) => {
      this.handleStrategyChange(e.detail);
    });
    
    // Listen for threshold changes
    document.addEventListener('thresholdChanged', (e) => {
      this.handleThresholdChange(e.detail);
    });
  }
  
  initializeSignalWiring() {
    // Connect to existing signal system
    if (window.signalSystem) {
      window.signalSystem.addEventListener('signalUpdate', (data) => {
        this.processSignalUpdate(data);
      });
    }
    
    // Connect to WebSocket for real-time updates
    if (window.ws) {
      const originalOnMessage = window.ws.onmessage;
      window.ws.onmessage = (event) => {
        if (originalOnMessage) originalOnMessage(event);
        this.handleWebSocketMessage(event);
      };
    }
  }
  
  processSignalUpdate(data) {
    if (!data || !data.indicator || !data.timeframe) return;
    
    const key = `${data.indicator}:${data.timeframe}`;
    const now = Date.now();
    
    // Store signal state
    this.signalState.set(key, {
      ...data,
      timestamp: now,
      processed: false
    });
    
    // Throttle processing
    if (now - this.lastUpdate > this.updateInterval) {
      this.processSignalLogic();
      this.lastUpdate = now;
    }
  }
  
  processSignalLogic() {
    const unprocessedSignals = Array.from(this.signalState.entries())
      .filter(([key, data]) => !data.processed);
    
    if (unprocessedSignals.length === 0) return;
    
    // Analyze convergence
    const convergenceAnalysis = this.analyzeConvergence();
    
    // Apply signal logic
    this.applySignalLogic(convergenceAnalysis);
    
    // Update visual effects
    this.updateVisualEffects(convergenceAnalysis);
    
    // Mark signals as processed
    unprocessedSignals.forEach(([key]) => {
      const signal = this.signalState.get(key);
      if (signal) {
        signal.processed = true;
        this.signalState.set(key, signal);
      }
    });
    
    // Update convergence history
    this.updateConvergenceHistory(convergenceAnalysis);

    // Apply enhanced signal logic
    this.enhanceSignalLights(convergenceAnalysis);
  }

  enhanceSignalLights(analysis) {
    const circles = document.querySelectorAll('.signal-circle');

    circles.forEach(circle => {
      const indicator = circle.getAttribute('data-ind') || circle.getAttribute('data-indicator');
      const timeframe = circle.getAttribute('data-tf') || circle.getAttribute('data-timeframe');

      if (indicator && timeframe) {
        // Apply enhanced signal logic
        this.applyEnhancedSignalToCircle(circle, indicator, timeframe, analysis);

        // Add convergence effects
        this.addConvergenceEffects(circle, analysis);

        // Update chevron indicators
        if (this.lightLogicSettings.enableChevrons) {
          const chevron = circle.querySelector('.signal-chevron');
          if (chevron) {
            this.updateChevronDirection(chevron, circle);
          }
        }
      }
    });
  }

  applyEnhancedSignalToCircle(circle, indicator, timeframe, analysis) {
    // Get current indicator data
    const data = window.indicatorsData?.[timeframe]?.[indicator];
    if (!data) return;

    // Calculate enhanced signal strength
    const signalStrength = this.calculateEnhancedSignalStrength(data, indicator, analysis);

    // Apply signal class based on enhanced logic
    const signalClass = this.determineSignalClass(signalStrength, analysis);

    // Update circle appearance
    this.updateCircleAppearance(circle, signalClass, signalStrength);

    // Add pulsing effect for strong signals
    if (signalStrength.confidence > 0.8) {
      circle.classList.add('pulse-strong');
    } else if (signalStrength.confidence > 0.6) {
      circle.classList.add('pulse-mild');
    } else {
      circle.classList.remove('pulse-strong', 'pulse-mild');
    }
  }

  calculateEnhancedSignalStrength(data, indicator, analysis) {
    let baseStrength = 0.5;
    let confidence = 0.5;
    let direction = 'neutral';

    // Analyze indicator value
    if (data.value !== undefined) {
      const value = typeof data.value === 'object' ?
        (data.value.value || data.value.macd || 0) : data.value;

      // Get thresholds for this indicator
      const thresholds = window.thresholds?.[indicator] || this.getDefaultThresholds(indicator);

      // Calculate signal strength based on thresholds
      if (value <= thresholds.green) {
        baseStrength = 0.9;
        direction = 'strong-buy';
        confidence = 0.85;
      } else if (value <= thresholds.blue) {
        baseStrength = 0.7;
        direction = 'mild-buy';
        confidence = 0.65;
      } else if (value >= thresholds.red) {
        baseStrength = 0.1;
        direction = 'strong-sell';
        confidence = 0.85;
      } else if (value >= thresholds.orange) {
        baseStrength = 0.3;
        direction = 'mild-sell';
        confidence = 0.65;
      } else {
        baseStrength = 0.5;
        direction = 'neutral';
        confidence = 0.4;
      }
    }

    // Apply convergence boost
    if (analysis.convergenceStrength > 0.7) {
      confidence *= 1.2; // Boost confidence during convergence
    }

    // Apply signal logic settings
    const logicMultiplier = this.getLogicMultiplier();
    confidence *= logicMultiplier;

    return {
      strength: baseStrength,
      confidence: Math.min(confidence, 1.0),
      direction: direction,
      value: data.value
    };
  }

  determineSignalClass(signalStrength, analysis) {
    const { direction, confidence } = signalStrength;

    // Enhanced signal classification
    if (confidence > 0.8) {
      return direction === 'strong-buy' ? 'degen-buy' :
             direction === 'mild-buy' ? 'mild-buy' :
             direction === 'strong-sell' ? 'degen-sell' :
             direction === 'mild-sell' ? 'mild-sell' : 'neutral';
    } else if (confidence > 0.6) {
      return direction.includes('buy') ? 'mild-buy' :
             direction.includes('sell') ? 'mild-sell' : 'neutral';
    } else {
      return 'neutral';
    }
  }

  updateCircleAppearance(circle, signalClass, signalStrength) {
    // Remove existing signal classes
    circle.classList.remove('degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell');

    // Add new signal class
    circle.classList.add(signalClass);

    // Set background color based on signal
    const colors = {
      'degen-buy': '#00FF00',
      'mild-buy': '#0080FF',
      'neutral': '#808080',
      'mild-sell': '#FFA500',
      'degen-sell': '#FF0000'
    };

    circle.style.backgroundColor = colors[signalClass] || '#808080';

    // Add glow effect based on confidence
    const glowIntensity = signalStrength.confidence;
    circle.style.boxShadow = `0 0 ${glowIntensity * 20}px ${colors[signalClass]}`;

    // Update opacity based on confidence
    circle.style.opacity = 0.7 + (signalStrength.confidence * 0.3);
  }

  getLogicMultiplier() {
    switch (this.lightLogicSettings.style) {
      case 'conservative': return 0.8;
      case 'wallstreet': return 0.9;
      case 'vibeflow': return 1.0;
      case 'cosmic': return 1.1;
      case 'chaos': return 1.2;
      default: return 1.0;
    }
  }

  getDefaultThresholds(indicator) {
    const defaults = {
      rsi: { green: 30, blue: 40, orange: 60, red: 70 },
      macd: { green: -0.5, blue: -0.2, orange: 0.2, red: 0.5 },
      bollingerBands: { green: 0.2, blue: 0.4, orange: 0.6, red: 0.8 },
      ema: { green: 20, blue: 40, orange: 60, red: 80 },
      sma: { green: 20, blue: 40, orange: 60, red: 80 },
      atr: { green: 20, blue: 40, orange: 60, red: 80 },
      volume: { green: 20, blue: 40, orange: 60, red: 80 },
      stochastic: { green: 20, blue: 40, orange: 60, red: 80 },
      adx: { green: 25, blue: 35, orange: 50, red: 65 },
      cci: { green: -100, blue: -50, orange: 50, red: 100 }
    };

    return defaults[indicator] || { green: 20, blue: 40, orange: 60, red: 80 };
  }
  
  analyzeConvergence() {
    const signals = Array.from(this.signalState.values());
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    const analysis = {
      overall: { buy: 0, sell: 0, neutral: 0 },
      byTimeframe: {},
      convergenceStrength: 0,
      divergenceStrength: 0,
      dominantSignal: 'neutral',
      confidence: 0
    };
    
    // Analyze by timeframe
    timeframes.forEach(tf => {
      const tfSignals = signals.filter(s => s.timeframe === tf);
      const tfAnalysis = this.analyzeTimeframeSignals(tfSignals);
      analysis.byTimeframe[tf] = tfAnalysis;
      
      // Add to overall analysis with timeframe weighting
      const weight = this.getTimeframeWeight(tf);
      analysis.overall.buy += tfAnalysis.buy * weight;
      analysis.overall.sell += tfAnalysis.sell * weight;
      analysis.overall.neutral += tfAnalysis.neutral * weight;
    });
    
    // Calculate convergence strength
    const totalSignals = analysis.overall.buy + analysis.overall.sell + analysis.overall.neutral;
    if (totalSignals > 0) {
      const buyRatio = analysis.overall.buy / totalSignals;
      const sellRatio = analysis.overall.sell / totalSignals;
      
      if (buyRatio > this.convergenceSettings.convergenceThreshold) {
        analysis.convergenceStrength = buyRatio;
        analysis.dominantSignal = 'buy';
        analysis.confidence = buyRatio;
      } else if (sellRatio > this.convergenceSettings.convergenceThreshold) {
        analysis.convergenceStrength = sellRatio;
        analysis.dominantSignal = 'sell';
        analysis.confidence = sellRatio;
      } else {
        analysis.divergenceStrength = Math.abs(buyRatio - sellRatio);
        analysis.dominantSignal = 'neutral';
        analysis.confidence = 1 - analysis.divergenceStrength;
      }
    }
    
    return analysis;
  }
  
  analyzeTimeframeSignals(signals) {
    const analysis = { buy: 0, sell: 0, neutral: 0, strength: 0 };
    
    signals.forEach(signal => {
      const strength = signal.strength || 0.5;
      const weight = this.getSignalWeight(signal.signal, strength);
      
      switch (signal.signal) {
        case 'buy':
        case 'strong-buy':
          analysis.buy += weight;
          break;
        case 'sell':
        case 'strong-sell':
          analysis.sell += weight;
          break;
        default:
          analysis.neutral += weight;
      }
    });
    
    analysis.strength = Math.max(analysis.buy, analysis.sell, analysis.neutral);
    return analysis;
  }
  
  getTimeframeWeight(timeframe) {
    const weights = {
      '1m': 0.5,
      '5m': 0.7,
      '15m': 0.9,
      '1h': 1.2,
      '4h': 1.5,
      '1d': 2.0,
      '1w': 1.8
    };
    return weights[timeframe] || 1.0;
  }
  
  getSignalWeight(signal, strength) {
    const baseWeight = strength || 0.5;
    
    switch (signal) {
      case 'strong-buy':
      case 'strong-sell':
        return baseWeight * this.convergenceSettings.strongSignalWeight;
      case 'mild-buy':
      case 'mild-sell':
        return baseWeight * this.convergenceSettings.mildSignalWeight;
      default:
        return baseWeight;
    }
  }
  
  applySignalLogic(convergenceAnalysis) {
    // Update global signal state
    if (window.signalSystem) {
      window.signalSystem.convergenceAnalysis = convergenceAnalysis;
    }
    
    // Apply convergence-based logic
    this.applyConvergenceLogic(convergenceAnalysis);
    
    // Update signal lights with enhanced logic
    this.updateSignalLights(convergenceAnalysis);
    
    // Trigger helper step updates
    this.updateHelperSteps(convergenceAnalysis);
  }
  
  applyConvergenceLogic(analysis) {
    const { dominantSignal, confidence, convergenceStrength } = analysis;
    
    // Apply priority-based logic
    switch (this.convergenceSettings.priority) {
      case 'strong':
        this.prioritizeStrongSignals(analysis);
        break;
      case 'mild':
        this.prioritizeMildSignals(analysis);
        break;
      default:
        this.applyBalancedLogic(analysis);
    }
    
    // Apply minimum convergence filter
    if (convergenceStrength < this.convergenceSettings.minConvergence / 10) {
      this.filterWeakSignals();
    }
  }
  
  prioritizeStrongSignals(analysis) {
    const circles = document.querySelectorAll('.signal-circle');
    circles.forEach(circle => {
      const signal = circle.dataset.signal;
      if (signal && (signal.includes('strong') || analysis.confidence > 0.8)) {
        circle.classList.add('priority-signal');
        circle.style.zIndex = '15';
      } else {
        circle.classList.remove('priority-signal');
        circle.style.zIndex = '10';
      }
    });
  }
  
  prioritizeMildSignals(analysis) {
    const circles = document.querySelectorAll('.signal-circle');
    circles.forEach(circle => {
      const signal = circle.dataset.signal;
      if (signal && signal.includes('mild')) {
        circle.classList.add('priority-signal');
      } else {
        circle.classList.remove('priority-signal');
      }
    });
  }
  
  applyBalancedLogic(analysis) {
    const circles = document.querySelectorAll('.signal-circle');
    circles.forEach(circle => {
      circle.classList.remove('priority-signal');
      circle.style.zIndex = '10';
    });
  }
  
  filterWeakSignals() {
    const circles = document.querySelectorAll('.signal-circle');
    circles.forEach(circle => {
      const strength = parseFloat(circle.dataset.strength) || 0.5;
      if (strength < 0.3) {
        circle.classList.add('weak-signal');
        circle.style.opacity = '0.5';
      } else {
        circle.classList.remove('weak-signal');
        circle.style.opacity = '1';
      }
    });
  }
  
  updateSignalLights(analysis) {
    // Apply enhanced light logic
    this.applyLightLogic();
    
    // Update convergence indicators
    this.updateConvergenceIndicators(analysis);
    
    // Apply visual enhancements
    this.applyVisualEnhancements(analysis);
  }
  
  applyLightLogic() {
    const circles = document.querySelectorAll('.signal-circle');
    
    // Remove existing light logic classes
    circles.forEach(circle => {
      circle.classList.remove(
        'light-logic-conservative', 'light-logic-wallstreet', 
        'light-logic-vibeflow', 'light-logic-cosmic', 'light-logic-chaos'
      );
      
      // Apply current light logic style
      circle.classList.add(`light-logic-${this.lightLogicSettings.style}`);
      
      // Apply intensity
      circle.style.setProperty('--intensity', this.lightLogicSettings.intensity);
      
      // Apply effects
      if (this.lightLogicSettings.enablePulse) {
        circle.classList.add('enable-pulse');
      }
      
      if (this.lightLogicSettings.enableGlow) {
        circle.classList.add('enable-glow');
      }
      
      if (this.lightLogicSettings.enableChevrons) {
        circle.classList.add('enable-chevrons');
        this.addChevronIndicator(circle);
      }
    });
  }

  updateConvergenceIndicators(analysis) {
    // Update convergence strength indicator
    const convergenceIndicator = document.querySelector('.convergence-strength');
    if (convergenceIndicator) {
      const strength = Math.round(analysis.convergenceStrength * 100);
      convergenceIndicator.textContent = `${strength}%`;
      convergenceIndicator.className = `convergence-strength ${analysis.dominantSignal}`;
    }

    // Update confidence indicator
    const confidenceIndicator = document.querySelector('.confidence-level');
    if (confidenceIndicator) {
      const confidence = Math.round(analysis.confidence * 100);
      confidenceIndicator.textContent = `${confidence}%`;
      confidenceIndicator.style.setProperty('--confidence', analysis.confidence);
    }
  }

  applyVisualEnhancements(analysis) {
    // Apply convergence-based visual effects
    const container = document.querySelector('.oracle-matrix');
    if (container) {
      container.classList.remove('convergence-buy', 'convergence-sell', 'convergence-neutral');
      container.classList.add(`convergence-${analysis.dominantSignal}`);

      // Apply intensity based on confidence
      container.style.setProperty('--convergence-intensity', analysis.confidence);
    }

    // Apply dot matrix effects
    if (this.lightLogicSettings.enableDotMatrix) {
      this.updateDotMatrix(analysis);
    }
  }

  updateDotMatrix(analysis) {
    // Create dot matrix if it doesn't exist
    let dotMatrix = document.querySelector('.dot-matrix');
    if (!dotMatrix) {
      dotMatrix = this.createDotMatrix();
    }

    // Update dot colors based on convergence
    const dots = dotMatrix.querySelectorAll('.matrix-dot');
    dots.forEach((dot, index) => {
      const intensity = analysis.confidence * (1 - (index % 10) / 10);
      dot.style.setProperty('--dot-intensity', intensity);
      dot.classList.remove('dot-buy', 'dot-sell', 'dot-neutral');
      dot.classList.add(`dot-${analysis.dominantSignal}`);

      // Add pulsing effect for strong convergence
      if (analysis.confidence > 0.8) {
        dot.classList.add('pulse-strong');
      } else if (analysis.confidence > 0.6) {
        dot.classList.add('pulse-mild');
      } else {
        dot.classList.remove('pulse-strong', 'pulse-mild');
      }
    });

    // Apply pattern based on signal type
    this.applyDotMatrixPattern(dotMatrix, analysis);
  }

  createDotMatrix() {
    const container = document.querySelector('.oracle-matrix') || document.querySelector('.momentum-indicators');
    if (!container) return null;

    const dotMatrix = document.createElement('div');
    dotMatrix.className = 'dot-matrix';
    dotMatrix.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      grid-template-rows: repeat(8, 1fr);
      gap: 2px;
      padding: 5px;
    `;

    // Create 64 dots (8x8 matrix)
    for (let i = 0; i < 64; i++) {
      const dot = document.createElement('div');
      dot.className = 'matrix-dot';
      dot.style.cssText = `
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: rgba(0, 255, 255, 0.1);
        transition: all 0.3s ease;
        box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
      `;
      dotMatrix.appendChild(dot);
    }

    container.appendChild(dotMatrix);
    return dotMatrix;
  }

  applyDotMatrixPattern(dotMatrix, analysis) {
    const dots = dotMatrix.querySelectorAll('.matrix-dot');
    const pattern = this.getPatternForSignal(analysis.dominantSignal, analysis.confidence);

    dots.forEach((dot, index) => {
      const row = Math.floor(index / 8);
      const col = index % 8;
      const patternValue = pattern[row] ? pattern[row][col] : 0;

      if (patternValue > 0) {
        dot.style.opacity = patternValue * analysis.confidence;
        dot.style.transform = `scale(${0.5 + patternValue * 0.5})`;
      } else {
        dot.style.opacity = 0.1;
        dot.style.transform = 'scale(0.3)';
      }
    });
  }

  getPatternForSignal(signal, confidence) {
    // Define 8x8 patterns for different signals
    const patterns = {
      buy: [
        [0,0,1,1,1,1,0,0],
        [0,1,1,1,1,1,1,0],
        [1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1],
        [0,1,1,1,1,1,1,0],
        [0,0,1,1,1,1,0,0]
      ],
      sell: [
        [1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1],
        [0,1,1,1,1,1,1,0],
        [0,0,1,1,1,1,0,0],
        [0,0,1,1,1,1,0,0],
        [0,1,1,1,1,1,1,0],
        [1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1]
      ],
      neutral: [
        [0,0,0,1,1,0,0,0],
        [0,0,1,1,1,1,0,0],
        [0,1,1,1,1,1,1,0],
        [1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1],
        [0,1,1,1,1,1,1,0],
        [0,0,1,1,1,1,0,0],
        [0,0,0,1,1,0,0,0]
      ]
    };

    return patterns[signal] || patterns.neutral;
  }

  updateVisualEffects(analysis) {
    // Apply background effects
    this.updateBackgroundEffects(analysis);

    // Apply helper step highlights
    this.updateHelperHighlights(analysis);

    // Apply convergence animations
    this.updateConvergenceAnimations(analysis);
  }

  updateBackgroundEffects(analysis) {
    const body = document.body;

    // Remove existing convergence classes
    body.classList.remove('bg-convergence-buy', 'bg-convergence-sell', 'bg-convergence-neutral');

    // Apply new convergence background
    if (analysis.confidence > 0.7) {
      body.classList.add(`bg-convergence-${analysis.dominantSignal}`);
      body.style.setProperty('--bg-intensity', analysis.confidence);
    }
  }

  updateHelperHighlights(analysis) {
    const helperSteps = document.querySelectorAll('.helper-step');
    helperSteps.forEach((step, index) => {
      if (analysis.confidence > 0.6) {
        step.classList.add('highlight-active');
        step.style.setProperty('--highlight-delay', `${index * 0.1}s`);
      } else {
        step.classList.remove('highlight-active');
      }
    });
  }

  updateConvergenceAnimations(analysis) {
    // Apply convergence pulse animation
    if (analysis.convergenceStrength > 0.8) {
      document.body.classList.add('convergence-pulse');
    } else {
      document.body.classList.remove('convergence-pulse');
    }

    // Apply divergence warning animation
    if (analysis.divergenceStrength > 0.6) {
      document.body.classList.add('divergence-warning');
    } else {
      document.body.classList.remove('divergence-warning');
    }
  }

  updateHelperSteps(analysis) {
    const helperContainer = document.querySelector('.helper-container');
    if (!helperContainer) return;

    // Update helper text based on convergence
    const helperText = this.generateHelperText(analysis);
    const helperTextElement = helperContainer.querySelector('.helper-text');
    if (helperTextElement) {
      helperTextElement.innerHTML = helperText;
    }

    // Update helper steps
    this.updateHelperStepVisuals(analysis);
  }

  generateHelperText(analysis) {
    const { dominantSignal, confidence, convergenceStrength } = analysis;

    if (confidence > 0.8) {
      switch (dominantSignal) {
        case 'buy':
          return `<strong>🚀 STRONG BUY CONVERGENCE</strong><br>
                  Confidence: ${Math.round(confidence * 100)}%<br>
                  Consider entering long positions with proper risk management.`;
        case 'sell':
          return `<strong>📉 STRONG SELL CONVERGENCE</strong><br>
                  Confidence: ${Math.round(confidence * 100)}%<br>
                  Consider entering short positions or taking profits.`;
      }
    } else if (confidence > 0.6) {
      return `<strong>⚡ MODERATE ${dominantSignal.toUpperCase()} SIGNAL</strong><br>
              Confidence: ${Math.round(confidence * 100)}%<br>
              Monitor for confirmation before taking action.`;
    } else {
      return `<strong>⚖️ MIXED SIGNALS</strong><br>
              Confidence: ${Math.round(confidence * 100)}%<br>
              Wait for clearer convergence before acting.`;
    }
  }

  updateHelperStepVisuals(analysis) {
    const steps = document.querySelectorAll('.helper-step');
    steps.forEach((step, index) => {
      // Apply convergence-based styling
      step.classList.remove('step-buy', 'step-sell', 'step-neutral');
      step.classList.add(`step-${analysis.dominantSignal}`);

      // Apply confidence-based opacity
      step.style.opacity = Math.max(0.5, analysis.confidence);

      // Apply staggered animation
      if (analysis.confidence > 0.7) {
        step.style.animationDelay = `${index * 0.1}s`;
        step.classList.add('step-highlight');
      } else {
        step.classList.remove('step-highlight');
      }
    });
  }

  updateConvergenceHistory(analysis) {
    this.convergenceHistory.push({
      timestamp: Date.now(),
      analysis: { ...analysis }
    });

    // Keep only last 100 entries
    if (this.convergenceHistory.length > 100) {
      this.convergenceHistory = this.convergenceHistory.slice(-100);
    }
  }

  handleStrategyChange(strategyData) {
    // Reset signal state for new strategy
    this.signalState.clear();

    // Update convergence settings based on strategy
    if (strategyData.convergenceSettings) {
      Object.assign(this.convergenceSettings, strategyData.convergenceSettings);
    }

    // Trigger immediate reprocessing
    setTimeout(() => {
      this.processSignalLogic();
    }, 100);
  }

  handleThresholdChange(thresholdData) {
    // Update convergence thresholds
    if (thresholdData.convergenceThreshold !== undefined) {
      this.convergenceSettings.convergenceThreshold = thresholdData.convergenceThreshold;
    }

    if (thresholdData.divergenceThreshold !== undefined) {
      this.convergenceSettings.divergenceThreshold = thresholdData.divergenceThreshold;
    }

    // Save settings
    this.saveSettings();

    // Trigger reprocessing
    this.processSignalLogic();
  }

  handleWebSocketMessage(event) {
    try {
      const data = JSON.parse(event.data);

      if (data.type === 'signal_update') {
        this.processSignalUpdate(data.payload);
      } else if (data.type === 'convergence_update') {
        this.handleConvergenceUpdate(data.payload);
      }
    } catch (e) {
      // Ignore non-JSON messages
    }
  }

  handleConvergenceUpdate(data) {
    if (data.settings) {
      Object.assign(this.convergenceSettings, data.settings);
      this.saveSettings();
    }

    if (data.lightLogic) {
      Object.assign(this.lightLogicSettings, data.lightLogic);
      this.saveSettings();
    }

    this.processSignalLogic();
  }

  // Public API methods
  updateConvergenceSettings(settings) {
    Object.assign(this.convergenceSettings, settings);
    this.saveSettings();
    this.processSignalLogic();
  }

  updateLightLogicSettings(settings) {
    Object.assign(this.lightLogicSettings, settings);
    this.saveSettings();
    this.applyLightLogic();
  }

  getConvergenceAnalysis() {
    return this.convergenceHistory.length > 0
      ? this.convergenceHistory[this.convergenceHistory.length - 1].analysis
      : null;
  }

  getSignalState() {
    return Array.from(this.signalState.entries()).map(([key, data]) => ({
      key,
      ...data
    }));
  }

  addChevronIndicator(circle) {
    // Remove existing chevron
    const existingChevron = circle.querySelector('.signal-chevron');
    if (existingChevron) {
      existingChevron.remove();
    }

    // Create chevron element
    const chevron = document.createElement('div');
    chevron.className = 'signal-chevron';
    chevron.style.cssText = `
      position: absolute;
      top: -3px;
      right: -3px;
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 8px solid #00FFFF;
      opacity: 0.8;
      transition: all 0.3s ease;
      pointer-events: none;
      z-index: 2;
    `;

    // Position circle relatively if not already
    if (getComputedStyle(circle).position === 'static') {
      circle.style.position = 'relative';
    }

    circle.appendChild(chevron);

    // Update chevron based on signal
    this.updateChevronDirection(chevron, circle);
  }

  updateChevronDirection(chevron, circle) {
    const signalClasses = ['degen-buy', 'mild-buy', 'neutral', 'mild-sell', 'degen-sell'];
    let direction = 'up'; // default
    let color = '#00FFFF';

    signalClasses.forEach(signalClass => {
      if (circle.classList.contains(signalClass)) {
        switch (signalClass) {
          case 'degen-buy':
            direction = 'up';
            color = '#00FF00';
            break;
          case 'mild-buy':
            direction = 'up-right';
            color = '#88FF88';
            break;
          case 'mild-sell':
            direction = 'down-right';
            color = '#FF8888';
            break;
          case 'degen-sell':
            direction = 'down';
            color = '#FF0000';
            break;
          default:
            direction = 'right';
            color = '#808080';
        }
      }
    });

    // Apply chevron direction and color
    this.setChevronStyle(chevron, direction, color);
  }

  setChevronStyle(chevron, direction, color) {
    // Reset all borders
    chevron.style.borderTop = 'none';
    chevron.style.borderBottom = 'none';
    chevron.style.borderLeft = 'none';
    chevron.style.borderRight = 'none';

    switch (direction) {
      case 'up':
        chevron.style.borderLeft = '6px solid transparent';
        chevron.style.borderRight = '6px solid transparent';
        chevron.style.borderBottom = `8px solid ${color}`;
        break;
      case 'down':
        chevron.style.borderLeft = '6px solid transparent';
        chevron.style.borderRight = '6px solid transparent';
        chevron.style.borderTop = `8px solid ${color}`;
        break;
      case 'right':
        chevron.style.borderTop = '6px solid transparent';
        chevron.style.borderBottom = '6px solid transparent';
        chevron.style.borderLeft = `8px solid ${color}`;
        break;
      case 'up-right':
        chevron.style.borderBottom = `6px solid ${color}`;
        chevron.style.borderLeft = `6px solid transparent`;
        break;
      case 'down-right':
        chevron.style.borderTop = `6px solid ${color}`;
        chevron.style.borderLeft = `6px solid transparent`;
        break;
    }

    // Add glow effect
    chevron.style.filter = `drop-shadow(0 0 3px ${color})`;
  }

  reset() {
    this.signalState.clear();
    this.convergenceHistory = [];
    this.processSignalLogic();
  }
}

// Initialize enhanced signal logic system
window.enhancedSignalLogic = new EnhancedSignalLogic();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedSignalLogic;
}
