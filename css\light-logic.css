/* Light Logic and Logic Menu Styles */
.light-logic-description {
  margin: 10px 0;
  font-size: 0.9rem;
  color: #CCCCCC;
  line-height: 1.4;
}

.light-logic-option {
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.5);
  border-radius: 5px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.option-description {
  margin-top: 5px;
  font-size: 0.8rem;
  color: #AAAAAA;
  font-style: italic;
}

/* Strategy button styles */
.strategy-button {
  background: linear-gradient(to right, #00BFFF, #00FFFF) !important;
  color: #000 !important;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.strategy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 191, 255, 0.4);
}

/* ENHANCED LIGHT LOGIC CONTROLS */
.light-logic-controls {
  padding: 20px;
  background: rgba(0, 10, 30, 0.95);
  border-radius: 12px;
  border: 2px solid rgba(0, 255, 255, 0.4);
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.light-logic-option {
  margin: 20px 0;
  padding: 15px;
  background: rgba(0, 20, 40, 0.8);
  border-radius: 10px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.light-logic-option:hover {
  background: rgba(0, 30, 60, 0.9);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.light-logic-option h4 {
  margin: 0 0 15px 0;
  color: #00FFFF;
  font-size: 1.1rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* ENHANCED LOGIC MENU STYLES - PREMIUM POSITIONING */
#logicMenu {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 350px;
  background: linear-gradient(135deg, rgba(0, 10, 30, 0.98), rgba(0, 20, 50, 0.95));
  border: 3px solid rgba(0, 255, 255, 0.6);
  border-radius: 15px;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.4), inset 0 0 20px rgba(0, 100, 200, 0.1);
  z-index: 1200;
  padding: 25px;
  color: #e0e0e0;
  display: none;
  pointer-events: auto;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(15px);
  animation: slideInRight 0.4s ease-out;
}

#logicMenu.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

#logicMenu h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #00ccff;
  font-size: 1.1rem;
  border-bottom: 1px solid #303045;
  padding-bottom: 8px;
}

.logic-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.logic-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.logic-group label {
  font-size: 0.9rem;
  color: #aaa;
}

.logic-selector {
  padding: 6px 8px;
  background: #0f0f1a;
  border: 1px solid #303045;
  color: #e0e0e0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.logic-description {
  font-size: 0.8rem;
  color: #888;
  margin: 5px 0 0;
  line-height: 1.4;
}

.apply-logic-button {
  background: #2a5a8a;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 10px;
  transition: background 0.2s;
}

.apply-logic-button:hover {
  background: #3a6a9a;
}

/* Animation for menu appearance */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ENHANCED LIGHT LOGIC VISUAL TRIGGERS */

/* Convergence pulse animation for helper steps */
@keyframes convergencePulse {
  0% {
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
    transform: scale(1);
  }
}

/* Ordered pulse for convergence representation */
@keyframes orderedPulse {
  0% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

/* Strong convergence pulse */
@keyframes strongConvergence {
  0% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    border: 2px solid rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 215, 0, 1);
    border: 2px solid rgba(255, 215, 0, 0.8);
  }
  100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    border: 2px solid rgba(255, 215, 0, 0.3);
  }
}

/* Light logic activation classes */
.signal-circle.convergence-active {
  animation: convergencePulse 2s ease-in-out infinite;
}

.signal-circle.ordered-pulse {
  animation: orderedPulse 1.5s ease-in-out infinite;
}

.signal-circle.ordered-pulse-2 {
  animation: strongConvergence 1s ease-in-out infinite;
}

/* Helper step visual triggers */
.helper-step-active {
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0.3));
  border-left: 4px solid #00FFFF;
  padding-left: 12px;
  transition: all 0.3s ease;
}

.helper-step-completed {
  background: linear-gradient(90deg, rgba(0, 255, 0, 0.1), rgba(0, 255, 0, 0.2));
  border-left: 4px solid #00FF00;
  opacity: 0.7;
}

/* Convergence representation enhancements */
.convergence-indicator {
  position: relative;
  overflow: visible;
}

.convergence-indicator::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: inherit;
  background: linear-gradient(45deg, #00FFFF, #FF00FF, #FFFF00, #00FFFF);
  background-size: 400% 400%;
  animation: convergenceGlow 3s ease infinite;
  z-index: -1;
}

@keyframes convergenceGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Light intensity variations */
.light-intensity-low {
  opacity: 0.4;
  filter: brightness(0.6);
}

.light-intensity-medium {
  opacity: 0.7;
  filter: brightness(0.8);
}

.light-intensity-high {
  opacity: 1;
  filter: brightness(1.2);
}

.light-intensity-maximum {
  opacity: 1;
  filter: brightness(1.5) saturate(1.3);
  box-shadow: 0 0 15px currentColor;
}

/* System notification */
.system-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 2000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Preview light styles */
.preview-light {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

/* Light logic presets */
.light-logic-presets {
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.3);
  border-radius: 5px;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.light-logic-presets h5 {
  margin: 0 0 10px 0;
  color: #00BFFF;
  font-size: 0.9rem;
}

.preset-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preset-button {
  background: rgba(0, 150, 255, 0.2);
  border: 1px solid rgba(0, 150, 255, 0.5);
  color: #00BFFF;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.preset-button:hover {
  background: rgba(0, 150, 255, 0.4);
  transform: translateY(-1px);
}

/* Slider styles */
.slider-container {
  margin: 10px 0;
  position: relative;
  padding: 15px 0;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
  color: #00BFFF;
  font-size: 0.9rem;
}

.slider-container input[type="range"] {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  border-radius: 2px;
  background: rgba(0, 150, 255, 0.2);
  outline: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00BFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-container input[type="range"]:hover::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.8);
}

/* Color picker styles */
.color-picker-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.color-picker-container label {
  margin-right: 10px;
  color: #00BFFF;
  font-size: 0.9rem;
  min-width: 100px;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.5);
  border-radius: 8px;
  cursor: pointer;
  background: #000;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.color-picker:hover {
  border-color: rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  transform: scale(1.1);
}

/* ENHANCED ANIMATIONS */
@keyframes slideInRight {
  from {
    transform: translateX(100%) translateY(-50%);
    opacity: 0;
  }
  to {
    transform: translateX(0) translateY(-50%);
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.6);
  }
}

/* ENHANCED CONTROL BUTTONS */
.logic-control-button {
  background: linear-gradient(135deg, rgba(0, 100, 200, 0.8), rgba(0, 150, 255, 0.6));
  border: 2px solid rgba(0, 255, 255, 0.5);
  border-radius: 8px;
  color: #ffffff;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
  margin: 5px;
}

.logic-control-button:hover {
  background: linear-gradient(135deg, rgba(0, 150, 255, 0.9), rgba(0, 200, 255, 0.7));
  border-color: rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
  transform: translateY(-2px);
}

.logic-control-button:active {
  transform: translateY(0);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
}
}

/* Animation preview */
.animation-preview {
  width: 100%;
  height: 60px;
  margin: 15px 0;
  background: rgba(0, 20, 40, 0.3);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.animation-preview-light {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #00BFFF;
  position: relative;
}
