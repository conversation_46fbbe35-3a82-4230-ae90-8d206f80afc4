/**
 * StarCrypt Data Flow Verifier
 * Ensures all systems receive real WebSocket data, not fake/padding data
 */

class DataFlowVerifier {
  constructor() {
    this.dataChecks = new Map();
    this.verificationInterval = 10000; // 10 seconds
    this.lastVerification = 0;
    this.dataQuality = new Map();
    
    this.init();
  }

  init() {
    console.log('🔍 DATA FLOW VERIFIER: Initializing real data verification system...');
    
    this.setupDataChecks();
    this.startVerification();
    this.monitorDataFlow();
    
    console.log('✅ DATA FLOW VERIFIER: Real data verification active');
  }

  setupDataChecks() {
    // Check WebSocket data integrity
    this.dataChecks.set('websocket', {
      name: 'WebSocket Data',
      check: () => this.verifyWebSocketData(),
      critical: true
    });

    // Check price data integrity
    this.dataChecks.set('price', {
      name: 'Price Data',
      check: () => this.verifyPriceData(),
      critical: true
    });

    // Check indicator data integrity
    this.dataChecks.set('indicators', {
      name: 'Indicator Data',
      check: () => this.verifyIndicatorData(),
      critical: true
    });

    // Check signal light data
    this.dataChecks.set('signals', {
      name: 'Signal Light Data',
      check: () => this.verifySignalData(),
      critical: false
    });

    // Check ML system data
    this.dataChecks.set('ml', {
      name: 'ML System Data',
      check: () => this.verifyMLData(),
      critical: false
    });
  }

  startVerification() {
    setInterval(() => {
      this.performVerification();
    }, this.verificationInterval);

    // Initial verification after 5 seconds
    setTimeout(() => this.performVerification(), 5000);
  }

  performVerification() {
    console.log('🔍 DATA FLOW VERIFIER: Performing comprehensive data verification...');
    
    const results = new Map();
    let criticalIssues = 0;
    let totalIssues = 0;

    for (const [checkId, check] of this.dataChecks) {
      try {
        const result = check.check();
        results.set(checkId, result);
        
        if (!result.valid) {
          totalIssues++;
          if (check.critical) {
            criticalIssues++;
          }
          console.warn(`🚨 DATA ISSUE: ${check.name} - ${result.issue}`);
        } else {
          console.log(`✅ DATA OK: ${check.name} - ${result.message}`);
        }
      } catch (error) {
        console.error(`❌ VERIFICATION ERROR: ${check.name}:`, error);
        totalIssues++;
        if (check.critical) criticalIssues++;
      }
    }

    this.lastVerification = Date.now();
    
    if (criticalIssues > 0) {
      console.error(`🚨 CRITICAL DATA ISSUES: ${criticalIssues} critical, ${totalIssues} total`);
      this.attemptDataRecovery(results);
    } else if (totalIssues > 0) {
      console.warn(`⚠️ DATA ISSUES: ${totalIssues} non-critical issues detected`);
    } else {
      console.log('✅ DATA FLOW VERIFICATION: All systems receiving real data');
    }

    return results;
  }

  verifyWebSocketData() {
    // Check if WebSocket is connected and receiving data
    if (!window.ws || window.ws.readyState !== WebSocket.OPEN) {
      return { valid: false, issue: 'WebSocket not connected' };
    }

    // Check if we've received recent data
    const lastUpdate = window.lastDataUpdate;
    if (!lastUpdate || Date.now() - lastUpdate > 120000) { // 2 minutes
      return { valid: false, issue: 'No recent WebSocket data received' };
    }

    return { valid: true, message: 'WebSocket connected and receiving data' };
  }

  verifyPriceData() {
    // Check if window.currentPrice is a valid number
    if (typeof window.currentPrice !== 'number' || isNaN(window.currentPrice)) {
      return { valid: false, issue: `Invalid currentPrice: ${typeof window.currentPrice} ${window.currentPrice}` };
    }

    // Check if price is reasonable (between $1,000 and $1,000,000)
    if (window.currentPrice < 1000 || window.currentPrice > 1000000) {
      return { valid: false, issue: `Unrealistic price: $${window.currentPrice}` };
    }

    // Check if price element exists and matches
    const priceElement = document.getElementById('currentPrice');
    if (priceElement) {
      const elementText = priceElement.textContent || '';
      const elementPrice = parseFloat(elementText.replace(/[$,]/g, ''));
      if (Math.abs(elementPrice - window.currentPrice) > 1) {
        return { valid: false, issue: `Price mismatch: element=${elementPrice}, global=${window.currentPrice}` };
      }
    }

    return { valid: true, message: `Valid price: $${window.currentPrice.toLocaleString()}` };
  }

  verifyIndicatorData() {
    // Check if indicator data exists
    if (!window.indicatorsData || typeof window.indicatorsData !== 'object') {
      return { valid: false, issue: 'No indicator data available' };
    }

    const timeframes = Object.keys(window.indicatorsData);
    if (timeframes.length === 0) {
      return { valid: false, issue: 'No timeframe data available' };
    }

    // Check if data is recent
    let hasRecentData = false;
    const cutoff = Date.now() - 300000; // 5 minutes

    for (const timeframe of timeframes) {
      const tfData = window.indicatorsData[timeframe];
      if (tfData && typeof tfData === 'object') {
        for (const indicator in tfData) {
          const data = tfData[indicator];
          if (data && data.timestamp && data.timestamp > cutoff) {
            hasRecentData = true;
            break;
          }
        }
      }
      if (hasRecentData) break;
    }

    if (!hasRecentData) {
      return { valid: false, issue: 'No recent indicator data (older than 5 minutes)' };
    }

    // Check for common indicators
    const requiredIndicators = ['rsi', 'macd', 'volume'];
    const availableIndicators = new Set();
    
    for (const timeframe of timeframes) {
      const tfData = window.indicatorsData[timeframe];
      if (tfData) {
        Object.keys(tfData).forEach(ind => availableIndicators.add(ind));
      }
    }

    const missingIndicators = requiredIndicators.filter(ind => !availableIndicators.has(ind));
    if (missingIndicators.length > 0) {
      return { valid: false, issue: `Missing indicators: ${missingIndicators.join(', ')}` };
    }

    return { valid: true, message: `${timeframes.length} timeframes, ${availableIndicators.size} indicators` };
  }

  verifySignalData() {
    // Check signal circles
    const signalCircles = document.querySelectorAll('.signal-circle');
    if (signalCircles.length === 0) {
      return { valid: false, issue: 'No signal circles found' };
    }

    // Check connected signals
    const connectedSignals = document.querySelectorAll('.signal-circle[data-admiral-connected="true"]');
    const connectionRate = (connectedSignals.length / signalCircles.length) * 100;

    if (connectionRate < 50) {
      return { valid: false, issue: `Low connection rate: ${connectionRate.toFixed(1)}% (${connectedSignals.length}/${signalCircles.length})` };
    }

    return { valid: true, message: `${connectedSignals.length}/${signalCircles.length} signals connected (${connectionRate.toFixed(1)}%)` };
  }

  verifyMLData() {
    // Check if ML systems have access to real data
    if (!window.advancedMLFeatures) {
      return { valid: false, issue: 'ML features not initialized' };
    }

    // Check if ML system can access current price
    try {
      const mlPrice = window.advancedMLFeatures.getSafeCurrentPrice();
      if (typeof mlPrice !== 'number' || isNaN(mlPrice)) {
        return { valid: false, issue: 'ML system cannot access valid price data' };
      }

      // Check if ML price matches global price
      if (window.currentPrice && Math.abs(mlPrice - window.currentPrice) > 1) {
        return { valid: false, issue: `ML price mismatch: ML=${mlPrice}, Global=${window.currentPrice}` };
      }
    } catch (error) {
      return { valid: false, issue: `ML price access error: ${error.message}` };
    }

    return { valid: true, message: 'ML systems accessing real data' };
  }

  attemptDataRecovery(results) {
    console.log('🛠️ DATA FLOW VERIFIER: Attempting data recovery...');

    // Recovery for WebSocket issues
    if (results.get('websocket') && !results.get('websocket').valid) {
      if (window.ws && window.ws.readyState !== WebSocket.OPEN) {
        console.log('🛠️ Attempting WebSocket reconnection...');
        if (typeof initializeWebSocket === 'function') {
          initializeWebSocket();
        }
      }
    }

    // Recovery for price data issues
    if (results.get('price') && !results.get('price').valid) {
      console.log('🛠️ Attempting price data recovery...');
      
      // Try to extract price from DOM
      const priceElement = document.getElementById('currentPrice');
      if (priceElement) {
        const textContent = priceElement.textContent || '';
        const numericMatch = textContent.match(/[\d,]+\.?\d*/);
        if (numericMatch) {
          const extractedPrice = parseFloat(numericMatch[0].replace(/,/g, ''));
          if (!isNaN(extractedPrice) && extractedPrice > 0) {
            window.currentPrice = extractedPrice;
            console.log(`🛠️ Recovered price from DOM: $${extractedPrice}`);
          }
        }
      }
    }

    // Recovery for signal connection issues
    if (results.get('signals') && !results.get('signals').valid) {
      console.log('🛠️ Attempting signal light reconnection...');
      if (typeof connectSignalLightsToAdmiralMode === 'function') {
        setTimeout(() => {
          connectSignalLightsToAdmiralMode();
        }, 1000);
      }
    }
  }

  monitorDataFlow() {
    // Monitor data flow in real-time
    if (window.ws) {
      const originalOnMessage = window.ws.onmessage;
      window.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.recordDataFlow(data);
        if (originalOnMessage) originalOnMessage(event);
      };
    }
  }

  recordDataFlow(data) {
    const timestamp = Date.now();
    const dataType = data.type;
    
    if (!this.dataQuality.has(dataType)) {
      this.dataQuality.set(dataType, {
        count: 0,
        lastReceived: 0,
        avgSize: 0,
        errors: 0
      });
    }

    const quality = this.dataQuality.get(dataType);
    quality.count++;
    quality.lastReceived = timestamp;
    
    // Calculate average data size
    const dataSize = JSON.stringify(data).length;
    quality.avgSize = (quality.avgSize * (quality.count - 1) + dataSize) / quality.count;

    // Detect potential fake/padding data
    if (dataType === 'indicators' && dataSize < 100) {
      quality.errors++;
      console.warn(`🚨 SUSPICIOUS DATA: ${dataType} message unusually small (${dataSize} bytes)`);
    }
  }

  // Public API
  getDataQuality() {
    return Object.fromEntries(this.dataQuality);
  }

  getLastVerification() {
    return {
      timestamp: this.lastVerification,
      age: Date.now() - this.lastVerification
    };
  }

  forceVerification() {
    return this.performVerification();
  }
}

// Initialize data flow verifier
window.dataFlowVerifier = new DataFlowVerifier();

// Export for global access
window.DataFlowVerifier = DataFlowVerifier;

// Add to global controls
if (window.starCryptControls) {
  window.starCryptControls.verifyData = () => window.dataFlowVerifier.forceVerification();
  window.starCryptControls.dataQuality = () => window.dataFlowVerifier.getDataQuality();
}

console.log('🔍 DATA FLOW VERIFIER: Loaded and monitoring real data integrity');
