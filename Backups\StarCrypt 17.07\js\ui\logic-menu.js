/**
 * Logic Menu Module
 * Manages the signal logic menu interface and related functionality
 */

(function() {
    'use strict';
    
    // State
    let currentLogic = 'standard';
    let currentIntensity = 'medium';
    let showDirectionArrows = true;
    let moduleInitialized = false;
    
    /**
     * Initialize the logic menu handlers
     */
    function initializeLogicMenu() {
        ensureMenuContainerExists();
        renderLogicMenu();
        attachLogicMenuEventHandlers();
        
        moduleInitialized = true;
        console.log('Logic menu module initialized');
    }
    
    /**
     * Ensures the menu container exists with the correct ID
     */
    function ensureMenuContainerExists() {
        // Get the container created by menu-controller.js
        let container = document.getElementById('logicMenu');
        
        if (!container) {
            console.error('Logic menu container not found. Make sure menu-controller.js is loaded first.');
            return null;
        }
        
        // Ensure it has the right class
        container.classList.add('menu-content');
        return container;
    }
    
    /**
     * Renders the logic menu content
     */
    function renderLogicMenu() {
        // Always ensure container exists
        const container = ensureMenuContainerExists();
        
        if (!container) {
            console.error('Failed to create logic menu container');
            return;
        }
        
        container.innerHTML = `
            <h3>Signal Logic Settings</h3>
            <div class="logic-settings">
                <div class="logic-group">
                    <label>Logic Type:</label>
                    <select id="logicTypeSelector" class="logic-selector">
                        <option value="standard" ${currentLogic === 'standard' ? 'selected' : ''}>Standard</option>
                        <option value="aggressive" ${currentLogic === 'aggressive' ? 'selected' : ''}>Aggressive</option>
                        <option value="conservative" ${currentLogic === 'conservative' ? 'selected' : ''}>Conservative</option>
                    </select>
                    <p id="logicDescription" class="logic-description">
                        ${getLightLogicDescription(currentLogic)}
                    </p>
                </div>
                <div class="logic-group">
                    <label>Signal Intensity:</label>
                    <select id="intensitySelector" class="logic-selector">
                        <option value="low" ${currentIntensity === 'low' ? 'selected' : ''}>Low</option>
                        <option value="medium" ${currentIntensity === 'medium' ? 'selected' : ''}>Medium</option>
                        <option value="high" ${currentIntensity === 'high' ? 'selected' : ''}>High</option>
                    </select>
                </div>
                <div class="logic-group">
                    <label>
                        <input type="checkbox" id="showArrowsCheckbox" ${showDirectionArrows ? 'checked' : ''}>
                        Show Direction Arrows
                    </label>
                </div>
                <div class="logic-group">
                    <button id="applyLogicButton" class="apply-logic-button">Apply Logic</button>
                </div>
            </div>
        `;
    }
    
    /**
     * Attach event handlers to logic menu elements
     */
    function attachLogicMenuEventHandlers() {
        // Handler for logic type change
        const logicTypeSelector = document.getElementById('logicTypeSelector');
        if (logicTypeSelector) {
            logicTypeSelector.addEventListener('change', function() {
                const selectedLogic = this.value;
                document.getElementById('logicDescription').textContent = getLightLogicDescription(selectedLogic);
                currentLogic = selectedLogic;
            });
        }
        
        // Handler for intensity change
        const intensitySelector = document.getElementById('intensitySelector');
        if (intensitySelector) {
            intensitySelector.addEventListener('change', function() {
                currentIntensity = this.value;
            });
        }
        
        // Handler for arrows checkbox
        const showArrowsCheckbox = document.getElementById('showArrowsCheckbox');
        if (showArrowsCheckbox) {
            showArrowsCheckbox.addEventListener('change', function() {
                showDirectionArrows = this.checked;
            });
        }
        
        // Apply button handler
        const applyLogicButton = document.getElementById('applyLogicButton');
        if (applyLogicButton) {
            applyLogicButton.addEventListener('click', function() {
                applyLightLogic(currentLogic, currentIntensity, showDirectionArrows);
            });
        }
    }
    
    /**
     * Gets description text for a given logic type
     */
    function getLightLogicDescription(type) {
        const descriptions = {
            standard: 'Standard logic provides a balanced approach to signals, with equal weight given to momentum and trend indicators.',
            aggressive: 'Aggressive logic puts more emphasis on early signals. May generate more signals but with higher false positive rate.',
            conservative: 'Conservative logic requires stronger confirmation before signal changes. Fewer signals but higher confidence levels.'
        };
        
        return descriptions[type] || 'No description available';
    }
    
    /**
     * Applies the selected light logic settings with enhanced visual effects
     */
    function applyLightLogic(type, intensity, showArrows) {
        console.log(`Applying light logic: ${type}, intensity: ${intensity}, arrows: ${showArrows}`);

        // Save settings to local storage
        localStorage.setItem('signalLogicType', type);
        localStorage.setItem('signalLogicIntensity', intensity);
        localStorage.setItem('signalShowArrows', showArrows);

        // Update global settings if available
        if (window.signalSystem) {
            window.signalSystem.config.logicType = type;
            window.signalSystem.config.logicIntensity = intensity;
            window.signalSystem.config.showDirectionArrows = showArrows;
        }

        // Apply intensity classes to all signal circles
        applyIntensityClasses(intensity);

        // Apply convergence visual effects
        applyConvergenceEffects(type);

        // Force signal matrix update
        if (typeof updateSignalMatrix === 'function') {
            updateSignalMatrix();
        }

        // Update helper steps with visual triggers
        updateHelperStepVisuals();

        // Close menu after applying
        closeLogicMenu();
    }

    /**
     * Apply intensity classes to signal circles
     */
    function applyIntensityClasses(intensity) {
        const circles = document.querySelectorAll('.signal-circle');
        const intensityClass = `light-intensity-${intensity}`;

        circles.forEach(circle => {
            // Remove existing intensity classes
            circle.classList.remove('light-intensity-low', 'light-intensity-medium', 'light-intensity-high', 'light-intensity-maximum');
            // Add new intensity class
            circle.classList.add(intensityClass);
        });
    }

    /**
     * Apply convergence visual effects
     */
    function applyConvergenceEffects(type) {
        const circles = document.querySelectorAll('.signal-circle');

        circles.forEach(circle => {
            // Remove existing convergence classes
            circle.classList.remove('convergence-active', 'ordered-pulse', 'ordered-pulse-2');

            // Apply convergence effects based on type
            if (type === 'convergence' || type === 'weighted') {
                const indicator = circle.getAttribute('data-ind');
                const timeframe = circle.getAttribute('data-tf');

                // Check if this indicator is part of convergence
                if (isConvergenceIndicator(indicator, timeframe)) {
                    circle.classList.add('convergence-active');

                    // Add stronger effects for high convergence
                    const convergenceStrength = getConvergenceStrength(indicator, timeframe);
                    if (convergenceStrength > 0.7) {
                        circle.classList.add('ordered-pulse-2');
                    } else if (convergenceStrength > 0.4) {
                        circle.classList.add('ordered-pulse');
                    }
                }
            }
        });
    }

    /**
     * Update helper steps with visual triggers
     */
    function updateHelperStepVisuals() {
        const helperSteps = document.querySelectorAll('.helper-step, .helper-container li');

        helperSteps.forEach((step, index) => {
            // Remove existing classes
            step.classList.remove('helper-step-active', 'helper-step-completed');

            // Add visual triggers based on step completion status
            setTimeout(() => {
                if (isStepCompleted(step, index)) {
                    step.classList.add('helper-step-completed');
                } else if (isStepActive(step, index)) {
                    step.classList.add('helper-step-active');
                }
            }, index * 100); // Staggered animation
        });
    }

    /**
     * Check if indicator is part of convergence
     */
    function isConvergenceIndicator(indicator, timeframe) {
        // This would integrate with the actual convergence logic
        // For now, return a placeholder
        return Math.random() > 0.6; // Simulate convergence detection
    }

    /**
     * Get convergence strength for an indicator
     */
    function getConvergenceStrength(indicator, timeframe) {
        // This would calculate actual convergence strength
        // For now, return a placeholder
        return Math.random();
    }

    /**
     * Check if a helper step is completed
     */
    function isStepCompleted(step, index) {
        // This would check actual step completion status
        // For now, return a placeholder
        return index < 2; // First two steps completed
    }

    /**
     * Check if a helper step is currently active
     */
    function isStepActive(step, index) {
        // This would check if step is currently being executed
        // For now, return a placeholder
        return index === 2; // Third step is active
    }

    /**
     * Close the logic menu
     */
    function closeLogicMenu() {
        const menu = document.getElementById('logicMenu');
        if (menu) {
            menu.classList.remove('active');
            menu.style.display = 'none';
        }
    }

    /**
     * Show system notification
     */
    function showSystemNotification(message) {
        // Show confirmation to user
        const notification = document.createElement('div');
        notification.className = 'system-notification';
        notification.textContent = message || 'Signal logic updated successfully';
        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    // Make initializeLogicMenu available globally
    window.initializeLogicMenu = function() {
        if (moduleInitialized) {
            console.log('Logic menu already initialized');
            return;
        }
        ensureMenuContainerExists();
        renderLogicMenu();
        attachLogicMenuEventHandlers();
        moduleInitialized = true;
        console.log('Logic menu module initialized');
    };
    
    // Load saved settings
    const savedType = localStorage.getItem('signalLogicType');
    const savedIntensity = localStorage.getItem('signalLogicIntensity');
    const savedShowArrows = localStorage.getItem('signalShowArrows');
    
    if (savedType) currentLogic = savedType;
    if (savedIntensity) currentIntensity = savedIntensity;
    if (savedShowArrows !== null) showDirectionArrows = savedShowArrows === 'true';
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Wait a short delay to ensure containers are ready
        setTimeout(initializeLogicMenu, 500);
    });
    
    // Export to window
    window.renderLogicMenu = renderLogicMenu;
    window.applyLightLogic = applyLightLogic;
    window.getLightLogicDescription = getLightLogicDescription;
    
})();
