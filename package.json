{"name": "starcrypt-enterprise", "version": "1.0.0", "description": "StarCrypt Enterprise Trading Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint .", "format": "prettier --write ."}, "dependencies": {"chart.js": "^4.4.4", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.0.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "express-rate-limit": "^7.1.5", "helmet": "^7.2.0", "limiter": "^2.1.0", "node-fetch": "^2.7.0", "openai": "^5.8.2", "uuid": "^11.1.0", "vader-sentiment": "^1.1.3", "ws": "^8.17.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^8.1.3", "globals": "^16.3.0", "nodemon": "^3.1.0", "prettier": "^3.2.5"}, "engines": {"node": ">=16.0.0"}}