/**
 * StarCrypt System Health Monitor
 * Monitors all systems for errors and provides automatic recovery
 */

class SystemHealthMonitor {
  constructor() {
    this.systems = new Map();
    this.errorCounts = new Map();
    this.lastHealthCheck = Date.now();
    this.healthCheckInterval = 30000; // 30 seconds
    this.maxErrors = 5;
    this.recoveryAttempts = new Map();
    
    this.init();
  }

  init() {
    console.log('🏥 SYSTEM HEALTH MONITOR: Initializing comprehensive health monitoring...');
    
    this.registerSystems();
    this.startHealthChecking();
    this.setupErrorHandling();
    
    console.log('✅ SYSTEM HEALTH MONITOR: Health monitoring active');
  }

  registerSystems() {
    // Register all critical StarCrypt systems
    this.systems.set('starfield', {
      name: 'Starfield Animation',
      check: () => window.starfieldAnimation && window.starfieldAnimation.isRunning,
      recover: () => this.recoverStarfield(),
      critical: false
    });

    this.systems.set('websocket', {
      name: 'WebSocket Connection',
      check: () => window.ws && window.ws.readyState === WebSocket.OPEN,
      recover: () => this.recoverWebSocket(),
      critical: true
    });

    this.systems.set('signalLights', {
      name: 'Signal Lights System',
      check: () => document.querySelectorAll('.signal-circle').length > 0,
      recover: () => this.recoverSignalLights(),
      critical: true
    });

    this.systems.set('mlSystems', {
      name: 'ML Analysis Systems',
      check: () => window.MLHistoricalAnalysis || window.mlHistoricalAnalysis,
      recover: () => this.recoverMLSystems(),
      critical: false
    });

    this.systems.set('unifiedCommander', {
      name: 'Unified Signal Commander',
      check: () => window.unifiedSignalCommander && window.unifiedSignalCommander.eventListenersAttached,
      recover: () => this.recoverUnifiedCommander(),
      critical: true
    });

    this.systems.set('tooltips', {
      name: 'Tooltip System',
      check: () => window.tooltipFixes || document.getElementById('global-tooltip'),
      recover: () => this.recoverTooltips(),
      critical: false
    });
  }

  startHealthChecking() {
    setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckInterval);

    // Initial health check
    setTimeout(() => this.performHealthCheck(), 5000);
  }

  performHealthCheck() {
    console.log('🏥 HEALTH CHECK: Performing system health assessment...');
    
    let healthyCount = 0;
    let totalSystems = this.systems.size;
    const issues = [];

    for (const [systemId, system] of this.systems) {
      try {
        const isHealthy = system.check();
        
        if (isHealthy) {
          healthyCount++;
          // Reset error count on successful check
          this.errorCounts.set(systemId, 0);
        } else {
          issues.push(system.name);
          this.handleSystemFailure(systemId, system);
        }
      } catch (error) {
        console.error(`🏥 HEALTH CHECK: Error checking ${system.name}:`, error);
        issues.push(`${system.name} (check failed)`);
        this.handleSystemFailure(systemId, system);
      }
    }

    const healthPercentage = Math.round((healthyCount / totalSystems) * 100);
    
    if (issues.length === 0) {
      console.log(`✅ HEALTH CHECK: All systems healthy (${healthPercentage}%)`);
    } else {
      console.warn(`⚠️ HEALTH CHECK: ${issues.length} issues detected (${healthPercentage}% healthy):`, issues);
    }

    this.lastHealthCheck = Date.now();
    this.updateHealthDisplay(healthPercentage, issues);
  }

  handleSystemFailure(systemId, system) {
    const errorCount = (this.errorCounts.get(systemId) || 0) + 1;
    this.errorCounts.set(systemId, errorCount);

    console.warn(`🚨 SYSTEM FAILURE: ${system.name} failed (${errorCount}/${this.maxErrors})`);

    if (errorCount >= this.maxErrors) {
      console.error(`💀 CRITICAL FAILURE: ${system.name} has exceeded maximum errors, attempting recovery...`);
      this.attemptRecovery(systemId, system);
    }
  }

  attemptRecovery(systemId, system) {
    const attempts = (this.recoveryAttempts.get(systemId) || 0) + 1;
    this.recoveryAttempts.set(systemId, attempts);

    if (attempts > 3) {
      console.error(`💀 RECOVERY FAILED: ${system.name} recovery attempts exhausted`);
      return;
    }

    console.log(`🔧 RECOVERY: Attempting to recover ${system.name} (attempt ${attempts}/3)...`);

    try {
      system.recover();
      console.log(`✅ RECOVERY: ${system.name} recovery initiated`);
      
      // Reset error count after recovery attempt
      this.errorCounts.set(systemId, 0);
    } catch (error) {
      console.error(`❌ RECOVERY: Failed to recover ${system.name}:`, error);
    }
  }

  // Recovery methods for each system
  recoverStarfield() {
    if (window.starfieldAnimation) {
      window.starfieldAnimation.stop();
    }
    setTimeout(() => {
      window.starfieldAnimation = new StarfieldAnimation();
    }, 1000);
  }

  recoverWebSocket() {
    if (window.ws) {
      window.ws.close();
    }
    setTimeout(() => {
      if (typeof initializeWebSocket === 'function') {
        initializeWebSocket();
      }
    }, 2000);
  }

  recoverSignalLights() {
    if (typeof connectSignalLightsToAdmiralMode === 'function') {
      connectSignalLightsToAdmiralMode();
    }
    if (window.unifiedSignalCommander) {
      window.unifiedSignalCommander.setupMasterEventHandler();
    }
  }

  recoverMLSystems() {
    if (typeof initializeMLSystems === 'function') {
      initializeMLSystems();
    }
  }

  recoverUnifiedCommander() {
    if (window.unifiedSignalCommander) {
      window.unifiedSignalCommander.setupMasterEventHandler();
    }
  }

  recoverTooltips() {
    if (window.TooltipFixes) {
      new TooltipFixes();
    }
  }

  setupErrorHandling() {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('🚨 GLOBAL ERROR:', event.error);
      this.logError('global', event.error);
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 UNHANDLED PROMISE REJECTION:', event.reason);
      this.logError('promise', event.reason);
    });
  }

  logError(source, error) {
    const errorKey = `${source}_errors`;
    const count = (this.errorCounts.get(errorKey) || 0) + 1;
    this.errorCounts.set(errorKey, count);

    if (count > 10) {
      console.warn(`⚠️ HIGH ERROR RATE: ${source} has generated ${count} errors`);
    }
  }

  updateHealthDisplay(percentage, issues) {
    // Update any health display elements
    const healthDisplay = document.getElementById('system-health-display');
    if (healthDisplay) {
      healthDisplay.innerHTML = `
        <div class="health-status ${percentage >= 80 ? 'healthy' : percentage >= 60 ? 'warning' : 'critical'}">
          System Health: ${percentage}%
          ${issues.length > 0 ? `<br>Issues: ${issues.join(', ')}` : ''}
        </div>
      `;
    }
  }

  // Public API
  getSystemHealth() {
    const health = {};
    for (const [systemId, system] of this.systems) {
      health[systemId] = {
        name: system.name,
        healthy: system.check(),
        errors: this.errorCounts.get(systemId) || 0,
        recoveryAttempts: this.recoveryAttempts.get(systemId) || 0
      };
    }
    return health;
  }

  forceRecovery(systemId) {
    const system = this.systems.get(systemId);
    if (system) {
      this.attemptRecovery(systemId, system);
    }
  }
}

// Initialize system health monitor
window.systemHealthMonitor = new SystemHealthMonitor();

// Export for global access
window.SystemHealthMonitor = SystemHealthMonitor;

console.log('🏥 SYSTEM HEALTH MONITOR: Loaded and monitoring all systems');
