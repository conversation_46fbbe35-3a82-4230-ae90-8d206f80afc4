/**
 * Enhanced Light Logic Overlays Manager for StarCrypt
 * Comprehensive overlay management with proper z-index hierarchy and convergence visualization
 */

class EnhancedLightLogicOverlays {
  constructor() {
    this.overlaySettings = {
      enableConvergenceOverlays: true,
      enableHelperStepOverlays: true,
      enableStructuredLights: true,
      enableDegenismMode: false,
      lightLogicStyle: 'cosmic',
      convergenceThreshold: 0.6,
      overlayIntensity: 0.7,
      animationSpeed: 1.0
    };
    
    this.activeOverlays = new Map();
    this.convergenceData = new Map();
    this.isInitialized = false;
    
    this.init();
  }

  init() {
    console.log('[EnhancedLightLogicOverlays] Initializing enhanced overlay system...');
    
    // Fix z-index issues immediately
    this.fixZIndexIssues();
    
    // Setup overlay containers
    this.setupOverlayContainers();
    
    // Initialize convergence detection
    this.initializeConvergenceDetection();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Apply initial overlays
    this.applyInitialOverlays();
    
    this.isInitialized = true;
    console.log('[EnhancedLightLogicOverlays] Enhanced overlay system initialized successfully');
  }

  fixZIndexIssues() {
    console.log('[EnhancedLightLogicOverlays] Fixing z-index issues...');
    
    // Fix menu z-index conflicts
    const menus = ['lightLogicMenu', 'logicMenu', 'indicatorMenu', 'strategyMenu', 'thresholdsMenu'];
    menus.forEach(menuId => {
      const menu = document.getElementById(menuId);
      if (menu) {
        menu.style.zIndex = menuId.includes('enhanced') || menuId.includes('lightLogic') ? '1100' : '1000';
        menu.style.pointerEvents = 'auto';
      }
    });

    // Ensure interactive elements are always accessible
    const interactiveSelectors = [
      'button', 'input', 'select', '.slider', '.threshold-slider', 
      '.enhanced-slider', '.signal-circle', '.menu-button'
    ];
    
    interactiveSelectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(element => {
        element.style.position = 'relative';
        element.style.zIndex = '100';
        element.style.pointerEvents = 'auto';
        element.classList.add('interactive-element');
      });
    });

    // Fix tooltip z-index
    document.querySelectorAll('.custom-tooltip, .tooltip').forEach(tooltip => {
      tooltip.style.zIndex = '9999';
      tooltip.style.pointerEvents = 'none';
    });
  }

  setupOverlayContainers() {
    console.log('[EnhancedLightLogicOverlays] Setting up overlay containers...');
    
    // Create main overlay container if it doesn't exist
    if (!document.getElementById('enhanced-overlays-container')) {
      const container = document.createElement('div');
      container.id = 'enhanced-overlays-container';
      container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 50;
      `;
      document.body.appendChild(container);
    }

    // Setup signal light overlay containers
    this.setupSignalLightOverlays();
    
    // Setup helper step overlay containers
    this.setupHelperStepOverlays();
  }

  setupSignalLightOverlays() {
    const signalCircles = document.querySelectorAll('.signal-circle');
    signalCircles.forEach(circle => {
      if (!circle.querySelector('.signal-light-overlay')) {
        const overlay = document.createElement('div');
        overlay.className = 'signal-light-overlay';
        circle.appendChild(overlay);
      }
      
      if (!circle.querySelector('.light-logic-layer')) {
        const layer = document.createElement('div');
        layer.className = 'light-logic-layer';
        circle.appendChild(layer);
      }
      
      if (!circle.querySelector('.convergence-overlay')) {
        const convergenceOverlay = document.createElement('div');
        convergenceOverlay.className = 'convergence-overlay';
        circle.appendChild(convergenceOverlay);
      }
    });
  }

  setupHelperStepOverlays() {
    const helperSteps = document.querySelectorAll('.helper-step');
    helperSteps.forEach(step => {
      if (!step.querySelector('.helper-step-overlay')) {
        const overlay = document.createElement('div');
        overlay.className = 'helper-step-overlay';
        step.style.position = 'relative';
        step.appendChild(overlay);
      }
    });
  }

  initializeConvergenceDetection() {
    console.log('[EnhancedLightLogicOverlays] Initializing convergence detection...');
    
    // Monitor signal changes for convergence
    this.convergenceMonitor = setInterval(() => {
      this.detectConvergence();
    }, 1000);
  }

  detectConvergence() {
    const signalCircles = document.querySelectorAll('.signal-circle');
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    const indicators = new Set();
    
    // Collect all indicators
    signalCircles.forEach(circle => {
      const indicator = circle.dataset.indicator;
      if (indicator) indicators.add(indicator);
    });
    
    // Analyze convergence for each indicator
    indicators.forEach(indicator => {
      const convergenceData = this.analyzeIndicatorConvergence(indicator, timeframes);
      this.convergenceData.set(indicator, convergenceData);
      
      if (convergenceData.strength > this.overlaySettings.convergenceThreshold) {
        this.activateConvergenceOverlay(indicator, convergenceData);
      } else {
        this.deactivateConvergenceOverlay(indicator);
      }
    });
  }

  analyzeIndicatorConvergence(indicator, timeframes) {
    const signals = [];
    let buyCount = 0;
    let sellCount = 0;
    
    timeframes.forEach(timeframe => {
      const circle = document.querySelector(`.signal-circle[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
      if (circle) {
        const signal = this.getSignalFromCircle(circle);
        signals.push({ timeframe, signal });
        
        if (signal === 'buy') buyCount++;
        else if (signal === 'sell') sellCount++;
      }
    });
    
    const totalSignals = signals.length;
    const convergenceStrength = Math.max(buyCount, sellCount) / totalSignals;
    const dominantSignal = buyCount > sellCount ? 'buy' : sellCount > buyCount ? 'sell' : 'neutral';
    
    return {
      indicator,
      signals,
      strength: convergenceStrength,
      dominantSignal,
      buyCount,
      sellCount,
      totalSignals
    };
  }

  getSignalFromCircle(circle) {
    const classList = circle.classList;
    if (classList.contains('buy') || classList.contains('bullish') || classList.contains('overbought')) {
      return 'buy';
    } else if (classList.contains('sell') || classList.contains('bearish') || classList.contains('oversold')) {
      return 'sell';
    }
    return 'neutral';
  }

  activateConvergenceOverlay(indicator, convergenceData) {
    const circles = document.querySelectorAll(`.signal-circle[data-indicator="${indicator}"]`);
    circles.forEach(circle => {
      const overlay = circle.querySelector('.convergence-overlay');
      if (overlay) {
        overlay.classList.add('active');
        overlay.classList.add(`convergence-${convergenceData.dominantSignal}`);
        
        // Add enhanced convergence indicator
        circle.classList.add('enhanced-convergence-indicator', 'active');
      }
    });
    
    this.activeOverlays.set(indicator, convergenceData);
  }

  deactivateConvergenceOverlay(indicator) {
    const circles = document.querySelectorAll(`.signal-circle[data-indicator="${indicator}"]`);
    circles.forEach(circle => {
      const overlay = circle.querySelector('.convergence-overlay');
      if (overlay) {
        overlay.classList.remove('active', 'convergence-buy', 'convergence-sell', 'convergence-neutral');
      }
      
      circle.classList.remove('enhanced-convergence-indicator', 'active');
    });
    
    this.activeOverlays.delete(indicator);
  }

  applyLightLogicStyle(style) {
    console.log(`[EnhancedLightLogicOverlays] Applying light logic style: ${style}`);
    
    this.overlaySettings.lightLogicStyle = style;
    
    const lightLayers = document.querySelectorAll('.light-logic-layer');
    lightLayers.forEach(layer => {
      // Remove all style classes
      layer.classList.remove('conservative', 'wallstreet', 'vibeflow', 'cosmic', 'chaos');
      // Add current style
      layer.classList.add(style);
    });
    
    // Apply background effects based on style
    this.applyBackgroundEffects(style);
  }

  applyBackgroundEffects(style) {
    const body = document.body;
    
    // Remove existing background classes
    body.classList.remove('bg-convergence-buy', 'bg-convergence-sell', 'bg-convergence-neutral');
    
    // Apply style-specific background effects
    const dominantSignal = this.getDominantMarketSignal();
    if (dominantSignal !== 'neutral') {
      body.classList.add(`bg-convergence-${dominantSignal}`);
      
      if (style === 'cosmic' || style === 'chaos') {
        body.classList.add('convergence-pulse');
      }
    }
  }

  getDominantMarketSignal() {
    let totalBuy = 0;
    let totalSell = 0;
    
    this.convergenceData.forEach(data => {
      totalBuy += data.buyCount;
      totalSell += data.sellCount;
    });
    
    if (totalBuy > totalSell * 1.2) return 'buy';
    if (totalSell > totalBuy * 1.2) return 'sell';
    return 'neutral';
  }

  enableDegenismMode(enabled = true) {
    console.log(`[EnhancedLightLogicOverlays] ${enabled ? 'Enabling' : 'Disabling'} degenism mode`);
    
    this.overlaySettings.enableDegenismMode = enabled;
    
    const signalCircles = document.querySelectorAll('.signal-circle');
    signalCircles.forEach(circle => {
      let degenOverlay = circle.querySelector('.degenism-overlay');
      
      if (enabled && !degenOverlay) {
        degenOverlay = document.createElement('div');
        degenOverlay.className = 'degenism-overlay';
        circle.appendChild(degenOverlay);
      }
      
      if (degenOverlay) {
        degenOverlay.classList.toggle('degen-mode', enabled);
      }
    });
  }

  setupEventListeners() {
    // Listen for strategy changes
    document.addEventListener('strategyChanged', (event) => {
      this.handleStrategyChange(event.detail);
    });
    
    // Listen for signal updates
    document.addEventListener('signalUpdated', (event) => {
      this.handleSignalUpdate(event.detail);
    });
    
    // Listen for light logic changes
    document.addEventListener('lightLogicChanged', (event) => {
      this.applyLightLogicStyle(event.detail.style);
    });
    
    // Setup menu interaction fixes
    this.setupMenuInteractionFixes();
  }

  setupMenuInteractionFixes() {
    // Ensure menus don't interfere with each other
    const menuButtons = document.querySelectorAll('[id*="Button"]');
    menuButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        // Ensure button remains interactive
        button.style.zIndex = '150';
        button.style.pointerEvents = 'auto';
      });
    });
    
    // Fix threshold slider interactions
    const sliders = document.querySelectorAll('.threshold-slider, .enhanced-slider');
    sliders.forEach(slider => {
      slider.addEventListener('mousedown', (e) => {
        e.stopPropagation();
        slider.style.zIndex = '150';
      });
    });
  }

  applyInitialOverlays() {
    // Apply default light logic style
    this.applyLightLogicStyle(this.overlaySettings.lightLogicStyle);
    
    // Setup structured light layers
    if (this.overlaySettings.enableStructuredLights) {
      this.setupStructuredLightLayers();
    }
  }

  setupStructuredLightLayers() {
    const signalCircles = document.querySelectorAll('.signal-circle');
    signalCircles.forEach(circle => {
      circle.classList.add('structured-light-container');
      
      // Add three light layers
      for (let i = 1; i <= 3; i++) {
        if (!circle.querySelector(`.light-layer-${i}`)) {
          const layer = document.createElement('div');
          layer.className = `light-layer-${i}`;
          circle.appendChild(layer);
        }
      }
    });
  }

  handleStrategyChange(strategyData) {
    console.log('[EnhancedLightLogicOverlays] Handling strategy change:', strategyData);
    
    // Reset overlays for new strategy
    this.resetAllOverlays();
    
    // Apply strategy-specific overlay settings
    if (strategyData.name === 'Admiral T.O.A.') {
      this.enableDegenismMode(true);
      this.applyLightLogicStyle('cosmic');
    } else {
      this.enableDegenismMode(false);
      this.applyLightLogicStyle('conservative');
    }
  }

  handleSignalUpdate(signalData) {
    // Trigger convergence detection on signal updates
    setTimeout(() => {
      this.detectConvergence();
    }, 100);
  }

  resetAllOverlays() {
    // Clear all active overlays
    this.activeOverlays.clear();
    this.convergenceData.clear();
    
    // Remove overlay classes
    document.querySelectorAll('.convergence-overlay').forEach(overlay => {
      overlay.classList.remove('active', 'convergence-buy', 'convergence-sell', 'convergence-neutral');
    });
    
    document.querySelectorAll('.enhanced-convergence-indicator').forEach(indicator => {
      indicator.classList.remove('active');
    });
  }

  updateSettings(newSettings) {
    console.log('[EnhancedLightLogicOverlays] Updating settings:', newSettings);
    
    Object.assign(this.overlaySettings, newSettings);
    
    // Apply updated settings
    if (newSettings.lightLogicStyle) {
      this.applyLightLogicStyle(newSettings.lightLogicStyle);
    }
    
    if (newSettings.enableDegenismMode !== undefined) {
      this.enableDegenismMode(newSettings.enableDegenismMode);
    }
  }

  destroy() {
    console.log('[EnhancedLightLogicOverlays] Destroying overlay system...');
    
    if (this.convergenceMonitor) {
      clearInterval(this.convergenceMonitor);
    }
    
    this.resetAllOverlays();
    
    // Remove overlay containers
    const container = document.getElementById('enhanced-overlays-container');
    if (container) {
      container.remove();
    }
    
    this.isInitialized = false;
  }
}

// Initialize enhanced light logic overlays
window.enhancedLightLogicOverlays = new EnhancedLightLogicOverlays();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedLightLogicOverlays;
}
