// Function to initialize TradingView widget once Datafeeds is available
function initTradingViewWidget(targetSymbol, targetInterval, studies, logMessages, updateLogger) {
  if (typeof Datafeeds === 'undefined') {
    // If Datafeeds is not defined yet, wait and try again
    setTimeout(() => initTradingViewWidget(targetSymbol, targetInterval, studies, logMessages, updateLogger), 100);
    return;
  }

  try {
    const widgetOptions = {
      symbol: targetSymbol,
      interval: targetInterval,
      container_id: 'tradingview_candle',
      width: '100%',
      height: '100%',
      locale: 'en',
      theme: document.body.classList.contains('dark-mode') ? 'dark' : 'light',
      style: '1',
      toolbar_bg: '#1a1a2a',
      enable_publishing: false,
      allow_symbol_change: true,
      hide_side_toolbar: false,
      studies: studies,
      autosize: true,
      datafeed: new Datafeeds.UDFCompatibleDatafeed("https://demo-feed-data.tradingview.com"),
      library_path: 'js/vendor/tradingview/charting_library/'
    };

    if (window.StarCrypt.tradingViewManager.chart && typeof window.StarCrypt.tradingViewManager.chart.remove === 'function') {
      window.StarCrypt.tradingViewManager.chart.remove();
    }

    window.StarCrypt.tradingViewManager.chart = new TradingView.widget(widgetOptions);
    const chart = window.StarCrypt.tradingViewManager.chart;
    
    chart.onChartReady(() => {
      logMessages.push(`[${new Date().toLocaleString()}] New TradingView chart ready for ${targetSymbol} on ${targetInterval}.`);
      if (window.StarCrypt && window.StarCrypt.signalSystem && typeof window.StarCrypt.signalSystem.setTradingViewChart === 'function') {
        window.StarCrypt.signalSystem.setTradingViewChart(chart);
      } else {
        console.warn('[TradingViewInit] SignalSystem or setTradingViewChart not available on window.StarCrypt.signalSystem.');
      }
      updateLogger();
    });
  } catch (error) {
    console.error('Error initializing TradingView widget:', error);
    logMessages.push(`[${new Date().toLocaleString()}] Error initializing TradingView: ${error.message}`);
    updateLogger();
  }
}

// Export the function for use in index.html
window.initTradingViewWidget = initTradingViewWidget;
