/* Animations and overlay styles extracted from index.html */
#loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(10, 10, 26, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  transition: opacity 0.5s ease-in-out;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 5px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #00FFFF;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
  animation: pulse 1.5s infinite alternate;
}

.loading-progress {
  width: 300px;
  height: 10px;
  background-color: rgba(0, 255, 255, 0.2);
  border-radius: 5px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #0088FF, #00FFFF);
  border-radius: 5px;
  transition: width 0.3s ease-in-out;
}

.strategy-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(10, 10, 26, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.strategy-animation-content {
  background: rgba(0, 10, 30, 0.8);
  border: 2px solid #00FFFF;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  max-width: 80%;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.strategy-animation-content h2 {
  color: #00FFFF;
  font-size: 2rem;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
}

.strategy-animation-icon {
  font-size: 3rem;
  margin: 20px 0;
}

.strategy-animation-icon i {
  animation: spin 2s linear infinite;
}

/* Golden Pulse Glow for Active Timeframe */
.golden-pulse-glow {
  position: relative;
  z-index: 2;
  animation: goldenPulse 1.5s infinite ease-in-out;
  box-shadow: 0 0 10px 3px rgba(255, 215, 0, 0.7) !important;
  border: 2px solid #FFD700 !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.strategy-indicators-preview {
  margin: 20px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.indicator-preview {
  padding: 5px 10px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.3);
  font-weight: bold;
}

@keyframes indicatorPulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.strategy-animation-progress {
  width: 100%;
  height: 10px;
  background-color: rgba(0, 255, 255, 0.2);
  border-radius: 5px;
  overflow: hidden;
  margin-top: 20px;
}

.strategy-animation-progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #0088FF, #00FFFF);
  border-radius: 5px;
  transition: width 1.5s ease-in-out;
}

@keyframes goldenPulse {
  0% {
    box-shadow: 0 0 5px 2px rgba(255, 215, 0, 0.7), 0 0 7px 3px rgba(255, 223, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 10px 4px rgba(255, 215, 0, 1), 0 0 15px 6px rgba(255, 223, 0, 0.7);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 15px 5px rgba(255, 215, 0, 1), 0 0 25px 10px rgba(255, 223, 0, 0.7);
    transform: scale(1.05);
  }
}
