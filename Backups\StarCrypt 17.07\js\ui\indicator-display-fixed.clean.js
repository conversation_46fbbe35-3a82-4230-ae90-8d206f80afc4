/* =========================================================
   StarCrypt Indicator Display System - CLEAN IMPLEMENTATION
   This implementation fixes the following issues:
   1. Prevents [object Object] in the UI
   2. Ensures exactly 7 signal lights per indicator row
   3. <PERSON><PERSON><PERSON> handles neutral state (always gray, never blank)
   4. Prevents memory leaks and duplicate event handlers
   5. Optimizes performance with controlled refresh rates
 ========================================================= */

// Global tracking for initialization
window.indicatorsInitialized = false;
window.indicatorUpdateInterval = null;
window.indicatorCharts = {};

/**
 * Get proper signal color with 5-color logic
 * @param {string} signal - The signal type ('buy', 'sell', 'neutral')
 * @param {number} strength - Signal strength (0-1)
 * @returns {string} CSS color value
 */
function getSignalColor(signal, strength = 0.5) {
  // Force neutral to be gray, never blank
  if (!signal || signal === 'neutral') {
    return '#808080'; // Medium gray for neutral
  }

  // 5-color logic implementation
  if (signal === 'buy') {
    return strength > 0.6 ? '#00FF00' : '#00AAFF'; // Strong vs mild buy
  } else if (signal === 'sell') {
    return strength > 0.6 ? '#FF0000' : '#FFA500'; // Strong vs mild sell
  }

  // Fallback
  return '#808080';
}

/**
 * Create signal lights for all indicators
 */
function createAllIndicatorLights() {
  console.log('[IndicatorDisplay] Creating signal lights for all indicators');

  // Get all required indicators based on current strategy
  const indicators = getAllRequiredIndicators();
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

  // Process each indicator row
  indicators.forEach(indicator => {
    try {
      // Skip invalid indicators
      if (typeof indicator !== 'string') {
        console.error('[IndicatorDisplay] Invalid indicator:', indicator);
        return;
      }

      // Find or create the indicator row (this now includes the lights container)
      const row = getOrCreateIndicatorRow(indicator);
      if (!row) return;

      // Get the lights container (should already exist from getOrCreateIndicatorRow)
      const lightsContainer = row.querySelector('.signal-lights-container');
      if (!lightsContainer) {
        console.error(`[IndicatorDisplay] Lights container not found for ${indicator}`);
        return;
      }

      // Clear existing lights to prevent duplicates
      lightsContainer.innerHTML = '';

      // Create 7 lights (one for each timeframe)
      timeframes.forEach(tf => {
        const light = document.createElement('div');
        light.className = 'signal-circle neutral';

        // Data attributes for signal light identification
        light.setAttribute('data-indicator', indicator);
        light.setAttribute('data-timeframe', tf);
        light.id = `${indicator}-${tf}-signal`;
        
        // Tooltip and accessibility
        light.setAttribute('data-tooltip', `${indicator.toUpperCase()} (${tf}): No data`);
        light.setAttribute('aria-label', `${indicator} ${tf} signal`);
        light.setAttribute('role', 'status');
        
        // Add animation class for visual feedback
        light.classList.add('pulse');
        
        // Add to container
        lightsContainer.appendChild(light);
      });
      
      console.log(`[IndicatorDisplay] Created signal lights for ${indicator}`);
      
    } catch (error) {
      console.error(`[IndicatorDisplay] Error creating lights for ${indicator}:`, error);
    }
  });
}

/**
 * Create or get indicator row with proper structure
 * @param {string} indicator - The indicator name
 * @returns {HTMLElement|null} The indicator row element or null if not found/created
 */
function getOrCreateIndicatorRow(indicator) {
  // Find momentum indicators container
  const momentumIndicators = document.getElementById('momentum-indicators');
  if (!momentumIndicators) {
    console.error('[IndicatorDisplay] Momentum indicators container not found');
    return null;
  }

  // Try to find existing row
  let row = document.querySelector(`.indicator-row[data-indicator="${indicator}"]`);
  
  // Create new row if not found
  if (!row) {
    // Create main row container
    row = document.createElement('div');
    row.className = 'indicator-row';
    row.setAttribute('data-indicator', indicator);
    
    // Create indicator name cell with tooltip
    const nameCell = document.createElement('div');
    nameCell.className = 'indicator-name';
    nameCell.textContent = indicator.toUpperCase();
    nameCell.setAttribute('data-tooltip', `View ${indicator} details`);
    row.appendChild(nameCell);
    
    // Create mini-chart container
    const chartCell = document.createElement('div');
    chartCell.className = 'indicator-chart';
    
    // Create canvas for the mini-chart
    const chartCanvas = document.createElement('canvas');
    chartCanvas.id = `${indicator}-chart`;
    chartCanvas.width = 120;
    chartCanvas.height = 40;
    chartCell.appendChild(chartCanvas);
    row.appendChild(chartCell);
    
    // Create signal lights container
    const lightsCell = document.createElement('div');
    lightsCell.className = 'signal-lights-cell';
    
    // Create container for all lights
    const lightsContainer = document.createElement('div');
    lightsContainer.className = 'signal-lights-container';
    lightsCell.appendChild(lightsContainer);
    
    // Add lights container to row
    row.appendChild(lightsCell);
    
    // Add row to container (insert before ML analysis if it exists)
    const mlAnalysis = momentumIndicators.querySelector('.ml-analysis-container');
    if (mlAnalysis) {
      momentumIndicators.insertBefore(row, mlAnalysis);
    } else {
      momentumIndicators.appendChild(row);
    }
    
    console.log(`[IndicatorDisplay] Created new row for ${indicator}`);
  }
  
  return row;
}

/**
 * Create mini charts for all indicators
 */
function createAllMiniCharts() {
  console.log('[IndicatorDisplay] Creating mini charts for all indicators');

  // Get all required indicators based on current strategy
  const indicators = getAllRequiredIndicators();

  // Process each indicator
  indicators.forEach(indicator => {
    try {
      // Skip invalid indicators
      if (typeof indicator !== 'string') {
        console.error('[IndicatorDisplay] Invalid indicator:', indicator);
        return;
      }

      // Find or create the indicator row (this now includes the chart container)
      const row = getOrCreateIndicatorRow(indicator);
      if (!row) return;

      // Get the chart container (should already exist from getOrCreateIndicatorRow)
      const chartCell = row.querySelector('.indicator-chart');
      if (!chartCell) {
        console.error(`[IndicatorDisplay] Chart container not found for ${indicator}`);
        return;
      }

      // Ensure we have a canvas element
      let chartCanvas = chartCell.querySelector('canvas');
      if (!chartCanvas) {
        chartCanvas = document.createElement('canvas');
        chartCanvas.id = `${indicator}-chart`;
        chartCanvas.width = 120;
        chartCanvas.height = 40;
        chartCell.innerHTML = ''; // Clear any existing content
        chartCell.appendChild(chartCanvas);
      }

      // Create the actual chart
      createMiniChart(indicator);
      
      console.log(`[IndicatorDisplay] Created mini chart for ${indicator}`);
      
    } catch (error) {
      console.error(`[IndicatorDisplay] Error creating mini chart for ${indicator}:`, error);
    }
  });
}

/**
 * Create mini chart for a specific indicator
 * @param {string} indicator - The indicator name
 * @param {HTMLElement} [row] - Optional parent row element
 */
function createMiniChart(indicator, row) {
  console.log(`[IndicatorDisplay] Creating mini chart for ${indicator}`);
  
  try {
    // Find or create the indicator row if not provided
    if (!row) {
      row = getOrCreateIndicatorRow(indicator);
      if (!row) {
        console.error(`[IndicatorDisplay] Could not find or create row for ${indicator}`);
        return null;
      }
    }
    
    // Find or create chart container
    let chartContainer = row.querySelector('.indicator-chart');
    if (!chartContainer) {
      console.warn(`[IndicatorDisplay] No chart container found for ${indicator}, creating one`);
      chartContainer = document.createElement('div');
      chartContainer.className = 'indicator-chart';
      row.insertBefore(chartContainer, row.querySelector('.signal-lights-cell'));
    }
    
    // Clear existing chart if any
    chartContainer.innerHTML = '';
    
    // Create canvas with exact dimensions
    const canvas = document.createElement('canvas');
    canvas.id = `${indicator}-chart`;
    canvas.width = 207;
    canvas.height = 58;
    canvas.style.display = 'block';
    canvas.style.width = '207px';
    canvas.style.height = '58px';
    
    // Create value display
    const valueDisplay = document.createElement('div');
    valueDisplay.className = 'indicator-value';
    valueDisplay.style.position = 'absolute';
    valueDisplay.style.top = '4px';
    valueDisplay.style.right = '8px';
    valueDisplay.style.fontSize = '10px';
    valueDisplay.style.fontFamily = 'monospace';
    valueDisplay.style.fontWeight = 'bold';
    valueDisplay.style.color = '#ffffff';
    valueDisplay.style.textShadow = '0 0 2px #000';
    valueDisplay.textContent = '--';
    
    // Create wrapper for proper positioning
    const wrapper = document.createElement('div');
    wrapper.style.position = 'relative';
    wrapper.style.width = '207px';
    wrapper.style.height = '58px';
    wrapper.appendChild(canvas);
    wrapper.appendChild(valueDisplay);
    
    chartContainer.appendChild(wrapper);
    
    // Initialize chart with sample data (will be updated with real data)
    const ctx = canvas.getContext('2d');
    const chart = {
      canvas,
      ctx,
      data: [],
      maxPoints: 30,
      currentValue: 0,
      lastUpdate: 0,
      
      // Generate sample data
      generateSampleData() {
        const data = [];
        for (let i = 0; i < this.maxPoints; i++) {
          data.push(Math.random() * 100);
        }
        return data;
      },
      
      // Update chart with new data point
      update(value) {
        if (value === undefined || value === null) return;
        
        // Add new data point
        this.data.push(parseFloat(value));
        this.currentValue = value;
        
        // Trim data to max points
        if (this.data.length > this.maxPoints) {
          this.data.shift();
        }
        
        // Update display
        this.render();
      },
      
      // Render the chart
      render() {
        const { ctx, canvas } = this;
        const width = canvas.width;
        const height = canvas.height;
        const data = this.data;
        const len = data.length;
        
        if (len < 2) return;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Calculate min/max for scaling
        let min = Math.min(...data);
        let max = Math.max(...data);
        const range = Math.max(1, max - min);
        
        // Add some padding to the range
        min -= range * 0.1;
        max += range * 0.1;
        
        // Scale function
        const scaleY = (val) => height - ((val - min) / (max - min)) * height;
        const scaleX = (i) => (i / (this.maxPoints - 1)) * width;
        
        // Draw grid lines
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        
        // Draw horizontal grid lines
        for (let y = 0; y <= 1; y += 0.25) {
          const yPos = y * height;
          ctx.beginPath();
          ctx.moveTo(0, yPos);
          ctx.lineTo(width, yPos);
          ctx.stroke();
        }
        
        // Create gradient for line
        const lineGradient = ctx.createLinearGradient(0, 0, 0, height);
        const isBullish = this.currentValue > (data[len - 2] || 0);
        
        if (isBullish) {
          lineGradient.addColorStop(0, '#00ff00');
          lineGradient.addColorStop(1, '#006400');
          valueDisplay.style.color = '#00ff00';
        } else {
          lineGradient.addColorStop(0, '#ff0000');
          lineGradient.addColorStop(1, '#8b0000');
          valueDisplay.style.color = '#ff0000';
        }
        
        // Update value display
        valueDisplay.textContent = this.currentValue.toFixed(2);
        
        // Draw line
        ctx.beginPath();
        ctx.moveTo(0, scaleY(data[0]));
        
        for (let i = 1; i < len; i++) {
          ctx.lineTo(scaleX(i), scaleY(data[i]));
        }
        
        ctx.strokeStyle = lineGradient;
        ctx.lineWidth = 1.5;
        ctx.lineJoin = 'round';
        ctx.stroke();
        
        // Create gradient for fill
        const fillGradient = ctx.createLinearGradient(0, 0, 0, height);
        fillGradient.addColorStop(0, isBullish ? 'rgba(0, 255, 0, 0.2)' : 'rgba(255, 0, 0, 0.2)');
        fillGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
        
        // Fill area under line
        ctx.lineTo(scaleX(len - 1), height);
        ctx.lineTo(0, height);
        ctx.closePath();
        ctx.fillStyle = fillGradient;
        ctx.fill();
      }
    };
    
    // Initialize with sample data
    chart.data = chart.generateSampleData();
    chart.render();
    
    // Store chart instance for updates
    if (!window.indicatorCharts) window.indicatorCharts = {};
    window.indicatorCharts[indicator] = chart;
    
    // Return the chart instance for external updates
    return chart;
    
  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating mini chart for ${indicator}:`, error);
    return null;
  }
}

/**
 * Get all required indicators for the current strategy
 * @returns {string[]} Array of indicator names
 */
function getAllRequiredIndicators() {
  try {
    // Get current strategy from StrategyManager or use default
    const strategy = window.StrategyManager?.currentStrategy || 'admiral_toa';
    
    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[strategy];
    if (!strategyDetails) {
      console.warn(`[IndicatorDisplay] Strategy not found: ${strategy}`);
      return window.enabledIndicators || [];
    }

    // Return strategy indicators or fallback to default enabled indicators
    return strategyDetails.indicators || window.enabledIndicators || [];
  } catch (error) {
    console.error('[IndicatorDisplay] Error getting required indicators:', error);
    return [];
  }
}

/**
 * Update all indicators
 */
function updateAllIndicators() {
  if (!window.indicatorsInitialized) return;

  try {
    console.log('[IndicatorDisplay] Updating all indicators');

    // Get all required indicators based on current strategy
    const indicators = getAllRequiredIndicators();
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

    // Update each indicator for each timeframe
    indicators.forEach(indicator => {
      timeframes.forEach(timeframe => {
        updateIndicator(indicator, timeframe);
      });
    });

    console.log('[IndicatorDisplay] All indicators updated');
  } catch (error) {
    console.error('[IndicatorDisplay] Error updating indicators:', error);
  }
}

/**
 * Update a specific indicator for a specific timeframe
 * @param {string} indicator - The indicator name
 * @param {string} timeframe - The timeframe (e.g., '1m', '1h')
 */
function updateIndicator(indicator, timeframe) {
  try {
    // Get indicator data
    const data = getIndicatorData(indicator, timeframe);
    
    // Update signal light
    updateSignalLight(indicator, timeframe, data);
    
    // Update chart if it exists
    const chart = window.indicatorCharts[`${indicator}-${timeframe}`];
    if (chart) {
      updateChart(indicator, data);
    }
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating ${indicator} (${timeframe}):`, error);
  }
}

/**
 * Cleanup existing intervals and handlers to prevent memory leaks
 */
function cleanupExistingHandlers() {
  // Clear any existing intervals
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval);
    window.indicatorUpdateInterval = null;
  }

  // Remove any existing event listeners
  document.removeEventListener('strategyChanged', handleStrategyChange);
  
  // Reset initialization flag
  window.indicatorsInitialized = false;
}

/**
 * Handle strategy change events
 */
function handleStrategyChange() {
  console.log('[IndicatorDisplay] Strategy changed, updating indicators');
  
  // Clean up existing indicators
  cleanupExistingHandlers();
  
  // Re-initialize with new strategy
  initializeIndicatorDisplay();
}

/**
 * Initialize the indicator display system
 */
function initializeIndicatorDisplay() {
  try {
    console.log('[IndicatorDisplay] Initializing indicator display system');

    // Clean up any existing handlers to prevent duplicates
    cleanupExistingHandlers();

    // Delay to ensure all necessary components are loaded
    setTimeout(() => {
      // Create indicator rows and signal lights
      createAllIndicatorLights();

      // Create mini charts
      createAllMiniCharts();

      // Set up update interval - store reference for cleanup
      window.indicatorUpdateInterval = setInterval(updateAllIndicators, 5000);

      // Mark as initialized
      window.indicatorsInitialized = true;

      // Do initial update
      updateAllIndicators();

      // Add strategy change listener
      document.addEventListener('strategyChanged', handleStrategyChange);

      console.log('[IndicatorDisplay] Indicator display system initialized');
    }, 1000);
  } catch (error) {
    console.error('[IndicatorDisplay] Error initializing indicator display:', error);
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
  // Check if StrategyManager is available
  if (window.StrategyManager) {
    // Initialize when StrategyManager is ready
    if (window.StrategyManager.isInitialized) {
      initializeIndicatorDisplay();
    } else {
      window.StrategyManager.on('initialized', initializeIndicatorDisplay);
    }
  } else {
    // Fallback: Initialize directly if StrategyManager is not available
    console.log('[IndicatorDisplay] Initializing without StrategyManager');
    initializeIndicatorDisplay();
  }
});
