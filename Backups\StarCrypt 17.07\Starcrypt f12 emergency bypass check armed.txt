// 🔧 SCOTTIE'S EMERGENCY THERMAL BYPASS
function emergencyThermalBypass() {
  console.log('🔧 SCOTTIE: EMERGENCY THERMAL BYPASS INITIATED!');
  
  // FORCE HEAT UP THE ML REACTOR
  const mlSystem = window.MLHistoricalAnalysis || window.mlHistoricalAnalysis;
  if (mlSystem) {
    // EMERGENCY REACTOR RESTART
    mlSystem.selectedLights = new Set();
    
    // FORCE INJECT THERMAL ENERGY
    const selectedLights = document.querySelectorAll('.signal-circle.selected, .signal-light.selected');
    selectedLights.forEach((light, index) => {
      const indicator = light.getAttribute('data-ind') || light.getAttribute('data-indicator') || `signal${index}`;
      const timeframe = light.getAttribute('data-tf') || light.getAttribute('data-timeframe') || '1h';
      mlSystem.selectedLights.add(`${indicator}-${timeframe}`);
      console.log(`🔧 THERMAL INJECTION: ${indicator}-${timeframe}`);
    });
    
    console.log(`🔧 REACTOR TEMPERATURE: ${mlSystem.selectedLights.size} thermal units`);
    
    // FORCE ANALYZE BUTTON ACTIVATION
    const analyzeBtn = document.getElementById('analyzeConvergence');
    if (analyzeBtn) {
      analyzeBtn.style.background = 'linear-gradient(45deg, #ff0000, #ffff00)';
      analyzeBtn.style.boxShadow = '0 0 30px #ff0000';
      analyzeBtn.style.transform = 'scale(1.1)';
      console.log('🔧 ANALYZE BUTTON: EMERGENCY POWER RESTORED');
    }
    
    return mlSystem.selectedLights.size;
  }
  return 0;
}

emergencyThermalBypass();