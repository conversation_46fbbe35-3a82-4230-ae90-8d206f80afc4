<svg width="300" height="80" viewBox="0 0 300 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions -->
    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00FFFF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0080FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00FFFF;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00FFFF;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00FFFF;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#0080FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00FFFF;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00FFFF;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#0080FF;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#000040;stop-opacity:0" />
    </radialGradient>
    
    <!-- Filter for glow effect -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Animation for pulsing -->
    <animate id="pulse" attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Background glow -->
  <ellipse cx="150" cy="40" rx="140" ry="35" fill="url(#glowGradient)" opacity="0.3"/>
  
  <!-- Star symbol -->
  <g transform="translate(20, 40)">
    <!-- Central star -->
    <polygon points="0,-15 4,-4 15,-4 6,2 10,13 0,7 -10,13 -6,2 -15,-4 -4,-4" 
             fill="url(#starGradient)" 
             filter="url(#glow)"
             opacity="0.9">
      <animateTransform attributeName="transform" 
                        type="rotate" 
                        values="0;360" 
                        dur="20s" 
                        repeatCount="indefinite"/>
    </polygon>
    
    <!-- Orbiting particles -->
    <circle cx="20" cy="0" r="2" fill="#00FFFF" opacity="0.8">
      <animateTransform attributeName="transform" 
                        type="rotate" 
                        values="0;360" 
                        dur="3s" 
                        repeatCount="indefinite"/>
    </circle>
    
    <circle cx="-20" cy="0" r="1.5" fill="#0080FF" opacity="0.6">
      <animateTransform attributeName="transform" 
                        type="rotate" 
                        values="360;0" 
                        dur="4s" 
                        repeatCount="indefinite"/>
    </circle>
    
    <circle cx="0" cy="20" r="1" fill="#FFFFFF" opacity="0.7">
      <animateTransform attributeName="transform" 
                        type="rotate" 
                        values="0;360" 
                        dur="5s" 
                        repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- STAR text -->
  <text x="60" y="35" 
        font-family="Orbitron, Arial, sans-serif" 
        font-size="24" 
        font-weight="bold" 
        fill="url(#textGradient)" 
        filter="url(#glow)">STAR</text>
  
  <!-- CRYPT text -->
  <text x="60" y="55" 
        font-family="Orbitron, Arial, sans-serif" 
        font-size="24" 
        font-weight="bold" 
        fill="url(#textGradient)" 
        filter="url(#glow)">CRYPT</text>
  
  <!-- Subtitle -->
  <text x="180" y="35" 
        font-family="Roboto, Arial, sans-serif" 
        font-size="10" 
        fill="#00FFFF" 
        opacity="0.8">TRADING</text>
  
  <text x="180" y="47" 
        font-family="Roboto, Arial, sans-serif" 
        font-size="10" 
        fill="#00FFFF" 
        opacity="0.8">INTELLIGENCE</text>
  
  <text x="180" y="59" 
        font-family="Roboto, Arial, sans-serif" 
        font-size="10" 
        fill="#00FFFF" 
        opacity="0.8">PLATFORM</text>
  
  <!-- Decorative elements -->
  <line x1="170" y1="20" x2="170" y2="60" stroke="#00FFFF" stroke-width="1" opacity="0.5"/>
  
  <!-- Small stars -->
  <polygon points="250,-2 252,1 255,1 253,3 254,6 250,4 246,6 247,3 245,1 248,1" 
           fill="#00FFFF" 
           opacity="0.6"
           transform="translate(0, 25)">
    <animate attributeName="opacity" values="0.3;0.9;0.3" dur="3s" repeatCount="indefinite"/>
  </polygon>
  
  <polygon points="270,-1 271,0 272,0 271,1 272,2 270,1 268,2 269,1 268,0 269,0" 
           fill="#0080FF" 
           opacity="0.7"
           transform="translate(0, 45)">
    <animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite"/>
  </polygon>
  
  <polygon points="280,-1 281,0 282,0 281,1 282,2 280,1 278,2 279,1 278,0 279,0" 
           fill="#FFFFFF" 
           opacity="0.5"
           transform="translate(0, 15)">
    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="4s" repeatCount="indefinite"/>
  </polygon>
</svg>
