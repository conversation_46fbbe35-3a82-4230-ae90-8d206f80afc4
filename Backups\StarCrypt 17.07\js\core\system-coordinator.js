/**
 * StarCrypt System Coordinator
 * Ensures all systems work in perfect harmony
 * Coordinates data flow, prevents conflicts, and optimizes performance
 */

class SystemCoordinator {
  constructor() {
    this.systems = new Map();
    this.dataFlow = new Map();
    this.eventQueue = [];
    this.processingQueue = false;
    this.coordinationActive = false;
    
    this.init();
  }

  init() {
    console.log('🎯 SYSTEM COORDINATOR: Initializing master coordination system...');
    
    this.registerSystems();
    this.setupDataFlow();
    this.startCoordination();
    
    console.log('✅ SYSTEM COORDINATOR: All systems coordinated and optimized');
  }

  registerSystems() {
    // Register all StarCrypt systems with their dependencies and data requirements
    this.systems.set('websocket', {
      name: 'WebSocket Data Provider',
      dependencies: [],
      provides: ['indicatorData', 'priceData', 'strategyData'],
      priority: 1,
      status: 'active'
    });

    this.systems.set('signalLights', {
      name: 'Signal Lights System',
      dependencies: ['websocket'],
      provides: ['signalStates', 'signalClicks'],
      priority: 2,
      status: 'active'
    });

    this.systems.set('unifiedCommander', {
      name: 'Unified Signal Commander',
      dependencies: ['signalLights'],
      provides: ['signalSelection', 'admiralMode'],
      priority: 3,
      status: 'active'
    });

    this.systems.set('mlSystems', {
      name: 'ML Analysis Systems',
      dependencies: ['websocket', 'unifiedCommander'],
      provides: ['predictions', 'analysis'],
      priority: 4,
      status: 'active'
    });

    this.systems.set('starfield', {
      name: 'Starfield Animation',
      dependencies: ['mlSystems'],
      provides: ['visualEffects'],
      priority: 5,
      status: 'active'
    });

    this.systems.set('tooltips', {
      name: 'Tooltip System',
      dependencies: ['signalLights', 'websocket'],
      provides: ['userFeedback'],
      priority: 6,
      status: 'active'
    });
  }

  setupDataFlow() {
    // Define how data flows between systems
    this.dataFlow.set('indicatorData', {
      source: 'websocket',
      targets: ['signalLights', 'mlSystems', 'tooltips'],
      transform: (data) => this.transformIndicatorData(data)
    });

    this.dataFlow.set('signalStates', {
      source: 'signalLights',
      targets: ['unifiedCommander', 'tooltips'],
      transform: (data) => this.transformSignalStates(data)
    });

    this.dataFlow.set('signalSelection', {
      source: 'unifiedCommander',
      targets: ['mlSystems'],
      transform: (data) => this.transformSignalSelection(data)
    });

    this.dataFlow.set('predictions', {
      source: 'mlSystems',
      targets: ['starfield'],
      transform: (data) => this.transformPredictions(data)
    });
  }

  startCoordination() {
    this.coordinationActive = true;
    
    // Set up event coordination
    this.setupEventCoordination();
    
    // Start data flow monitoring
    this.startDataFlowMonitoring();
    
    // Optimize system performance
    this.optimizeSystemPerformance();
    
    console.log('🎯 SYSTEM COORDINATOR: Coordination active');
  }

  setupEventCoordination() {
    // Coordinate WebSocket events
    if (window.ws) {
      const originalOnMessage = window.ws.onmessage;
      window.ws.onmessage = (event) => {
        this.queueEvent('websocket', 'message', JSON.parse(event.data));
        if (originalOnMessage) originalOnMessage(event);
      };
    }

    // Coordinate signal clicks
    document.addEventListener('click', (event) => {
      const signalCircle = event.target.closest('.signal-circle');
      if (signalCircle) {
        const indicator = signalCircle.getAttribute('data-ind') || signalCircle.getAttribute('data-indicator');
        const timeframe = signalCircle.getAttribute('data-tf') || signalCircle.getAttribute('data-timeframe');
        
        if (indicator && timeframe) {
          this.queueEvent('signalLights', 'click', { indicator, timeframe, element: signalCircle });
        }
      }
    });

    // Process event queue
    setInterval(() => {
      this.processEventQueue();
    }, 50); // Process every 50ms
  }

  queueEvent(system, type, data) {
    this.eventQueue.push({
      timestamp: Date.now(),
      system,
      type,
      data,
      processed: false
    });

    // Limit queue size
    if (this.eventQueue.length > 1000) {
      this.eventQueue.shift();
    }
  }

  processEventQueue() {
    if (this.processingQueue || this.eventQueue.length === 0) return;
    
    this.processingQueue = true;
    
    try {
      const eventsToProcess = this.eventQueue.filter(e => !e.processed).slice(0, 10); // Process max 10 events per cycle
      
      for (const event of eventsToProcess) {
        this.processEvent(event);
        event.processed = true;
      }
      
      // Clean up processed events older than 30 seconds
      const cutoff = Date.now() - 30000;
      this.eventQueue = this.eventQueue.filter(e => !e.processed || e.timestamp > cutoff);
      
    } catch (error) {
      console.error('🎯 COORDINATOR: Error processing event queue:', error);
    } finally {
      this.processingQueue = false;
    }
  }

  processEvent(event) {
    try {
      switch (event.system) {
        case 'websocket':
          this.handleWebSocketEvent(event);
          break;
        case 'signalLights':
          this.handleSignalLightEvent(event);
          break;
        default:
          // Generic event handling
          this.handleGenericEvent(event);
      }
    } catch (error) {
      console.error(`🎯 COORDINATOR: Error processing ${event.system} event:`, error);
    }
  }

  handleWebSocketEvent(event) {
    if (event.type === 'message') {
      const data = event.data;
      
      // Coordinate indicator data updates
      if (data.type === 'indicators') {
        this.coordinateIndicatorUpdate(data);
      }
      
      // Coordinate price updates
      if (data.type === 'livePrice') {
        this.coordinatePriceUpdate(data);
      }
    }
  }

  handleSignalLightEvent(event) {
    if (event.type === 'click') {
      // Coordinate signal light clicks across all systems
      this.coordinateSignalClick(event.data);
    }
  }

  handleGenericEvent(event) {
    // Handle other system events
    console.log(`🎯 COORDINATOR: Processing ${event.system} ${event.type} event`);
  }

  coordinateIndicatorUpdate(data) {
    // Ensure all systems receive indicator updates in the correct order
    const { timeframe, data: indicators } = data;
    
    // Update global indicators data
    if (!window.indicatorsData) window.indicatorsData = {};
    if (!window.indicatorsData[timeframe]) window.indicatorsData[timeframe] = {};
    
    Object.assign(window.indicatorsData[timeframe], indicators);
    
    // Notify dependent systems
    this.notifySystemsOfUpdate('indicatorData', { timeframe, indicators });
  }

  coordinatePriceUpdate(data) {
    // Coordinate price updates across all systems
    const price = data.data?.price || data.price;
    if (price && !isNaN(price)) {
      window.currentPrice = price;
      
      // Notify systems that need price data
      this.notifySystemsOfUpdate('priceData', { price });
    }
  }

  coordinateSignalClick(data) {
    // Coordinate signal clicks to prevent conflicts
    const { indicator, timeframe, element } = data;
    
    // Ensure unified commander handles the click
    if (window.unifiedSignalCommander) {
      // Let unified commander handle it
      console.log(`🎯 COORDINATOR: Signal click coordinated - ${indicator}(${timeframe})`);
    }
  }

  notifySystemsOfUpdate(dataType, data) {
    const flow = this.dataFlow.get(dataType);
    if (!flow) return;
    
    const transformedData = flow.transform ? flow.transform(data) : data;
    
    // Notify target systems
    for (const target of flow.targets) {
      this.notifySystem(target, dataType, transformedData);
    }
  }

  notifySystem(systemName, dataType, data) {
    try {
      switch (systemName) {
        case 'signalLights':
          if (window.enhancedMiniCharts) {
            window.enhancedMiniCharts.updateCharts();
          }
          break;
        case 'mlSystems':
          if (window.advancedMLFeatures) {
            window.advancedMLFeatures.generateAdvancedPredictions();
          }
          break;
        case 'starfield':
          if (window.starfieldAnimation && dataType === 'predictions') {
            // Update starfield based on ML predictions
            const signal = data.signal || 'neutral';
            const intensity = data.intensity || 0.5;
            window.starfieldAnimation.updateMLSignal(signal, intensity);
          }
          break;
      }
    } catch (error) {
      console.error(`🎯 COORDINATOR: Error notifying ${systemName}:`, error);
    }
  }

  // Data transformation methods
  transformIndicatorData(data) {
    // Ensure data is in the correct format for all systems
    return {
      timeframe: data.timeframe,
      indicators: data.indicators || data.data,
      timestamp: Date.now()
    };
  }

  transformSignalStates(data) {
    // Transform signal states for consumption by other systems
    return {
      states: data,
      timestamp: Date.now()
    };
  }

  transformSignalSelection(data) {
    // Transform signal selection data for ML systems
    return {
      selectedSignals: Array.from(data.selectedLights || []),
      admiralMode: data.admiralMode || false,
      timestamp: Date.now()
    };
  }

  transformPredictions(data) {
    // Transform ML predictions for visual systems
    return {
      signal: data.signal || 'neutral',
      intensity: data.confidence ? data.confidence / 100 : 0.5,
      timestamp: Date.now()
    };
  }

  startDataFlowMonitoring() {
    setInterval(() => {
      this.monitorDataFlow();
    }, 5000);
  }

  monitorDataFlow() {
    // Monitor data flow health and detect bottlenecks
    const queueSize = this.eventQueue.length;
    const unprocessedEvents = this.eventQueue.filter(e => !e.processed).length;
    
    if (queueSize > 500) {
      console.warn(`🎯 COORDINATOR: High event queue size: ${queueSize}`);
    }
    
    if (unprocessedEvents > 100) {
      console.warn(`🎯 COORDINATOR: High unprocessed events: ${unprocessedEvents}`);
    }
  }

  optimizeSystemPerformance() {
    // Implement performance optimizations
    
    // Throttle high-frequency updates
    this.setupUpdateThrottling();
    
    // Optimize memory usage
    this.setupMemoryOptimization();
  }

  setupUpdateThrottling() {
    // Throttle signal light updates
    let lastSignalUpdate = 0;
    const originalUpdateSignalLights = window.updateAllSignalLights;
    
    if (originalUpdateSignalLights) {
      window.updateAllSignalLights = () => {
        const now = Date.now();
        if (now - lastSignalUpdate > 100) { // Max 10 updates per second
          originalUpdateSignalLights();
          lastSignalUpdate = now;
        }
      };
    }
  }

  setupMemoryOptimization() {
    // Clean up old data periodically
    setInterval(() => {
      // Clean up old event queue entries
      const cutoff = Date.now() - 60000; // Keep last 60 seconds
      this.eventQueue = this.eventQueue.filter(e => e.timestamp > cutoff);
      
      // Clean up old indicator data
      if (window.indicatorsData) {
        for (const timeframe in window.indicatorsData) {
          for (const indicator in window.indicatorsData[timeframe]) {
            const data = window.indicatorsData[timeframe][indicator];
            if (data.timestamp && Date.now() - data.timestamp > 300000) { // 5 minutes old
              delete window.indicatorsData[timeframe][indicator];
            }
          }
        }
      }
    }, 30000); // Clean every 30 seconds
  }

  // Public API
  getSystemStatus() {
    const status = {};
    for (const [id, system] of this.systems) {
      status[id] = {
        name: system.name,
        status: system.status,
        dependencies: system.dependencies,
        provides: system.provides
      };
    }
    return status;
  }

  getEventQueueStatus() {
    return {
      total: this.eventQueue.length,
      unprocessed: this.eventQueue.filter(e => !e.processed).length,
      processing: this.processingQueue
    };
  }
}

// Initialize system coordinator
window.systemCoordinator = new SystemCoordinator();

// Export for global access
window.SystemCoordinator = SystemCoordinator;

console.log('🎯 SYSTEM COORDINATOR: Master coordination system online');
