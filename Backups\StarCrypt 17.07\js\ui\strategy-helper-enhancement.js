/**
 * Strategy Helper Enhancement for StarCrypt
 * Adds comprehensive helper text for strategies missing step-by-step guidance
 */

class StrategyHelperEnhancement {
  constructor() {
    this.missingHelperStrategies = [
      'random_walk',
      'time_warp_scalper', 
      'ml_predictor',
      'deep_learning_diver',
      'ai_pattern_prophet',
      'machine_learning_momentum',
      'sentiment_analysis_surfer',
      'sentiment_blaster',
      'x_sentiment_blaster',
      'vwap_guardian',
      'correlation_hunter',
      'volatility_breakout_hunter',
      'mean_reversion_master',
      'momentum_divergence_spotter',
      'volume_profile_analyzer',
      'multi_timeframe_confluence',
      'ichimoku_cloud_master',
      'volume_flow_hunter',
      'momentum_oscillator_pro',
      'trend_strength_analyzer',
      'institutional_flow_tracker'
    ];
    
    this.init();
  }

  init() {
    console.log('[StrategyHelperEnhancement] Adding missing helper text for strategies...');
    
    try {
      this.addMissingHelperText();
      this.enhanceHelperSystem();
      
      console.log('[StrategyHelperEnhancement] Strategy helper enhancement completed successfully');
    } catch (error) {
      console.error('[StrategyHelperEnhancement] Error enhancing strategy helpers:', error);
    }
  }

  addMissingHelperText() {
    if (!window.TRADING_STRATEGIES) {
      console.error('[StrategyHelperEnhancement] TRADING_STRATEGIES not found');
      return;
    }

    // Add helper text for Random Walk Prototype
    if (window.TRADING_STRATEGIES.random_walk && !window.TRADING_STRATEGIES.random_walk.helperText) {
      window.TRADING_STRATEGIES.random_walk.helperText = `<p><strong>Step 1: Random Element Analysis (<span class="indicator">RSI</span> & <span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">MACD</span> provide base technical analysis while random elements identify market inefficiencies. Confidence: ~60%.</p>
<p><strong>Step 2: Bollinger Band Volatility Check</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> width indicates market volatility. Random walk theory works best in high volatility environments. Confidence: ~65%.</p>
<p><strong>Step 3: ADX Trend Strength Assessment</strong></p>
<p>- <span class="indicator">ADX</span> < 25 indicates sideways market where random walk elements are most effective. Confidence: ~70%.</p>
<p><strong>Step 4: Volume Pattern Recognition</strong></p>
<p>- <span class="indicator">Volume</span> spikes during random events help identify genuine market inefficiencies vs noise. Confidence: ~75%.</p>
<p><strong>Step 5: Williams %R Confirmation</strong></p>
<p>- <span class="indicator">Williams %R</span> extreme readings combined with random elements signal potential opportunities. Final confidence: ~80%.</p>
<p><strong>Tip:</strong> This experimental strategy works best during uncertain market conditions. Use smaller position sizes and tight risk management!</p>`;
    }

    // Add helper text for Time Warp Scalper
    if (window.TRADING_STRATEGIES.time_warp_scalper && !window.TRADING_STRATEGIES.time_warp_scalper.helperText) {
      window.TRADING_STRATEGIES.time_warp_scalper.helperText = `<p><strong>Step 1: Multi-Timeframe RSI Analysis</strong></p>
<p>- <span class="indicator">RSI</span> alignment across 1m, 5m, 15m timeframes. All timeframes showing same direction increases confidence. Confidence: ~70%.</p>
<p><strong>Step 2: Stoch RSI Quick Momentum</strong></p>
<p>- <span class="indicator">Stoch RSI</span> rapid changes across timeframes signal momentum shifts. Look for 1m leading 5m signals. Confidence: ~75%.</p>
<p><strong>Step 3: Bollinger Band Multi-TF Squeeze</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> squeezing on multiple timeframes simultaneously signals explosive breakout potential. Confidence: ~80%.</p>
<p><strong>Step 4: MACD Time Convergence</strong></p>
<p>- <span class="indicator">MACD</span> signals aligning across timeframes create high-probability scalping opportunities. Confidence: ~85%.</p>
<p><strong>Step 5: Williams %R Time Sync</strong></p>
<p>- <span class="indicator">Williams %R</span> showing synchronized extreme readings across timeframes. Confidence: ~88%.</p>
<p><strong>Step 6: Time Anomaly Detection</strong></p>
<p>- <span class="indicator">Time Anomaly</span> indicator identifies unusual patterns across timeframes for unique scalping opportunities. Confidence: ~90%.</p>
<p><strong>Step 7: Volume Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spikes confirming multi-timeframe signals. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Best for 1m-5m scalping when multiple timeframes align. Quick entries and exits required!</p>`;
    }

    // Add helper text for ML Predictor
    if (window.TRADING_STRATEGIES.ml_predictor && !window.TRADING_STRATEGIES.ml_predictor.helperText) {
      window.TRADING_STRATEGIES.ml_predictor.helperText = `<p><strong>Step 1: ML Model Confidence Check</strong></p>
<p>- <span class="indicator">ML</span> prediction confidence >75% indicates high-probability setup. Higher confidence = better trade quality. Confidence: ~80%.</p>
<p><strong>Step 2: RSI Technical Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> must align with ML prediction direction. ML bullish + RSI >50 or ML bearish + RSI <50. Confidence: ~85%.</p>
<p><strong>Step 3: MACD Momentum Alignment</strong></p>
<p>- <span class="indicator">MACD</span> momentum must support ML prediction. Divergence between ML and MACD reduces confidence. Confidence: ~88%.</p>
<p><strong>Step 4: Sentiment Analysis Integration</strong></p>
<p>- <span class="indicator">Sentiment</span> indicator provides market mood context for ML predictions. Aligned sentiment boosts confidence. Confidence: ~90%.</p>
<p><strong>Step 5: Correlation Factor Analysis</strong></p>
<p>- <span class="indicator">Correlation</span> with other assets helps validate ML predictions. Independent moves have higher success rates. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Only trade when ML confidence >75% and at least 2 technical indicators align. ML works best with confirmation!</p>`;
    }

    // Add helper text for Deep Learning Diver
    if (window.TRADING_STRATEGIES.deep_learning_diver && !window.TRADING_STRATEGIES.deep_learning_diver.helperText) {
      window.TRADING_STRATEGIES.deep_learning_diver.helperText = `<p><strong>Step 1: Deep Learning Pattern Recognition</strong></p>
<p>- <span class="indicator">ML</span> deep learning model identifies complex patterns across multiple timeframes. Pattern confidence >80% required. Confidence: ~85%.</p>
<p><strong>Step 2: Multi-Oscillator Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">Stoch RSI</span> must align with deep learning predictions for momentum confirmation. Confidence: ~88%.</p>
<p><strong>Step 3: MACD Deep Analysis</strong></p>
<p>- <span class="indicator">MACD</span> histogram patterns analyzed by deep learning for momentum prediction accuracy. Confidence: ~90%.</p>
<p><strong>Step 4: Advanced Sentiment Integration</strong></p>
<p>- <span class="indicator">Sentiment</span> analysis combined with deep learning provides market psychology insights. Confidence: ~92%.</p>
<p><strong>Step 5: Volume Flow Analysis</strong></p>
<p>- <span class="indicator">Volume</span> patterns analyzed by neural networks to identify institutional activity. Confidence: ~93%.</p>
<p><strong>Step 6: Correlation & Entropy Analysis</strong></p>
<p>- <span class="indicator">Correlation</span> and <span class="indicator">Entropy</span> provide deep market structure insights for prediction enhancement. Final confidence: ~95%.</p>
<p><strong>Tip:</strong> Most advanced AI strategy. Wait for high-confidence signals (>80%) with multiple confirmations. Best for complex market conditions!</p>`;
    }

    // Add helper text for AI Pattern Prophet
    if (window.TRADING_STRATEGIES.ai_pattern_prophet && !window.TRADING_STRATEGIES.ai_pattern_prophet.helperText) {
      window.TRADING_STRATEGIES.ai_pattern_prophet.helperText = `<p><strong>Step 1: AI Pattern Detection</strong></p>
<p>- <span class="indicator">ML</span> identifies recurring chart patterns (triangles, flags, head & shoulders). Pattern completion probability >75% required. Confidence: ~80%.</p>
<p><strong>Step 2: Bollinger Band Pattern Context</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> provide volatility context for pattern formation. Tight bands before pattern completion signal higher success. Confidence: ~83%.</p>
<p><strong>Step 3: MACD Pattern Momentum</strong></p>
<p>- <span class="indicator">MACD</span> momentum must align with pattern direction. Divergence reduces pattern reliability. Confidence: ~86%.</p>
<p><strong>Step 4: ADX Trend Strength Validation</strong></p>
<p>- <span class="indicator">ADX</span> confirms trend strength for directional patterns. ADX >25 increases pattern success rate. Confidence: ~88%.</p>
<p><strong>Step 5: Entropy & Time Anomaly Analysis</strong></p>
<p>- <span class="indicator">Entropy</span> and <span class="indicator">Time Anomaly</span> detect unusual market conditions that enhance pattern reliability. Confidence: ~90%.</p>
<p><strong>Step 6: Fractal Pattern Confirmation</strong></p>
<p>- <span class="indicator">Fractal</span> analysis confirms pattern structure and provides precise entry/exit levels. Confidence: ~92%.</p>
<p><strong>Step 7: Volume Pattern Validation</strong></p>
<p>- <span class="indicator">Volume</span> must confirm pattern with increasing volume on breakouts. Final confidence: ~94%.</p>
<p><strong>Tip:</strong> Wait for pattern completion probability >75% with volume confirmation. Best for swing trading established patterns!</p>`;
    }

    // Add helper text for Machine Learning Momentum
    if (window.TRADING_STRATEGIES.machine_learning_momentum && !window.TRADING_STRATEGIES.machine_learning_momentum.helperText) {
      window.TRADING_STRATEGIES.machine_learning_momentum.helperText = `<p><strong>Step 1: ML Momentum Analysis</strong></p>
<p>- <span class="indicator">ML</span> analyzes momentum patterns across multiple timeframes. Momentum score >75% indicates strong directional bias. Confidence: ~80%.</p>
<p><strong>Step 2: RSI Momentum Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> must align with ML momentum direction. RSI >60 for bullish or <40 for bearish momentum. Confidence: ~83%.</p>
<p><strong>Step 3: MACD Momentum Validation</strong></p>
<p>- <span class="indicator">MACD</span> histogram expansion confirms momentum strength. Expanding histogram = stronger momentum. Confidence: ~86%.</p>
<p><strong>Step 4: Williams %R Momentum Timing</strong></p>
<p>- <span class="indicator">Williams %R</span> provides precise entry timing within momentum trends. Pullbacks to -20/-80 levels ideal. Confidence: ~88%.</p>
<p><strong>Step 5: Volume Momentum Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> >1.5x average confirms institutional momentum participation. Confidence: ~90%.</p>
<p><strong>Step 6: Sentiment & Correlation Analysis</strong></p>
<p>- <span class="indicator">Sentiment</span> and <span class="indicator">Correlation</span> provide momentum sustainability insights. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Best for trending markets with strong momentum. Use ML momentum score >75% as primary filter!</p>`;
    }

    // Add helper text for Sentiment Analysis Surfer
    if (window.TRADING_STRATEGIES.sentiment_analysis_surfer && !window.TRADING_STRATEGIES.sentiment_analysis_surfer.helperText) {
      window.TRADING_STRATEGIES.sentiment_analysis_surfer.helperText = `<p><strong>Step 1: Market Sentiment Analysis</strong></p>
<p>- <span class="indicator">Sentiment</span> indicator measures market mood from social media and news. Extreme sentiment (>70% or <30%) signals potential reversals. Confidence: ~75%.</p>
<p><strong>Step 2: ML Sentiment Validation</strong></p>
<p>- <span class="indicator">ML</span> model validates sentiment signals against historical patterns. ML confidence >70% required. Confidence: ~80%.</p>
<p><strong>Step 3: RSI Sentiment Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> must align with sentiment direction. Bullish sentiment + RSI >50 or bearish sentiment + RSI <50. Confidence: ~83%.</p>
<p><strong>Step 4: MACD Momentum Alignment</strong></p>
<p>- <span class="indicator">MACD</span> momentum should support sentiment direction. Divergence reduces signal reliability. Confidence: ~86%.</p>
<p><strong>Step 5: Volume Sentiment Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spikes during sentiment extremes confirm institutional awareness. Confidence: ~88%.</p>
<p><strong>Step 6: Correlation & Entropy Context</strong></p>
<p>- <span class="indicator">Correlation</span> and <span class="indicator">Entropy</span> provide market structure context for sentiment analysis. Final confidence: ~90%.</p>
<p><strong>Tip:</strong> Best during news events and social media-driven moves. Trade sentiment extremes with technical confirmation!</p>`;
    }

    // Add helper text for Sentiment Blaster
    if (window.TRADING_STRATEGIES.sentiment_blaster && !window.TRADING_STRATEGIES.sentiment_blaster.helperText) {
      window.TRADING_STRATEGIES.sentiment_blaster.helperText = `<p><strong>Step 1: RSI Sentiment Alignment</strong></p>
<p>- <span class="indicator">RSI</span> extreme readings (>70 or <30) combined with market sentiment analysis. Contrarian approach to sentiment extremes. Confidence: ~70%.</p>
<p><strong>Step 2: MACD Momentum Check</strong></p>
<p>- <span class="indicator">MACD</span> divergence from sentiment can signal reversal opportunities. Look for momentum shifts. Confidence: ~75%.</p>
<p><strong>Step 3: Volume Sentiment Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spikes during sentiment extremes indicate institutional participation. Confidence: ~80%.</p>
<p><strong>Step 4: MFI Money Flow Analysis</strong></p>
<p>- <span class="indicator">MFI</span> shows volume-weighted sentiment. Divergence from price sentiment provides trading opportunities. Confidence: ~83%.</p>
<p><strong>Step 5: Williams %R Timing</strong></p>
<p>- <span class="indicator">Williams %R</span> extreme readings confirm sentiment-based entry timing. Final confidence: ~85%.</p>
<p><strong>Tip:</strong> Trade against extreme sentiment with technical confirmation. Best during news-driven market moves!</p>`;
    }

    // Add helper text for X Sentiment Blaster
    if (window.TRADING_STRATEGIES.x_sentiment_blaster && !window.TRADING_STRATEGIES.x_sentiment_blaster.helperText) {
      window.TRADING_STRATEGIES.x_sentiment_blaster.helperText = `<p><strong>Step 1: Advanced Sentiment Analysis</strong></p>
<p>- <span class="indicator">Sentiment</span> indicator provides real-time market mood from social media and news. Extreme readings (>75% or <25%) signal opportunities. Confidence: ~75%.</p>
<p><strong>Step 2: RSI Sentiment Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> must align with sentiment direction for momentum trades or diverge for contrarian plays. Confidence: ~78%.</p>
<p><strong>Step 3: MACD Momentum Validation</strong></p>
<p>- <span class="indicator">MACD</span> confirms sentiment-driven momentum or identifies divergences for reversals. Confidence: ~81%.</p>
<p><strong>Step 4: Volume Flow Analysis</strong></p>
<p>- <span class="indicator">Volume</span> spikes during sentiment events confirm institutional awareness and participation. Confidence: ~84%.</p>
<p><strong>Step 5: MFI Money Flow Confirmation</strong></p>
<p>- <span class="indicator">MFI</span> shows volume-weighted sentiment strength. High MFI + positive sentiment = strong bullish signal. Confidence: ~87%.</p>
<p><strong>Step 6: Williams %R & Ultimate Oscillator</strong></p>
<p>- <span class="indicator">Williams %R</span> and <span class="indicator">Ultimate Oscillator</span> provide precise entry timing within sentiment trends. Final confidence: ~90%.</p>
<p><strong>Tip:</strong> Enhanced sentiment analysis with AI integration. Best during social media-driven market events and news releases!</p>`;
    }

    // Add helper text for VWAP Guardian
    if (window.TRADING_STRATEGIES.vwap_guardian && !window.TRADING_STRATEGIES.vwap_guardian.helperText) {
      window.TRADING_STRATEGIES.vwap_guardian.helperText = `<p><strong>Step 1: VWAP Position Analysis</strong></p>
<p>- <span class="indicator">VWAP</span> acts as dynamic support/resistance. Price above VWAP = bullish bias, below = bearish bias. Confidence: ~75%.</p>
<p><strong>Step 2: RSI VWAP Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> momentum must align with VWAP position. Price above VWAP + RSI >50 = strong bullish signal. Confidence: ~80%.</p>
<p><strong>Step 3: MACD Trend Validation</strong></p>
<p>- <span class="indicator">MACD</span> confirms trend direction relative to VWAP. MACD bullish + price above VWAP = high confidence. Confidence: ~83%.</p>
<p><strong>Step 4: Volume VWAP Interaction</strong></p>
<p>- <span class="indicator">Volume</span> spikes near VWAP indicate institutional activity. High volume VWAP tests are significant. Confidence: ~86%.</p>
<p><strong>Step 5: Bollinger Band VWAP Context</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> relative to VWAP show volatility context. VWAP within bands = consolidation. Final confidence: ~88%.</p>
<p><strong>Tip:</strong> VWAP is institutional reference point. Trade bounces off VWAP with volume confirmation. Best for intraday trading!</p>`;
    }

    // Add helper text for Correlation Hunter
    if (window.TRADING_STRATEGIES.correlation_hunter && !window.TRADING_STRATEGIES.correlation_hunter.helperText) {
      window.TRADING_STRATEGIES.correlation_hunter.helperText = `<p><strong>Step 1: Correlation Analysis</strong></p>
<p>- <span class="indicator">Correlation</span> with other assets identifies divergences and unique opportunities. Low correlation = independent moves. Confidence: ~75%.</p>
<p><strong>Step 2: RSI Divergence Detection</strong></p>
<p>- <span class="indicator">RSI</span> diverging from correlated assets signals potential reversal or breakout opportunities. Confidence: ~78%.</p>
<p><strong>Step 3: MACD Correlation Confirmation</strong></p>
<p>- <span class="indicator">MACD</span> momentum diverging from correlated assets confirms independent price action. Confidence: ~81%.</p>
<p><strong>Step 4: Volume Independence Analysis</strong></p>
<p>- <span class="indicator">Volume</span> spikes during correlation breaks indicate asset-specific catalysts. Confidence: ~84%.</p>
<p><strong>Step 5: Bollinger Band Relative Analysis</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> position relative to correlated assets shows relative value opportunities. Final confidence: ~87%.</p>
<p><strong>Tip:</strong> Best during market decoupling events. Trade correlation breaks with volume confirmation!</p>`;
    }

    // Add helper text for Volatility Breakout Hunter
    if (window.TRADING_STRATEGIES.volatility_breakout_hunter && !window.TRADING_STRATEGIES.volatility_breakout_hunter.helperText) {
      window.TRADING_STRATEGIES.volatility_breakout_hunter.helperText = `<p><strong>Step 1: ATR Volatility Analysis</strong></p>
<p>- <span class="indicator">ATR</span> at multi-period lows indicates volatility compression. Rising ATR signals potential breakout. Confidence: ~75%.</p>
<p><strong>Step 2: Bollinger Band Squeeze</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> width <2% indicates tight consolidation. Band expansion signals volatility breakout. Confidence: ~80%.</p>
<p><strong>Step 3: ADX Trend Emergence</strong></p>
<p>- <span class="indicator">ADX</span> rising from <20 to >25 confirms trend emergence from consolidation. Confidence: ~83%.</p>
<p><strong>Step 4: Volume Breakout Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spike >2x average on breakout confirms institutional participation. Confidence: ~86%.</p>
<p><strong>Step 5: RSI Momentum Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> breaking above 60 (bullish) or below 40 (bearish) confirms breakout momentum. Confidence: ~88%.</p>
<p><strong>Step 6: MACD Breakout Validation</strong></p>
<p>- <span class="indicator">MACD</span> histogram expansion confirms momentum behind volatility breakout. Final confidence: ~90%.</p>
<p><strong>Tip:</strong> Wait for the squeeze to release! Best entries on initial breakout with volume. Set stops inside consolidation range!</p>`;
    }

    // Add helper text for Mean Reversion Master
    if (window.TRADING_STRATEGIES.mean_reversion_master && !window.TRADING_STRATEGIES.mean_reversion_master.helperText) {
      window.TRADING_STRATEGIES.mean_reversion_master.helperText = `<p><strong>Step 1: RSI Extreme Identification</strong></p>
<p>- <span class="indicator">RSI</span> <25 (deeply oversold) or >75 (deeply overbought) signals extreme deviation from mean. Confidence: ~75%.</p>
<p><strong>Step 2: Stoch RSI Confirmation</strong></p>
<p>- <span class="indicator">Stoch RSI</span> <10 or >90 confirms extreme momentum conditions for mean reversion. Confidence: ~78%.</p>
<p><strong>Step 3: Bollinger Band Extremes</strong></p>
<p>- Price touching or exceeding <span class="indicator">Bollinger Bands</span> outer edges indicates statistical extremes. Confidence: ~81%.</p>
<p><strong>Step 4: Williams %R & MFI Alignment</strong></p>
<p>- <span class="indicator">Williams %R</span> and <span class="indicator">MFI</span> at extreme levels provide additional mean reversion confirmation. Confidence: ~84%.</p>
<p><strong>Step 5: VWAP Distance Analysis</strong></p>
<p>- Price deviation >3% from <span class="indicator">VWAP</span> indicates extreme conditions ripe for mean reversion. Final confidence: ~87%.</p>
<p><strong>Tip:</strong> Wait for multiple oscillators at extremes. Best for range-bound markets and swing trading!</p>`;
    }

    // Add helper text for Momentum Divergence Spotter
    if (window.TRADING_STRATEGIES.momentum_divergence_spotter && !window.TRADING_STRATEGIES.momentum_divergence_spotter.helperText) {
      window.TRADING_STRATEGIES.momentum_divergence_spotter.helperText = `<p><strong>Step 1: RSI Divergence Detection</strong></p>
<p>- <span class="indicator">RSI</span> making lower highs while price makes higher highs (bearish divergence) or vice versa (bullish divergence). Confidence: ~75%.</p>
<p><strong>Step 2: MACD Divergence Confirmation</strong></p>
<p>- <span class="indicator">MACD</span> histogram and lines showing divergence from price action confirms momentum shift. Confidence: ~80%.</p>
<p><strong>Step 3: Stoch RSI Divergence Validation</strong></p>
<p>- <span class="indicator">Stoch RSI</span> divergence aligning with RSI and MACD increases reversal probability. Confidence: ~83%.</p>
<p><strong>Step 4: MFI Volume-Weighted Divergence</strong></p>
<p>- <span class="indicator">MFI</span> divergence shows volume-weighted momentum shifts, adding institutional context. Confidence: ~86%.</p>
<p><strong>Step 5: Volume Divergence Analysis</strong></p>
<p>- <span class="indicator">Volume</span> declining during price advances (bearish) or increasing during declines (bullish) confirms divergence. Confidence: ~88%.</p>
<p><strong>Step 6: Ultimate Oscillator Confirmation</strong></p>
<p>- <span class="indicator">Ultimate Oscillator</span> divergence provides final confirmation of momentum shift. Final confidence: ~90%.</p>
<p><strong>Tip:</strong> Wait for multiple momentum indicators to show divergence. Best for catching trend reversals!</p>`;
    }

    // Add helper text for Volume Profile Analyzer
    if (window.TRADING_STRATEGIES.volume_profile_analyzer && !window.TRADING_STRATEGIES.volume_profile_analyzer.helperText) {
      window.TRADING_STRATEGIES.volume_profile_analyzer.helperText = `<p><strong>Step 1: Volume Analysis</strong></p>
<p>- <span class="indicator">Volume</span> profile identifies high-volume price levels acting as support/resistance. Trade bounces off these levels. Confidence: ~75%.</p>
<p><strong>Step 2: VWAP Volume Context</strong></p>
<p>- <span class="indicator">VWAP</span> combined with volume analysis shows institutional activity levels and fair value zones. Confidence: ~78%.</p>
<p><strong>Step 3: RSI Volume Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> momentum at volume-based support/resistance levels provides entry timing. Confidence: ~81%.</p>
<p><strong>Step 4: MACD Volume Validation</strong></p>
<p>- <span class="indicator">MACD</span> momentum shifts at high-volume price levels confirm institutional interest. Confidence: ~84%.</p>
<p><strong>Step 5: Bollinger Band Volume Context</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> interaction with volume profile levels shows volatility and mean reversion opportunities. Confidence: ~86%.</p>
<p><strong>Step 6: ATR Volume Relationship</strong></p>
<p>- <span class="indicator">ATR</span> expansion/contraction at volume profile levels indicates breakout potential. Final confidence: ~88%.</p>
<p><strong>Tip:</strong> Focus on high-volume price levels for support/resistance. Best for institutional-level trading!</p>`;
    }

    // Add helper text for Multi-Timeframe Confluence
    if (window.TRADING_STRATEGIES.multi_timeframe_confluence && !window.TRADING_STRATEGIES.multi_timeframe_confluence.helperText) {
      window.TRADING_STRATEGIES.multi_timeframe_confluence.helperText = `<p><strong>Step 1: RSI Multi-Timeframe Alignment</strong></p>
<p>- <span class="indicator">RSI</span> showing same direction across 1h, 4h, 1d timeframes increases signal strength. All aligned = high confidence. Confidence: ~80%.</p>
<p><strong>Step 2: MACD Timeframe Confluence</strong></p>
<p>- <span class="indicator">MACD</span> signals aligning across multiple timeframes create high-probability setups. Confidence: ~83%.</p>
<p><strong>Step 3: ADX Trend Strength Across Timeframes</strong></p>
<p>- <span class="indicator">ADX</span> >25 on multiple timeframes confirms strong multi-timeframe trend. Confidence: ~86%.</p>
<p><strong>Step 4: Bollinger Band Multi-TF Analysis</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> position consistent across timeframes shows sustained directional bias. Confidence: ~88%.</p>
<p><strong>Step 5: Volume Multi-Timeframe Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> increasing across multiple timeframes confirms institutional participation. Confidence: ~90%.</p>
<p><strong>Step 6: ATR & VWAP Confluence</strong></p>
<p>- <span class="indicator">ATR</span> and <span class="indicator">VWAP</span> alignment across timeframes provides volatility and value context. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Only trade when 3+ timeframes align. Highest probability setups in trading!</p>`;
    }

    // Add helper text for Ichimoku Cloud Master
    if (window.TRADING_STRATEGIES.ichimoku_cloud_master && !window.TRADING_STRATEGIES.ichimoku_cloud_master.helperText) {
      window.TRADING_STRATEGIES.ichimoku_cloud_master.helperText = `<p><strong>Step 1: Ichimoku Cloud Analysis</strong></p>
<p>- <span class="indicator">Ichimoku</span> cloud position: Price above cloud = bullish, below = bearish, in cloud = neutral. Cloud color confirms trend. Confidence: ~80%.</p>
<p><strong>Step 2: RSI Cloud Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> momentum must align with cloud position. Price above cloud + RSI >50 = strong bullish signal. Confidence: ~83%.</p>
<p><strong>Step 3: Volume Cloud Interaction</strong></p>
<p>- <span class="indicator">Volume</span> spikes when price breaks through cloud levels confirm institutional participation. Confidence: ~86%.</p>
<p><strong>Step 4: ATR Volatility Context</strong></p>
<p>- <span class="indicator">ATR</span> expansion during cloud breakouts indicates strong momentum moves. Confidence: ~88%.</p>
<p><strong>Step 5: Parabolic SAR Trend Confirmation</strong></p>
<p>- <span class="indicator">Parabolic SAR</span> alignment with Ichimoku signals provides additional trend confirmation. Final confidence: ~90%.</p>
<p><strong>Tip:</strong> Trade cloud breakouts with volume confirmation. Best for trend following and swing trading!</p>`;
    }

    // Add helper text for Volume Flow Hunter
    if (window.TRADING_STRATEGIES.volume_flow_hunter && !window.TRADING_STRATEGIES.volume_flow_hunter.helperText) {
      window.TRADING_STRATEGIES.volume_flow_hunter.helperText = `<p><strong>Step 1: OBV Flow Analysis</strong></p>
<p>- <span class="indicator">OBV</span> (On-Balance Volume) tracks cumulative volume flow. Rising OBV = accumulation, falling = distribution. Confidence: ~75%.</p>
<p><strong>Step 2: Chaikin Money Flow Confirmation</strong></p>
<p>- <span class="indicator">Chaikin Money Flow</span> >0.2 indicates strong buying pressure, <-0.2 indicates selling pressure. Confidence: ~78%.</p>
<p><strong>Step 3: MFI Volume-Weighted Momentum</strong></p>
<p>- <span class="indicator">MFI</span> combines price and volume for volume-weighted RSI. Extreme readings signal reversal opportunities. Confidence: ~81%.</p>
<p><strong>Step 4: Volume Spike Analysis</strong></p>
<p>- <span class="indicator">Volume</span> spikes >2x average indicate institutional activity. Direction of price during spike shows intent. Confidence: ~84%.</p>
<p><strong>Step 5: VWAP Flow Context</strong></p>
<p>- <span class="indicator">VWAP</span> provides institutional reference point for volume flow analysis. Flow above/below VWAP shows bias. Final confidence: ~87%.</p>
<p><strong>Tip:</strong> Follow the smart money! Trade in direction of volume flow with institutional confirmation!</p>`;
    }

    // Add helper text for Momentum Oscillator Pro
    if (window.TRADING_STRATEGIES.momentum_oscillator_pro && !window.TRADING_STRATEGIES.momentum_oscillator_pro.helperText) {
      window.TRADING_STRATEGIES.momentum_oscillator_pro.helperText = `<p><strong>Step 1: CCI Momentum Analysis</strong></p>
<p>- <span class="indicator">CCI</span> (Commodity Channel Index) >100 indicates strong upward momentum, <-100 indicates strong downward momentum. Confidence: ~75%.</p>
<p><strong>Step 2: RSI Multi-Oscillator Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> must align with CCI for momentum confirmation. Both overbought/oversold increases signal strength. Confidence: ~78%.</p>
<p><strong>Step 3: Stoch RSI Precision Timing</strong></p>
<p>- <span class="indicator">Stoch RSI</span> provides precise entry timing within momentum trends identified by CCI and RSI. Confidence: ~81%.</p>
<p><strong>Step 4: Williams %R Momentum Validation</strong></p>
<p>- <span class="indicator">Williams %R</span> extreme readings confirm momentum oscillator signals. All oscillators aligned = high confidence. Confidence: ~84%.</p>
<p><strong>Step 5: Ultimate Oscillator Convergence</strong></p>
<p>- <span class="indicator">Ultimate Oscillator</span> combines multiple timeframes for momentum confirmation. Final convergence confidence: ~87%.</p>
<p><strong>Tip:</strong> Wait for 3+ oscillators to align. Best for precise momentum trading and scalping!</p>`;
    }

    // Add helper text for Trend Strength Analyzer
    if (window.TRADING_STRATEGIES.trend_strength_analyzer && !window.TRADING_STRATEGIES.trend_strength_analyzer.helperText) {
      window.TRADING_STRATEGIES.trend_strength_analyzer.helperText = `<p><strong>Step 1: Aroon Trend Direction</strong></p>
<p>- <span class="indicator">Aroon</span> Up >70 and Aroon Down <30 indicates strong uptrend. Opposite for downtrend. Confidence: ~75%.</p>
<p><strong>Step 2: ADX Trend Strength Confirmation</strong></p>
<p>- <span class="indicator">ADX</span> >25 confirms strong trend, >40 indicates very strong trend. Rising ADX = strengthening trend. Confidence: ~80%.</p>
<p><strong>Step 3: Parabolic SAR Trend Validation</strong></p>
<p>- <span class="indicator">Parabolic SAR</span> below price (uptrend) or above price (downtrend) confirms trend direction. Confidence: ~83%.</p>
<p><strong>Step 4: TRIX Momentum Confirmation</strong></p>
<p>- <span class="indicator">TRIX</span> (Triple Exponential Average) above zero confirms uptrend momentum, below zero confirms downtrend. Confidence: ~86%.</p>
<p><strong>Step 5: MACD Trend Momentum</strong></p>
<p>- <span class="indicator">MACD</span> line above signal line with expanding histogram confirms trend strength. Final confidence: ~88%.</p>
<p><strong>Tip:</strong> Only trade in direction of confirmed strong trends. Best for trend following strategies!</p>`;
    }

    // Add helper text for Institutional Flow Tracker
    if (window.TRADING_STRATEGIES.institutional_flow_tracker && !window.TRADING_STRATEGIES.institutional_flow_tracker.helperText) {
      window.TRADING_STRATEGIES.institutional_flow_tracker.helperText = `<p><strong>Step 1: VWAP Institutional Reference</strong></p>
<p>- <span class="indicator">VWAP</span> acts as institutional benchmark. Large orders typically executed near VWAP levels. Confidence: ~75%.</p>
<p><strong>Step 2: OBV Institutional Accumulation</strong></p>
<p>- <span class="indicator">OBV</span> rising faster than price indicates institutional accumulation. Falling OBV = distribution. Confidence: ~78%.</p>
<p><strong>Step 3: Chaikin Money Flow Analysis</strong></p>
<p>- <span class="indicator">Chaikin Money Flow</span> >0.25 indicates strong institutional buying, <-0.25 indicates selling. Confidence: ~81%.</p>
<p><strong>Step 4: Volume Institutional Patterns</strong></p>
<p>- <span class="indicator">Volume</span> patterns: High volume + small price moves = accumulation/distribution. Low volume + big moves = retail. Confidence: ~84%.</p>
<p><strong>Step 5: Correlation Institutional Behavior</strong></p>
<p>- <span class="indicator">Correlation</span> changes indicate institutional rebalancing or sector rotation. Independent moves signal unique catalysts. Final confidence: ~87%.</p>
<p><strong>Tip:</strong> Follow institutional money flow for high-probability trades. Best for position trading and swing trading!</p>`;
    }

    console.log(`[StrategyHelperEnhancement] Added helper text for ${this.missingHelperStrategies.length} strategies`);
  }

  enhanceHelperSystem() {
    // Create a helper text generator for strategies without specific helper text
    window.generateDefaultHelperText = (strategy) => {
      const strategyDetails = window.TRADING_STRATEGIES[strategy];
      if (!strategyDetails || strategyDetails.helperText) {
        return strategyDetails?.helperText || 'No helper text available.';
      }

      const indicators = strategyDetails.indicators || [];
      let helperHTML = `<p><strong>${strategyDetails.name} Strategy Guide</strong></p>
                       <p>${strategyDetails.description}</p>
                       <p><strong>Follow these steps:</strong></p>
                       <ol>`;

      // Generate steps based on indicators
      let stepCount = 1;
      
      if (indicators.includes('rsi')) {
        helperHTML += `<li><strong>Step ${stepCount}: RSI Analysis</strong><br>
        Check <span class="indicator">RSI</span> levels: >70 (overbought), <30 (oversold), or momentum around 50 line.</li>`;
        stepCount++;
      }

      if (indicators.includes('macd')) {
        helperHTML += `<li><strong>Step ${stepCount}: MACD Confirmation</strong><br>
        Confirm <span class="indicator">MACD</span> line crossing signal line and histogram direction.</li>`;
        stepCount++;
      }

      if (indicators.includes('bollingerBands')) {
        helperHTML += `<li><strong>Step ${stepCount}: Bollinger Bands Position</strong><br>
        Check price position relative to <span class="indicator">Bollinger Bands</span> for volatility and mean reversion signals.</li>`;
        stepCount++;
      }

      if (indicators.includes('volume')) {
        helperHTML += `<li><strong>Step ${stepCount}: Volume Confirmation</strong><br>
        Confirm <span class="indicator">Volume</span> spike (>1.5x average) to validate the move.</li>`;
        stepCount++;
      }

      if (indicators.includes('adx')) {
        helperHTML += `<li><strong>Step ${stepCount}: Trend Strength Check</strong><br>
        Use <span class="indicator">ADX</span> to confirm trend strength (>25 for strong trends).</li>`;
        stepCount++;
      }

      helperHTML += `</ol>
                    <p><strong>Tip:</strong> Wait for at least 2-3 indicators to align before entering trades. Use proper risk management!</p>`;

      return helperHTML;
    };

    // Enhance the strategy helper update function
    const originalUpdateHelper = window.updateAdmiralHelper;
    window.updateAdmiralHelper = (strategy) => {
      const strategyDetails = window.TRADING_STRATEGIES?.[strategy];
      if (!strategyDetails) return;

      const helperContainer = document.querySelector('.helper-container');
      if (helperContainer) {
        let helperText = strategyDetails.helperText;
        
        // Use generated helper text if none exists
        if (!helperText) {
          helperText = window.generateDefaultHelperText(strategy);
        }

        helperContainer.innerHTML = `
          <div class="helper-header">
            <h3>Admiral T.O.A. Strategy Guide</h3>
            <span class="strategy-name">${strategyDetails.name}</span>
          </div>
          <div class="helper-content">
            ${helperText}
          </div>
        `;
      }

      // Call original function if it exists
      if (originalUpdateHelper && typeof originalUpdateHelper === 'function') {
        originalUpdateHelper(strategy);
      }
    };
  }
}

// Initialize strategy helper enhancement
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.strategyHelperEnhancement = new StrategyHelperEnhancement();
  }, 1000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StrategyHelperEnhancement;
}
