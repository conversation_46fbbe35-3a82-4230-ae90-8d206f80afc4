const WebSocket = require('ws');
const config = require('./config');

const {
  WS_URL,
  WS,
  PAIRS: KRAKEN_PAIRS
} = config.KRAKEN;

class KrakenWebSocket {
  // Track last price for each pair
  lastPrices = new Map();
  constructor(wss) {
    this.ws = null;
    this.wss = wss;
    this.pingInterval = null;
    this.pongTimeout = null;
    this.subscriptions = new Set();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = WS.RECONNECT_DELAY;
    this.pingIntervalMs = WS.PING_INTERVAL;
    this.pongTimeoutMs = WS.PONG_TIMEOUT;
    this.maxReconnectAttempts = WS.MAX_RECONNECT_ATTEMPTS;
  }

  connect() {
    if (this.ws) {
      console.log('WebSocket already connected');
      return;
    }

    this.ws = new WebSocket(WS_URL);

    this.ws.on('open', () => {
      console.log('WebSocket connected to Kraken');
      this.reconnectAttempts = 0;
      this.setupPingPong();
      this.resubscribe();
    });

    this.ws.on('message', (data) => this.handleMessage(data));
    this.ws.on('pong', () => this.handlePong());
    this.ws.on('close', () => this.handleClose());
    this.ws.on('error', (error) => this.handleError(error));
  }

  handleMessage(data) {
    try {
      const message = JSON.parse(data);
      if (Array.isArray(message) && message[0] > 0) {
        this.resetPongTimeout();
        
        if (message[1]?.a) {
          this.handlePriceUpdate(message);
        }
      }
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
    }
  }

  handlePriceUpdate(message) {
    const price = parseFloat(message[1].a[0][0]);
    const volume = parseFloat(message[1].a[0][1]);
    const timestamp = Math.floor(Date.now() / 1000);
    
    const pair = Object.entries(KRAKEN_PAIRS).find(
      ([_, krakenPair]) => krakenPair === message[3]?.replace('/', '')
    )?.[0];
    
    if (pair) {
      // Update last price for this pair
      this.lastPrices.set(pair, {
        price,
        volume,
        timestamp,
        source: 'websocket'
      });
      
      this.broadcastPriceUpdate(pair, price, volume, timestamp);
    }
  }
  
  /**
   * Get the last known price for a trading pair
   * @param {string} pair - The trading pair (e.g., 'BTC/USD')
   * @returns {Object|null} - The last price data or null if not available
   */
  getLastPrice(pair) {
    return this.lastPrices.get(pair) || null;
  }

  broadcastPriceUpdate(pair, price, volume, timestamp) {
    if (!this.wss) return;
    
    const update = JSON.stringify({
      type: 'price_update',
      pair,
      price,
      volume,
      timestamp
    });

    this.wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(update);
      }
    });
  }

  handlePong() {
    console.log('Received pong from Kraken');
    this.resetPongTimeout();
  }

  handleClose() {
    console.log('WebSocket disconnected');
    this.cleanup();
    this.attemptReconnect();
  }

  handleError(error) {
    console.error('WebSocket error:', error);
    this.cleanup();
    this.attemptReconnect();
  }

  setupPingPong() {
    if (this.pingInterval) clearInterval(this.pingInterval);
    if (this.pongTimeout) clearTimeout(this.pongTimeout);

    this.pingInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        try {
          this.ws.ping();
          console.log('Sent ping to Kraken');
          this.resetPongTimeout();
        } catch (error) {
          console.error('Error sending ping:', error);
          this.handleError(error);
        }
      }
    }, this.pingIntervalMs);
  }

  resetPongTimeout() {
    if (this.pongTimeout) clearTimeout(this.pongTimeout);
    this.pongTimeout = setTimeout(() => {
      console.error('Pong timeout, reconnecting...');
      this.handleError(new Error('Pong timeout'));
    }, this.pongTimeoutMs);
  }

  cleanup() {
    if (this.pingInterval) clearInterval(this.pingInterval);
    if (this.pongTimeout) clearTimeout(this.pongTimeout);
    if (this.ws) {
      this.ws.removeAllListeners();
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.close();
      }
      this.ws = null;
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnection attempts (${this.maxReconnectAttempts}) reached. Please check your connection.`);
      // Reset attempts after a longer delay
      setTimeout(() => {
        this.reconnectAttempts = 0;
        this.attemptReconnect();
      }, 60000); // Try again after 1 minute
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  subscribe(pair) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket not connected, cannot subscribe');
      return;
    }

    const krakenPair = KRAKEN_PAIRS[pair];
    if (!krakenPair) {
      console.error(`No Kraken pair mapping for ${pair}`);
      return;
    }

    const subscription = {
      event: 'subscribe',
      pair: [krakenPair],
      subscription: {
        name: 'ticker'
      }
    };

    this.ws.send(JSON.stringify(subscription));
    this.subscriptions.add(pair);
  }

  resubscribe() {
    this.subscriptions.forEach(pair => this.subscribe(pair));
  }
}

module.exports = KrakenWebSocket;
