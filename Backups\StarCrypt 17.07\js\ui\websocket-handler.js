// websocket-handler.js
// Handles WebSocket connection and message processing for StarCrypt

(function() {
  'use strict';

  // Configuration - OPTIMIZED FOR PERFORMANCE
  const CONFIG = {
    MAX_RECONNECT_ATTEMPTS: 5,
    TIMEFRAMES: ['1', '5', '15', '60', '240', '1440'],
    MAX_MESSAGES_PER_BATCH: 50, // Reduced for better performance
    MAX_PROCESS_TIME_MS: 500, // Reduced processing time
    MESSAGE_LOG_LIMIT: 50, // Reduced memory usage
    INDICATOR_THROTTLE_MS: 150, // Increased throttle for better performance
    PRICE_UPDATE_THROTTLE_MS: 100, // Increased throttle
    RECONNECT_DELAY_BASE_MS: 1000,
    // New performance settings
    DUPLICATE_MESSAGE_WINDOW_MS: 1000, // Window to detect duplicate messages
    MAX_CACHED_MESSAGES: 100, // Maximum cached messages for duplicate detection
    BATCH_PROCESSING_DELAY: 10 // Delay between batch processing
  };

  // Global state - ENHANCED FOR PERFORMANCE
  const state = {
    ws: null,
    reconnectAttempts: 0,
    messageQueue: [],
    isProcessingQueue: false,
    lastIndicatorUpdate: 0,
    lastPriceUpdate: 0,
    indicatorsData: {},
    currentPrice: null,
    wsLastMessageTime: null,
    isConnected: false,
    reconnectTimer: null,
    // New performance tracking
    messageCache: new Map(), // Cache for duplicate detection
    processedMessageIds: new Set(), // Track processed messages
    lastCleanup: Date.now(), // Last cache cleanup time
    performanceMetrics: {
      messagesReceived: 0,
      messagesProcessed: 0,
      duplicatesFiltered: 0,
      averageProcessingTime: 0
    }
  };

  // Global variables and constants
  window.ws = null;
  window.reconnectAttempts = 0;
  let MAX_RECONNECT_ATTEMPTS = 5;
  window.RECONNECT_DELAY = 2000;
  window.KRAKEN_WS_URL = 'ws://localhost:8080';
  window.SUBSCRIBE_MSG = JSON.stringify({
    event: 'subscribe',
    pair: ['XBT/USD'],
    subscription: {
      name: 'ticker'
    }
  });

  // Helper functions
  function calculateRSI(prices, period = 14) {
    if (!prices || prices.length < period + 1) return [];
    
    const changes = [];
    for (let i = 1; i < prices.length; i++) {
      changes.push(prices[i] - prices[i - 1]);
    }
    
    const gains = changes.map(change => Math.max(0, change));
    const losses = changes.map(change => Math.abs(Math.min(0, change)));
    
    let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
    let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
    
    const rsi = [100 - (100 / (1 + (avgGain / (avgLoss || 1))))];
    
    for (let i = period; i < changes.length; i++) {
      avgGain = (avgGain * (period - 1) + gains[i]) / period;
      avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
      rsi.push(100 - (100 / (1 + (avgGain / (avgLoss || 1)))));
    }
    
    return rsi;
  }

  // Initialize WebSocket connection
  function initWebSocket() {
    if (state.ws) {
      state.ws.close();
    }

    state.ws = new WebSocket(window.KRAKEN_WS_URL);
    state.ws.onopen = function() {
      console.log('WebSocket connected');
      state.reconnectAttempts = 0;
      state.isConnected = true;
      
      // Subscribe to ticker updates
      if (state.ws.readyState === WebSocket.OPEN) {
        state.ws.send(window.SUBSCRIBE_MSG);
      }
    };

    state.ws.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data);
        processWebSocketMessage(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    state.ws.onclose = function() {
      console.log('WebSocket disconnected');
      state.isConnected = false;
      
      // Attempt to reconnect with exponential backoff
      if (state.reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        const delay = Math.min(
          window.RECONNECT_DELAY * Math.pow(2, state.reconnectAttempts),
          30000 // Max 30 seconds
        );
        
        console.log(`Reconnecting in ${delay}ms...`);
        state.reconnectTimer = setTimeout(initWebSocket, delay);
        state.reconnectAttempts++;
      } else {
        console.error('Max reconnection attempts reached');
      }
    };

    state.ws.onerror = function(error) {
      console.error('WebSocket error:', error);
      state.ws.close(); // Will trigger onclose
    };
  }

  // Process WebSocket messages
  function processWebSocketMessage(data) {
    if (!data) return;
    
    // Add message to queue
    state.messageQueue.push(data);
    
    // Process queue if not already processing
    if (!state.isProcessingQueue) {
      processMessageQueue();
    }
  }

  // Process message queue with rate limiting and deduplication
  function processMessageQueue() {
    if (state.messageQueue.length === 0 || state.isProcessingQueue) {
      return;
    }
    
    state.isProcessingQueue = true;
    
    setTimeout(() => {
      try {
        const data = state.messageQueue.shift();
        if (!data) {
          state.isProcessingQueue = false;
          return;
        }
        
        // Log non-price update messages
        if (data.type !== 'priceUpdate' && window.logMessages) {
          window.logMessages.push(`[${new Date().toLocaleString()}] Received ${data.type || 'unknown'} message`);
          if (window.logMessages.length > 100) window.logMessages.shift();
          if (typeof window.updateLogger === 'function') window.updateLogger();
        }
        
        // Process message based on type
        if (data.event === 'heartbeat') {
          // Handle heartbeat
          state.wsLastMessageTime = Date.now();
        } else if (Array.isArray(data) && data[1] === 'ticker') {
          // Handle ticker update
          const tickerData = data[1];
          if (tickerData && tickerData.c && tickerData.c[0]) {
            const price = parseFloat(tickerData.c[0]);
            if (!isNaN(price)) {
              state.currentPrice = price;
              
              // Update UI with new price
              if (typeof window.updatePriceDisplay === 'function') {
                window.updatePriceDisplay(price);
              }
            }
          }
        } else if (data.type === 'indicators') {
          // Throttle indicator updates to prevent UI freezes
          const now = Date.now();
          if (now - state.lastIndicatorUpdate < CONFIG.INDICATOR_THROTTLE_MS) {
            // If we're getting updates too fast, requeue and try later
            state.messageQueue.unshift(data);
            state.isProcessingQueue = false;
            return;
          }
          state.lastIndicatorUpdate = now;
          
          // Process indicator data
          // Server sends indicator data in the 'data' field
          if (data.data && typeof data.data === 'object') {
            if (!window.indicatorsData) window.indicatorsData = {};
            if (data.timeframe && !window.indicatorsData[data.timeframe]) {
              window.indicatorsData[data.timeframe] = {};
            }
            
            try {
              // Extract indicators from data object
              const indicatorEntries = Object.entries(data.data)
                .filter(([key, value]) => key !== 'metadata' && typeof value === 'object')
                .map(([name, ind]) => ({
                  name,
                  ...ind
                data: Array.isArray(ind.data) ? ind.data.map(val => Number.isFinite(val) ? val : 0) : [],
                labels: Array.isArray(ind.labels) ? ind.labels.map(String) : []
              }));
              
              // Batch update indicators
              for (const indicator of indicatorEntries) {
                if (indicator.name && data.timeframe) {
                  window.indicatorsData[data.timeframe][indicator.name] = indicator;
                }
              }
              
              // Update signal lights only if the matrix is ready
              if (typeof window.isSignalMatrixReady === 'function' && window.isSignalMatrixReady()) {
                if (typeof window.updateAllSignalLights === 'function') {
                  if (!window.signalLightUpdatePending) {
                    window.signalLightUpdatePending = true;
                    requestAnimationFrame(() => {
                      window.updateAllSignalLights();
                      window.signalLightUpdatePending = false;
                    });
                  }
                }
              } else {
                console.warn('[WebSocket] Signal matrix not ready, discarding indicator update.');
              }
            } catch (e) {
              console.error('Error processing indicators:', e);
            }
          }
        }
        
        // Process next message in queue if any
        state.isProcessingQueue = false;
        if (state.messageQueue.length > 0) {
          processMessageQueue();
        }
      } catch (e) {
        console.error('Error in message queue processing:', e);
        state.isProcessingQueue = false;
      }
    }, 0);
  }

  // Initialize global error handler if not already installed
  if (!window.__errorHandlerInstalled) {
    const originalOnError = window.onerror;
    
    window.onerror = function(message, source, lineno, colno, error) {
      if (window.__errorHandling) return true;
      window.__errorHandling = true;
      
      try {
        // Enhanced handling for cross-origin script errors
        if (message === 'Script error.' || message === 'Script error') {
          // Silently handle cross-origin script errors without logging
          // These are typically caused by third-party scripts and can't be debugged
          return true;
        }
        
        console.error('Global error:', {
          message,
          source,
          lineno,
          colno,
          error: error && error.stack ? error.stack : error
        });
        
        // Call original handler if it exists
        if (typeof originalOnError === 'function') {
          return originalOnError(message, source, lineno, colno, error);
        }
        
        return false;
      } finally {
        window.__errorHandling = false;
      }
    };
    window.__errorHandlerInstalled = true;
  }

  // Export functions that need to be accessible globally
  window.processWebSocketMessage = processWebSocketMessage;
  window.processMessageQueue = processMessageQueue;
  window.initWebSocket = initWebSocket;

  // Initialize WebSocket connection when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWebSocket);
  } else {
    initWebSocket();
  }
})();
