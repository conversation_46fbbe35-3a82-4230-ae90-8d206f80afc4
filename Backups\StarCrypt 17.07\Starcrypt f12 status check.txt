// Quick status
const mlSystem = window.MLHistoricalAnalysis || window.mlHistoricalAnalysis;
console.log(`🔧 SCOTTIE: ML Reactor Temperature: ${mlSystem?.selectedLights?.size || 0} signals`);
console.log(`🔧 SCOTTIE: Forward Shields: ${document.querySelectorAll('.signal-circle.selected').length} active`);
console.log(`🔧 SCOTTIE: Analyze Button: ${document.getElementById('analyzeConvergence')?.disabled ? 'COLD' : 'HOT'}`);