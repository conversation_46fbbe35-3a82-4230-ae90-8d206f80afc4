const fs = require('fs');
const path = require('path');

// Configuration
const ROOT_DIR = __dirname;
const OUTPUT_FILE = path.join(ROOT_DIR, 'STRATEGY_SELECTOR_ANALYSIS.md');
const IGNORE_DIRS = ['node_modules', '.git', 'backup', 'StarCrypt 27.06'];

// Track strategy selector references
const references = [];

// Search for strategy selector references in a file
function analyzeFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(ROOT_DIR, filePath);
        
        // Check for strategy selector references
        const patterns = [
            'initializeStrategySelector',
            'strategySelector',
            'strategy-selector',
            'mainStrategySelector',
            'strategyMenu',
            'strategy-menu',
            'handleStrategyChange',
            'updateStrategyDescription'
        ];
        
        const matches = [];
        
        patterns.forEach(pattern => {
            const regex = new RegExp(pattern, 'g');
            let match;
            while ((match = regex.exec(content)) !== null) {
                // Get some context around the match
                const start = Math.max(0, match.index - 50);
                const end = Math.min(content.length, match.index + match[0].length + 50);
                const context = content.substring(start, end).replace(/\n/g, ' ');
                
                matches.push({
                    pattern,
                    context
                });
            }
        });
        
        if (matches.length > 0) {
            references.push({
                file: relativePath,
                matches: matches
            });
        }
    } catch (err) {
        console.error(`Error analyzing ${filePath}:`, err.message);
    }
}

// Recursively scan directory
function scanDirectory(dir) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
            if (!IGNORE_DIRS.includes(entry.name)) {
                scanDirectory(fullPath);
            }
        } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (['.js', '.html'].includes(ext)) {
                analyzeFile(fullPath);
            }
        }
    }
}

// Generate markdown output
function generateMarkdown() {
    let output = '# Strategy Selector Analysis\n\n';
    
    // Group by file
    references.forEach(ref => {
        output += `## ${ref.file}\n\n`;
        
        // Group by pattern
        const byPattern = {};
        ref.matches.forEach(match => {
            if (!byPattern[match.pattern]) {
                byPattern[match.pattern] = [];
            }
            byPattern[match.pattern].push(match.context);
        });
        
        // Output matches by pattern
        Object.entries(byPattern).forEach(([pattern, contexts]) => {
            output += `### ${pattern} (${contexts.length} occurrences)\n\n`;
            contexts.forEach(context => {
                output += `- ${context}\n`;
            });
            output += '\n';
        });
        
        output += '\n';
    });
    
    // Add summary
    output += '# Summary of Strategy Selector Initialization\n\n';
    output += '## Initialization Flow\n\n';
    output += '1. `index.html` loads and includes various JavaScript files\n';
    output += '2. `js/main.js` is loaded and initializes the application\n';
    output += '3. `js/ui/strategy-selector.js` is loaded and initializes the strategy selector\n';
    output += '4. `js/ui/menu-controller.js` may also initialize the strategy selector\n';
    output += '5. Other modules may reference or modify the strategy selector\n\n';
    
    output += '## Recommendations\n\n';
    output += '1. Ensure `initializeStrategySelector` is only called once\n';
    output += '2. Check for duplicate event listeners on strategy selector elements\n';
    output += '3. Verify that no other scripts are re-initializing the strategy selector\n';
    
    return output;
}

// Main execution
console.log('Analyzing strategy selector references...');
scanDirectory(ROOT_DIR);

// Write results to file
const markdownOutput = generateMarkdown();
fs.writeFileSync(OUTPUT_FILE, markdownOutput);
console.log(`Analysis complete! Results written to ${OUTPUT_FILE}`);
