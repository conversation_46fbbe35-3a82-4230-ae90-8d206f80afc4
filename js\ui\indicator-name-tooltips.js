/**
 * Indicator Name Tooltips System
 * Creates separate educational tooltips for indicator names that follow the mouse
 * and provide detailed information about each indicator
 */

class IndicatorNameTooltips {
  constructor() {
    this.tooltip = null;
    this.currentIndicator = null;
    this.mouseX = 0;
    this.mouseY = 0;
    this.isVisible = false;
    
    this.init();
  }

  init() {
    console.log('[IndicatorNameTooltips] 📚 Initializing educational indicator tooltips...');
    
    // Create tooltip element
    this.createTooltipElement();
    
    // Add mouse tracking
    this.setupMouseTracking();
    
    // Setup indicator name hover handlers
    this.setupIndicatorNameHandlers();
    
    // Add styles
    this.addTooltipStyles();
    
    console.log('[IndicatorNameTooltips] ✅ Educational tooltips system ready');
  }

  createTooltipElement() {
    this.tooltip = document.createElement('div');
    this.tooltip.id = 'indicator-name-tooltip';
    this.tooltip.className = 'indicator-educational-tooltip';
    this.tooltip.style.display = 'none';
    document.body.appendChild(this.tooltip);
  }

  setupMouseTracking() {
    // Track mouse position for tooltip following
    document.addEventListener('mousemove', (e) => {
      this.mouseX = e.clientX;
      this.mouseY = e.clientY;
      
      if (this.isVisible) {
        this.positionTooltip();
      }
    });
  }

  setupIndicatorNameHandlers() {
    // Use event delegation to handle dynamically created indicator names
    document.addEventListener('mouseenter', (e) => {
      // 🚫 PREVENT CONFLICTS: Only show on indicator names, NOT circles or mini charts
      if (this.isCircleOrMiniChart(e.target)) {
        return; // Let other tooltip systems handle these
      }

      const indicatorName = this.findIndicatorNameElement(e.target);
      if (indicatorName) {
        this.showIndicatorTooltip(indicatorName);
      }
    }, true);

    document.addEventListener('mouseleave', (e) => {
      if (this.isCircleOrMiniChart(e.target)) {
        return;
      }

      const indicatorName = this.findIndicatorNameElement(e.target);
      if (indicatorName) {
        this.hideIndicatorTooltip();
      }
    }, true);
  }

  isCircleOrMiniChart(element) {
    // Check if element is a signal circle or mini chart
    if (!element) return false;

    const isCircle = element.classList.contains('signal-circle') ||
                    element.classList.contains('signal-light') ||
                    element.hasAttribute('data-tf') ||
                    element.hasAttribute('data-timeframe');

    const isMiniChart = element.tagName === 'CANVAS' ||
                       element.classList.contains('mini-chart') ||
                       element.id?.includes('mini-chart');

    const isInMiniChart = element.closest('.mini-chart-container') ||
                         element.closest('canvas');

    return isCircle || isMiniChart || isInMiniChart;
  }

  findIndicatorNameElement(element) {
    // Look for indicator name elements in the momentum-indicators section
    if (!element || !element.closest) return null;
    
    // Check if this is an indicator name element
    const momentumContainer = element.closest('.momentum-indicators');
    if (!momentumContainer) return null;
    
    // Look for indicator name patterns
    const indicatorPatterns = [
      '.indicator-name',
      '.indicator-label', 
      '[data-indicator-name]',
      '.signal-row td:first-child',
      '.indicator-title'
    ];
    
    for (const pattern of indicatorPatterns) {
      const nameElement = element.closest(pattern) || element.querySelector(pattern);
      if (nameElement) {
        return nameElement;
      }
    }
    
    // Check if the text content matches known indicators
    const text = element.textContent?.trim().toLowerCase();
    if (text && this.getIndicatorInfo(text)) {
      return element;
    }
    
    return null;
  }

  showIndicatorTooltip(element) {
    const indicatorText = element.textContent?.trim().toLowerCase();
    const indicatorInfo = this.getIndicatorInfo(indicatorText);
    
    if (!indicatorInfo) {
      console.log('[IndicatorNameTooltips] No info found for:', indicatorText);
      return;
    }
    
    console.log('[IndicatorNameTooltips] 📚 Showing educational tooltip for:', indicatorInfo.name);
    
    this.currentIndicator = indicatorInfo;
    this.tooltip.innerHTML = this.generateEducationalTooltip(indicatorInfo);
    this.tooltip.style.display = 'block';
    this.isVisible = true;
    this.positionTooltip();
    
    // Add fade-in animation
    setTimeout(() => {
      this.tooltip.classList.add('visible');
    }, 10);
  }

  hideIndicatorTooltip() {
    if (!this.isVisible) return;
    
    this.tooltip.classList.remove('visible');
    this.isVisible = false;
    
    setTimeout(() => {
      this.tooltip.style.display = 'none';
      this.currentIndicator = null;
    }, 200);
  }

  positionTooltip() {
    if (!this.tooltip || !this.isVisible) return;

    const tooltipRect = this.tooltip.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 🎯 CLOSER TO MOUSE - REDUCED OFFSET
    let x = this.mouseX + 8;  // Reduced from 15
    let y = this.mouseY - 5;  // Reduced from -10

    // Adjust if tooltip would go off screen
    if (x + tooltipRect.width > viewportWidth) {
      x = this.mouseX - tooltipRect.width - 8;  // Reduced offset
    }

    if (y + tooltipRect.height > viewportHeight) {
      y = this.mouseY - tooltipRect.height - 5;  // Reduced offset
    }

    // Ensure tooltip doesn't go above viewport
    if (y < 0) {
      y = this.mouseY + 8;  // Reduced offset
    }

    // Ensure tooltip doesn't go left of viewport
    if (x < 0) {
      x = 5;  // Reduced margin
    }

    this.tooltip.style.left = `${x}px`;
    this.tooltip.style.top = `${y}px`;
  }

  generateEducationalTooltip(info) {
    return `
      <div class="educational-tooltip-content">
        <div class="tooltip-title">
          <span class="indicator-icon">${info.icon}</span>
          <span class="indicator-full-name">${info.name}</span>
        </div>
        
        <div class="tooltip-description">
          ${info.description}
        </div>
        
        <div class="tooltip-details">
          <div class="detail-row">
            <span class="detail-label">Type:</span>
            <span class="detail-value">${info.type}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Best For:</span>
            <span class="detail-value">${info.bestFor}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Signals:</span>
            <span class="detail-value">${info.signals}</span>
          </div>
        </div>
        
        <div class="tooltip-legend">
          <div class="legend-title">Signal Colors:</div>
          <div class="legend-colors">
            <div class="legend-item">
              <div class="legend-color" style="background: #00FF00;"></div>
              <span>Strong Buy</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #0000FF;"></div>
              <span>Buy</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #808080;"></div>
              <span>Neutral</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #FFA500;"></div>
              <span>Sell</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #FF0000;"></div>
              <span>Strong Sell</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  getIndicatorInfo(indicatorText) {
    const indicators = {
      'rsi': {
        name: 'Relative Strength Index',
        icon: '📊',
        type: 'Momentum Oscillator',
        description: 'Measures the speed and change of price movements. Values above 70 indicate overbought conditions, while values below 30 suggest oversold conditions.',
        bestFor: 'Identifying overbought/oversold levels',
        signals: 'Divergences, overbought/oversold levels'
      },
      'stochrsi': {
        name: 'Stochastic RSI',
        icon: '🎯',
        type: 'Momentum Oscillator',
        description: 'Combines Stochastic and RSI indicators. More sensitive than regular RSI, providing earlier signals but with more noise.',
        bestFor: 'Early trend reversal signals',
        signals: 'Crossovers, extreme levels'
      },
      'macd': {
        name: 'Moving Average Convergence Divergence',
        icon: '📈',
        type: 'Trend Following',
        description: 'Shows the relationship between two moving averages. The MACD line crossing above the signal line suggests bullish momentum.',
        bestFor: 'Trend changes and momentum shifts',
        signals: 'Line crossovers, histogram changes'
      },
      'bollinger': {
        name: 'Bollinger Bands',
        icon: '📏',
        type: 'Volatility',
        description: 'Uses standard deviation to create upper and lower bands around a moving average. Price touching bands indicates potential reversal points.',
        bestFor: 'Volatility and mean reversion',
        signals: 'Band touches, squeeze/expansion'
      },
      'atr': {
        name: 'Average True Range',
        icon: '⚡',
        type: 'Volatility',
        description: 'Measures market volatility by calculating the average range of price movements. Higher values indicate increased volatility.',
        bestFor: 'Risk management and position sizing',
        signals: 'Volatility expansion/contraction'
      },
      'cci': {
        name: 'Commodity Channel Index',
        icon: '🌊',
        type: 'Momentum Oscillator',
        description: 'Identifies cyclical trends and overbought/oversold conditions. Values above +100 suggest overbought, below -100 suggest oversold.',
        bestFor: 'Cyclical market analysis',
        signals: 'Extreme readings, divergences'
      },
      'williams': {
        name: 'Williams %R',
        icon: '🎪',
        type: 'Momentum Oscillator',
        description: 'Measures overbought and oversold levels. Values above -20 indicate overbought conditions, below -80 indicate oversold.',
        bestFor: 'Short-term reversal signals',
        signals: 'Extreme readings, divergences'
      },
      'roc': {
        name: 'Rate of Change',
        icon: '🚀',
        type: 'Momentum',
        description: 'Measures the percentage change in price over a specified period. Positive values indicate upward momentum.',
        bestFor: 'Momentum confirmation',
        signals: 'Zero line crosses, divergences'
      },
      'mfi': {
        name: 'Money Flow Index',
        icon: '💰',
        type: 'Volume-Price',
        description: 'Combines price and volume to measure buying and selling pressure. Values above 80 suggest overbought, below 20 suggest oversold.',
        bestFor: 'Volume-weighted momentum',
        signals: 'Overbought/oversold, divergences'
      },
      'adx': {
        name: 'Average Directional Index',
        icon: '🧭',
        type: 'Trend Strength',
        description: 'Measures the strength of a trend without indicating direction. Values above 25 suggest strong trending conditions.',
        bestFor: 'Trend strength assessment',
        signals: 'Trend strength changes'
      }
    };
    
    // Try exact match first
    if (indicators[indicatorText]) {
      return indicators[indicatorText];
    }
    
    // Try partial matches
    for (const [key, info] of Object.entries(indicators)) {
      if (indicatorText.includes(key) || key.includes(indicatorText)) {
        return info;
      }
    }
    
    return null;
  }

  addTooltipStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .indicator-educational-tooltip {
        position: fixed;
        background: linear-gradient(135deg, rgba(0, 30, 60, 0.98), rgba(0, 50, 100, 0.98));
        border: 2px solid rgba(0, 255, 255, 0.6);
        border-radius: 12px;
        padding: 0;
        min-width: 300px;
        max-width: 400px;
        z-index: 999999;
        pointer-events: none;
        opacity: 0;
        transform: scale(0.9) translateY(10px);
        transition: all 0.3s ease;
        backdrop-filter: blur(15px);
        box-shadow: 0 12px 40px rgba(0, 255, 255, 0.4);
        font-family: 'Roboto', sans-serif;
      }
      
      .indicator-educational-tooltip.visible {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
      
      .educational-tooltip-content {
        padding: 0;
      }
      
      .tooltip-title {
        background: linear-gradient(90deg, rgba(0, 255, 255, 0.3), rgba(0, 150, 255, 0.3));
        padding: 12px 16px;
        border-radius: 10px 10px 0 0;
        border-bottom: 1px solid rgba(0, 255, 255, 0.4);
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .indicator-icon {
        font-size: 1.2rem;
      }
      
      .indicator-full-name {
        font-size: 1rem;
        font-weight: bold;
        color: #00FFFF;
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
      }
      
      .tooltip-description {
        padding: 12px 16px;
        color: #FFFFFF;
        font-size: 0.9rem;
        line-height: 1.4;
        border-bottom: 1px solid rgba(0, 255, 255, 0.2);
      }
      
      .tooltip-details {
        padding: 10px 16px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.2);
      }
      
      .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;
        font-size: 0.85rem;
      }
      
      .detail-label {
        color: #AAAAAA;
        font-weight: 500;
      }
      
      .detail-value {
        color: #FFFFFF;
        font-weight: bold;
      }
      
      .tooltip-legend {
        padding: 10px 16px;
      }
      
      .legend-title {
        font-size: 0.8rem;
        color: #AAAAAA;
        margin-bottom: 8px;
        font-weight: bold;
      }
      
      .legend-colors {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 0.7rem;
        color: #CCCCCC;
      }
      
      .legend-color {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    `;
    
    document.head.appendChild(style);
  }

  // Cleanup method
  destroy() {
    if (this.tooltip) {
      this.tooltip.remove();
    }
    
    // Remove event listeners would go here if we stored references
    console.log('[IndicatorNameTooltips] 🧹 Cleaned up educational tooltips');
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (window.indicatorNameTooltips) {
      window.indicatorNameTooltips.destroy();
    }
    
    window.indicatorNameTooltips = new IndicatorNameTooltips();
    console.log('[IndicatorNameTooltips] 📚 Educational indicator tooltips system initialized');
  }, 2000);
});
