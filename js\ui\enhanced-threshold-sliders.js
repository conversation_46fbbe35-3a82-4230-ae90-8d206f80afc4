/**
 * Enhanced Threshold Sliders for StarCrypt
 * Provides proper 4-thumb sliders with 5-color quadrants for live adjustment
 */

class EnhancedThresholdSliders {
  constructor() {
    this.sliders = new Map();
    this.isDragging = false;
    this.activeThumb = null;
    this.currentStrategy = 'admiral_toa';
    this.liveAdjustment = true;

    // Default thresholds for 5-color logic
    this.defaultThresholds = {
      rsi: { green: 20, blue: 40, orange: 60, red: 80 },
      macd: { green: 15, blue: 35, orange: 65, red: 85 },
      stochRsi: { green: 20, blue: 40, orange: 60, red: 80 },
      bollingerBands: { green: 5, blue: 25, orange: 75, red: 95 },
      atr: { green: 10, blue: 30, orange: 70, red: 90 },
      volume: { green: 20, blue: 40, orange: 60, red: 80 },
      mfi: { green: 20, blue: 40, orange: 60, red: 80 },
      williamsR: { green: 80, blue: 60, orange: 40, red: 20 }, // Inverted for Williams %R
      cci: { green: 15, blue: 35, orange: 65, red: 85 },
      ultimateOscillator: { green: 20, blue: 40, orange: 60, red: 80 },
      adx: { green: 15, blue: 35, orange: 65, red: 85 },
      mlPrediction: { green: 20, blue: 40, orange: 60, red: 80 }
    };

    this.init();
  }

  init() {
    this.loadThresholds();
    this.bindGlobalEvents();
    this.disableCompetingSystems();
    console.log('🎚️ Enhanced Threshold Sliders initialized');
  }

  disableCompetingSystems() {
    // Disable other threshold slider systems to prevent conflicts
    if (window.thresholdSliders && window.thresholdSliders.render) {
      const originalRender = window.thresholdSliders.render;
      window.thresholdSliders.render = (...args) => {
        console.log('[EnhancedThresholdSliders] 🚫 BLOCKED: Legacy threshold sliders → Enhanced Sliders');
        // Don't call the original render to prevent conflicts
      };
    }

    // Override threshold menu rendering
    if (window.renderThresholdSliders) {
      window.renderThresholdSliders = (...args) => {
        console.log('[EnhancedThresholdSliders] 🚫 BLOCKED: Legacy renderThresholdSliders → Enhanced Sliders');
        // Use our enhanced system instead
        this.render(args[0]);
      };
    }
  }

  loadThresholds() {
    // Load from localStorage or use defaults
    const saved = localStorage.getItem('enhancedThresholds');
    if (saved) {
      try {
        const savedThresholds = JSON.parse(saved);
        Object.assign(this.defaultThresholds, savedThresholds);
      } catch (e) {
        console.warn('Failed to load saved thresholds, using defaults');
      }
    }
  }

  saveThresholds() {
    localStorage.setItem('enhancedThresholds', JSON.stringify(this.defaultThresholds));
  }

  renderThresholdSliders(strategy = null) {
    console.log(`🎚️ ENHANCED THRESHOLD SLIDERS: Rendering for strategy: ${strategy || 'default'}`);

    // Look for the correct container ID that matches threshold-sliders.js
    let container = document.getElementById('threshold-sliders') || document.getElementById('thresholdsMenu');
    if (!container) {
      console.warn('🎚️ ENHANCED THRESHOLD SLIDERS: Container not found, creating one...');
      // Create the container if it doesn't exist
      container = document.createElement('div');
      container.id = 'threshold-sliders';
      container.className = 'menu-container thresholds-menu';
      container.style.display = 'none';

      // Add to ticker container or body
      const tickerContainer = document.getElementById('tickerContainer');
      if (tickerContainer) {
        tickerContainer.appendChild(container);
      } else {
        document.body.appendChild(container);
      }
    } else {
      console.log(`🎚️ ENHANCED THRESHOLD SLIDERS: Container found: ${container.id}`);
    }

    this.currentStrategy = strategy || window.currentStrategy || 'admiral_toa';
    const strategyData = window.TRADING_STRATEGIES?.[this.currentStrategy];
    const indicators = strategyData?.indicators || Object.keys(this.defaultThresholds);

    console.log(`🎚️ ENHANCED THRESHOLD SLIDERS: Strategy: ${this.currentStrategy}, Indicators: ${indicators.join(', ')}`);

    container.innerHTML = this.generateSlidersHTML(indicators);
    console.log(`🎚️ ENHANCED THRESHOLD SLIDERS: HTML generated and inserted into container`);
    this.addEnhancedStyles();
    this.bindSliderEvents();
    this.updateAllSliders();

    console.log(`🎚️ Rendered ${indicators.length} enhanced threshold sliders`);
  }

  generateSlidersHTML(indicators) {
    return `
      <div class="enhanced-threshold-sliders" style="
        background: rgba(10, 10, 30, 0.95);
        border: 2px solid #00FFFF;
        border-radius: 8px;
        padding: 20px;
        color: #00FFFF;
        font-family: 'Orbitron', monospace;
        width: 100%;
        margin: 0;
        box-sizing: border-box;
      ">
        <div class="sliders-header" style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          border-bottom: 1px solid rgba(0, 255, 255, 0.3);
          padding-bottom: 10px;
        ">
          <h3 style="margin: 0; color: #00FFFF; font-size: 1.4em;">🎚️ 5-Color Logic Thresholds</h3>
          <div class="header-controls" style="display: flex; gap: 10px;">
            <button class="live-toggle ${this.liveAdjustment ? 'active' : ''}" id="liveAdjustmentToggle" style="
              background: ${this.liveAdjustment ? '#00FF00' : 'rgba(0, 255, 255, 0.2)'};
              color: ${this.liveAdjustment ? '#000' : '#00FFFF'};
              border: 1px solid #00FFFF;
              padding: 5px 10px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 1.0em;
            ">
              <span class="toggle-icon">⚡</span>
              Live Adjustment
            </button>
            <button class="reset-all-button" id="resetAllThresholds" style="
              background: rgba(255, 0, 0, 0.2);
              color: #FF0000;
              border: 1px solid #FF0000;
              padding: 5px 10px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 1.0em;
            ">Reset All</button>
          </div>
        </div>

        <div class="strategy-info">
          <span class="current-strategy">Strategy: ${this.getStrategyName()}</span>
          <span class="indicators-count">${indicators.length} indicators</span>
        </div>

        <div class="color-legend">
          <div class="legend-item green">
            <span class="color-box"></span>
            <span class="label">Strong Buy</span>
          </div>
          <div class="legend-item blue">
            <span class="color-box"></span>
            <span class="label">Mild Buy</span>
          </div>
          <div class="legend-item grey">
            <span class="color-box"></span>
            <span class="label">Neutral</span>
          </div>
          <div class="legend-item orange">
            <span class="color-box"></span>
            <span class="label">Mild Sell</span>
          </div>
          <div class="legend-item red">
            <span class="color-box"></span>
            <span class="label">Strong Sell</span>
          </div>
        </div>

        <div class="sliders-container">
          ${indicators.map(indicator => this.generateSliderHTML(indicator)).join('')}
        </div>

        <div class="global-controls">
          <button class="apply-button" id="applyThresholds">Apply Changes</button>
          <button class="export-button" id="exportThresholds">Export Settings</button>
          <button class="import-button" id="importThresholds">Import Settings</button>
        </div>
      </div>
    `;
  }

  generateSliderHTML(indicator) {
    const thresholds = this.defaultThresholds[indicator] || { green: 20, blue: 40, orange: 60, red: 80 };
    const displayName = this.getIndicatorDisplayName(indicator);

    return `
      <div class="enhanced-slider-group" data-indicator="${indicator}">
        <div class="slider-header">
          <h4>${displayName}</h4>
        </div>

        <div class="slider-container" id="${indicator}-slider">
          <div class="slider-track">
            <!-- 5-Color Segments -->
            <div class="color-segment green-segment" data-color="green"></div>
            <div class="color-segment blue-segment" data-color="blue"></div>
            <div class="color-segment grey-segment" data-color="grey"></div>
            <div class="color-segment orange-segment" data-color="orange"></div>
            <div class="color-segment red-segment" data-color="red"></div>

            <!-- 4 Draggable Thumbs with Value Labels Above -->
            <div class="slider-thumb green-thumb"
                 data-indicator="${indicator}"
                 data-type="green"
                 data-value="${thresholds.green}"
                 style="left: ${thresholds.green}%"
                 title="Green Threshold: ${thresholds.green}%">
              <span class="thumb-label">G</span>
              <span class="thumb-value green-value" style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-size: 10px; color: #00FF00; font-weight: bold; background: rgba(0,0,0,0.7); padding: 2px 4px; border-radius: 3px;">${thresholds.green}%</span>
            </div>
            <div class="slider-thumb blue-thumb"
                 data-indicator="${indicator}"
                 data-type="blue"
                 data-value="${thresholds.blue}"
                 style="left: ${thresholds.blue}%"
                 title="Blue Threshold: ${thresholds.blue}%">
              <span class="thumb-label">B</span>
              <span class="thumb-value blue-value" style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-size: 10px; color: #0080FF; font-weight: bold; background: rgba(0,0,0,0.7); padding: 2px 4px; border-radius: 3px;">${thresholds.blue}%</span>
            </div>
            <div class="slider-thumb orange-thumb"
                 data-indicator="${indicator}"
                 data-type="orange"
                 data-value="${thresholds.orange}"
                 style="left: ${thresholds.orange}%"
                 title="Orange Threshold: ${thresholds.orange}%">
              <span class="thumb-label">O</span>
              <span class="thumb-value orange-value" style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-size: 10px; color: #FFA500; font-weight: bold; background: rgba(0,0,0,0.7); padding: 2px 4px; border-radius: 3px;">${thresholds.orange}%</span>
            </div>
            <div class="slider-thumb red-thumb"
                 data-indicator="${indicator}"
                 data-type="red"
                 data-value="${thresholds.red}"
                 style="left: ${thresholds.red}%"
                 title="Red Threshold: ${thresholds.red}%">
              <span class="thumb-label">R</span>
              <span class="thumb-value red-value" style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-size: 10px; color: #FF0000; font-weight: bold; background: rgba(0,0,0,0.7); padding: 2px 4px; border-radius: 3px;">${thresholds.red}%</span>
            </div>
          </div>

          <!-- Scale moved below track to not interfere with bar width -->
          <div class="slider-scale" style="margin-top: 10px;">
            <span class="scale-mark" style="left: 0%; transform: translateX(0%)">0</span>
            <span class="scale-mark" style="left: 25%; transform: translateX(-50%)">25</span>
            <span class="scale-mark" style="left: 50%; transform: translateX(-50%)">50</span>
            <span class="scale-mark" style="left: 75%; transform: translateX(-50%)">75</span>
            <span class="scale-mark" style="left: 100%; transform: translateX(-100%)">100</span>
          </div>
        </div>

        <div class="slider-actions">
          <button class="reset-button" data-indicator="${indicator}">Reset</button>
          <button class="preset-button" data-indicator="${indicator}">Preset</button>
        </div>
      </div>
    `;
  }

  addEnhancedStyles() {
    if (document.getElementById('enhanced-threshold-styles')) return;

    const style = document.createElement('style');
    style.id = 'enhanced-threshold-styles';
    style.textContent = `
      /* 🎚️ ENHANCED THRESHOLD SLIDERS - PROPER 4-THUMB IMPLEMENTATION */
      .enhanced-slider-group {
        margin-bottom: 25px;
        padding: 20px;
        background: rgba(0, 20, 40, 0.4);
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        min-height: 100px;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
        position: relative;
        z-index: 10;
        width: 100%;
        box-sizing: border-box;
        overflow: visible;
      }

      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      .slider-header h4 {
        margin: 0;
        color: #00FFFF;
        font-size: 1.1em;
        text-transform: uppercase;
      }

      .threshold-values {
        display: flex;
        gap: 6px;
        font-size: 0.9em;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;
      }

      .threshold-values .value {
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        min-width: 32px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-sizing: border-box;
      }

      .green-value { background: rgba(0, 255, 0, 0.2); color: #00FF00; }
      .blue-value { background: rgba(0, 0, 255, 0.2); color: #0080FF; }
      .orange-value { background: rgba(255, 165, 0, 0.2); color: #FFA500; }
      .red-value { background: rgba(255, 0, 0, 0.2); color: #FF0000; }

      /* SLIDER CONTAINER - PROPER SIZING */
      .slider-container {
        position: relative;
        width: 100%;
        height: 50px;
        margin: 20px 0;
        padding: 0;
        box-sizing: border-box;
      }

      /* SLIDER TRACK - FULL WIDTH WITH 5 COLOR SEGMENTS - FIXED SCALING */
      .slider-track {
        position: relative !important;
        width: 100% !important;
        height: 20px !important;
        background: linear-gradient(to right,
          #00FF00 0%, #00FF00 20%,
          #0080FF 20%, #0080FF 40%,
          #808080 40%, #808080 60%,
          #FFA500 60%, #FFA500 80%,
          #FF0000 80%, #FF0000 100%
        );
        border: 1px solid #00FFFF;
        border-radius: 10px;
        cursor: pointer;
        box-sizing: border-box !important;
        margin: 10px 0;
        overflow: visible;
        z-index: 5;
        box-shadow: 0 0 8px rgba(0, 255, 255, 0.4);
      }

      /* COLOR SEGMENTS - VISUAL INDICATORS */
      .color-segment {
        position: absolute;
        top: 0;
        height: 100%;
        pointer-events: none;
        opacity: 0.7;
      }

      .green-segment { left: 0%; width: 20%; background: linear-gradient(90deg, #00FF00, #00CC00); }
      .blue-segment { left: 20%; width: 20%; background: linear-gradient(90deg, #0080FF, #0060CC); }
      .grey-segment { left: 40%; width: 20%; background: linear-gradient(90deg, #808080, #606060); }
      .orange-segment { left: 60%; width: 20%; background: linear-gradient(90deg, #FFA500, #CC8400); }
      .red-segment { left: 80%; width: 20%; background: linear-gradient(90deg, #FF0000, #CC0000); }

      /* 4 DRAGGABLE THUMBS - FIXED POSITIONING TO STAY ON BAR */
      .slider-thumb {
        position: absolute !important;
        top: -8px !important;
        width: 16px !important;
        height: 36px !important;
        background: linear-gradient(135deg, #00FFFF, #0080FF);
        border: 2px solid #FFF !important;
        border-radius: 6px;
        cursor: grab;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        font-weight: bold;
        color: #000;
        z-index: 20 !important;
        box-shadow: 0 3px 8px rgba(0, 255, 255, 0.6);
        transform: translateX(-50%) !important;
        transition: all 0.2s ease;
        box-sizing: border-box !important;
        user-select: none;
        pointer-events: auto;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
      }

      .slider-thumb:hover {
        transform: translateX(-50%) scale(1.15);
        box-shadow: 0 6px 16px rgba(0, 255, 255, 0.8);
        background: linear-gradient(135deg, #00FFFF, #00AAFF);
      }

      .slider-thumb:active,
      .slider-thumb.dragging {
        cursor: grabbing;
        transform: translateX(-50%) scale(1.25);
        z-index: 25;
        box-shadow: 0 8px 20px rgba(0, 255, 255, 1.0);
      }

      /* ENHANCED THUMB COLORS WITH GRADIENTS */
      .green-thumb {
        background: linear-gradient(135deg, #00FF00, #00CC00);
        border-color: #00AA00;
      }
      .blue-thumb {
        background: linear-gradient(135deg, #0080FF, #0060CC);
        border-color: #0040AA;
      }
      .orange-thumb {
        background: linear-gradient(135deg, #FFA500, #CC8400);
        border-color: #AA6600;
      }
      .red-thumb {
        background: linear-gradient(135deg, #FF0000, #CC0000);
        border-color: #AA0000;
      }

      /* THUMB LABELS */
      .thumb-label {
        font-size: 10px;
        font-weight: bold;
        color: #000;
        text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5);
      }

      /* THUMB VALUE LABELS - POSITIONED ABOVE EACH THUMB */
      .thumb-value {
        position: absolute !important;
        top: -25px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        font-size: 10px !important;
        font-weight: bold !important;
        background: rgba(0, 0, 0, 0.8) !important;
        padding: 2px 6px !important;
        border-radius: 4px !important;
        pointer-events: none !important;
        white-space: nowrap !important;
        z-index: 30 !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
      }

      /* SLIDER SCALE - FIXED TO SPAN FULL WIDTH */
      .slider-scale {
        position: relative !important;
        width: 100% !important;
        height: 20px !important;
        margin-top: 8px;
        padding: 0;
        box-sizing: border-box !important;
        display: block !important;
      }

      .scale-mark {
        position: absolute;
        font-size: 11px;
        color: #00FFFF;
        font-weight: bold;
        text-shadow: 0 0 2px rgba(0, 255, 255, 0.5);
        top: 0;
      }

      .scale-mark:first-child { transform: translateX(0%); }
      .scale-mark:last-child { transform: translateX(-100%); }
      .scale-mark:not(:first-child):not(:last-child) { transform: translateX(-50%); }

      /* SLIDER ACTIONS */
      .slider-actions {
        display: flex;
        gap: 8px;
        margin-top: 10px;
      }

      .slider-actions button {
        padding: 4px 8px;
        font-size: 0.9em;
        border: 1px solid #00FFFF;
        background: rgba(0, 255, 255, 0.1);
        color: #00FFFF;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .slider-actions button:hover {
        background: rgba(0, 255, 255, 0.2);
        box-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
      }

      /* GLOBAL CONTROLS */
      .global-controls {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid rgba(0, 255, 255, 0.3);
      }

      .global-controls button {
        padding: 8px 16px;
        font-size: 1.1em;
        border: 2px solid #00FFFF;
        background: rgba(0, 255, 255, 0.1);
        color: #00FFFF;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Orbitron', monospace;
      }

      .global-controls button:hover {
        background: rgba(0, 255, 255, 0.2);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
        transform: translateY(-2px);
      }

      .apply-button:hover {
        background: rgba(0, 255, 0, 0.2) !important;
        border-color: #00FF00 !important;
        color: #00FF00 !important;
      }

      /* RESPONSIVE DESIGN */
      @media (max-width: 600px) {
        .enhanced-threshold-sliders {
          max-width: 100%;
          margin: 5px;
          padding: 10px;
        }

        .slider-header {
          flex-direction: column;
          gap: 8px;
        }

        .threshold-values {
          justify-content: center;
        }

        .global-controls {
          flex-direction: column;
          align-items: center;
        }
      }

      .enhanced-threshold-sliders {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 30, 60, 0.95));
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px;
        padding: 20px;
        font-family: 'Orbitron', sans-serif;
        color: #FFFFFF;
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
      }

      .sliders-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.3);
      }

      .sliders-header h3 {
        margin: 0;
        color: #00FFFF;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      .header-controls {
        display: flex;
        gap: 10px;
      }

      .live-toggle {
        background: rgba(0, 255, 0, 0.2);
        border: 1px solid #00FF00;
        color: #00FF00;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Orbitron', sans-serif;
      }

      .live-toggle.active {
        background: rgba(0, 255, 0, 0.4);
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
      }

      .reset-all-button {
        background: rgba(255, 100, 100, 0.2);
        border: 1px solid #FF6464;
        color: #FF6464;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Orbitron', sans-serif;
      }

      .strategy-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        padding: 10px;
        background: rgba(0, 255, 255, 0.1);
        border-radius: 5px;
        font-size: 0.9rem;
      }

      .current-strategy {
        color: #00FFFF;
        font-weight: bold;
      }

      .indicators-count {
        color: #00FF00;
      }

      .color-legend {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        padding: 10px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 8px;
        flex-wrap: wrap;
        gap: 10px;
      }

      .legend-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.8rem;
      }

      .color-box {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .legend-item.green .color-box { background: #00FF00; }
      .legend-item.blue .color-box { background: #0080FF; }
      .legend-item.grey .color-box { background: #808080; }
      .legend-item.orange .color-box { background: #FFA500; }
      .legend-item.red .color-box { background: #FF0000; }

      .sliders-container {
        max-height: 70vh;
        overflow-y: auto;
        overflow-x: hidden;
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(0, 10, 20, 0.95);
        border-radius: 12px;
        border: 2px solid rgba(0, 255, 255, 0.5);
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
        position: relative;
        z-index: 1000;
        width: 100%;
        box-sizing: border-box;
      }

      .enhanced-slider-group {
        margin-bottom: 25px;
        padding: 20px;
        background: rgba(0, 20, 40, 0.8);
        border-radius: 12px;
        border: 2px solid rgba(0, 255, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        width: 100%;
        box-sizing: border-box;
        min-height: 120px;
        overflow: visible;
      }

      .enhanced-slider-group:hover {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(0, 255, 255, 0.3);
      }

      .slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .slider-header h4 {
        margin: 0;
        color: #00CCFF;
        font-size: 1rem;
        font-weight: 600;
      }

      .threshold-values {
        display: flex;
        gap: 10px;
        font-size: 0.8rem;
        font-family: 'Roboto Mono', monospace;
        width: 100%;
        box-sizing: border-box;
        justify-content: space-between;
        padding: 0 5px;
      }

      .threshold-values .value {
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: bold;
        min-width: 25px;
        text-align: center;
      }

      .green-value { background: rgba(0, 255, 0, 0.2); color: #00FF00; }
      .blue-value { background: rgba(0, 128, 255, 0.2); color: #0080FF; }
      .orange-value { background: rgba(255, 165, 0, 0.2); color: #FFA500; }
      .red-value { background: rgba(255, 0, 0, 0.2); color: #FF0000; }

      /* REMOVED CONFLICTING SLIDER-CONTAINER CSS - USING MAIN DEFINITION ABOVE */

      /* REMOVED CONFLICTING SLIDER-TRACK CSS - USING MAIN DEFINITION ABOVE */

      .color-segment {
        position: absolute;
        height: 100%;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .green-segment { background: linear-gradient(90deg, #00FF00, #00CC00); }
      .blue-segment { background: linear-gradient(90deg, #0080FF, #0060CC); }
      .grey-segment { background: linear-gradient(90deg, #808080, #606060); }
      .orange-segment { background: linear-gradient(90deg, #FFA500, #CC8400); }
      .red-segment { background: linear-gradient(90deg, #FF0000, #CC0000); }

      /* REMOVED CONFLICTING SLIDER-THUMB CSS - USING MAIN DEFINITION ABOVE */

      .slider-thumb:hover {
        transform: translateX(-50%) scale(1.1);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
      }

      .green-thumb { background: linear-gradient(135deg, #00FF00, #00CC00); }
      .blue-thumb { background: linear-gradient(135deg, #0080FF, #0060CC); }
      .orange-thumb { background: linear-gradient(135deg, #FFA500, #CC8400); }
      .red-thumb { background: linear-gradient(135deg, #FF0000, #CC0000); }

      .slider-thumb:active {
        cursor: grabbing;
        transform: translateX(-50%) scale(1.2);
      }

      .green-thumb { background: #00FF00; z-index: 14; }
      .blue-thumb { background: #0080FF; z-index: 13; }
      .orange-thumb { background: #FFA500; z-index: 12; }
      .red-thumb { background: #FF0000; z-index: 11; }

      .thumb-label {
        pointer-events: none;
      }

      /* REMOVED CONFLICTING SLIDER-SCALE CSS - USING MAIN DEFINITION ABOVE */

      .scale-mark {
        position: absolute;
        top: 0;
        font-size: 0.7rem;
        color: #CCCCCC;
        white-space: nowrap;
      }

      .scale-mark {
        position: relative;
      }

      .slider-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }

      .reset-button, .preset-button {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #FFFFFF;
        padding: 5px 10px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.3s ease;
        font-family: 'Orbitron', sans-serif;
      }

      .reset-button:hover {
        background: rgba(255, 100, 100, 0.3);
        border-color: #FF6464;
      }

      .preset-button:hover {
        background: rgba(255, 215, 0, 0.3);
        border-color: #FFD700;
      }

      .global-controls {
        display: flex;
        gap: 10px;
        justify-content: center;
        border-top: 1px solid rgba(0, 255, 255, 0.3);
        padding-top: 15px;
      }

      .apply-button, .export-button, .import-button {
        flex: 1;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 5px;
        background: rgba(255, 255, 255, 0.1);
        color: #FFFFFF;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Orbitron', sans-serif;
        font-weight: bold;
      }

      .apply-button {
        border-color: #00FF00;
        color: #00FF00;
      }

      .export-button {
        border-color: #00CCFF;
        color: #00CCFF;
      }

      .import-button {
        border-color: #FFD700;
        color: #FFD700;
      }

      .apply-button:hover { background: rgba(0, 255, 0, 0.2); }
      .export-button:hover { background: rgba(0, 204, 255, 0.2); }
      .import-button:hover { background: rgba(255, 215, 0, 0.2); }
    `;
    document.head.appendChild(style);
  }

  bindSliderEvents() {
    const container = document.querySelector('.enhanced-threshold-sliders');
    if (!container) return;

    // Bind thumb drag events
    const thumbs = container.querySelectorAll('.slider-thumb');
    thumbs.forEach(thumb => {
      thumb.addEventListener('mousedown', (e) => this.startDrag(e, thumb));
    });

    // Bind control button events
    const liveToggle = document.getElementById('liveAdjustmentToggle');
    if (liveToggle) {
      liveToggle.addEventListener('click', () => this.toggleLiveAdjustment());
    }

    const resetAllBtn = document.getElementById('resetAllThresholds');
    if (resetAllBtn) {
      resetAllBtn.addEventListener('click', () => this.resetAllThresholds());
    }

    const applyBtn = document.getElementById('applyThresholds');
    if (applyBtn) {
      applyBtn.addEventListener('click', () => this.applyThresholds());
    }

    // Bind individual reset buttons
    const resetButtons = container.querySelectorAll('.reset-button');
    resetButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const indicator = e.target.dataset.indicator;
        this.resetIndicatorThresholds(indicator);
      });
    });

    // Bind preset buttons
    const presetButtons = container.querySelectorAll('.preset-button');
    presetButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const indicator = e.target.dataset.indicator;
        this.showPresetMenu(indicator, e.target);
      });
    });
  }

  bindGlobalEvents() {
    // Global mouse events for dragging
    document.addEventListener('mousemove', (e) => this.handleDrag(e));
    document.addEventListener('mouseup', () => this.endDrag());

    // Prevent text selection during drag
    document.addEventListener('selectstart', (e) => {
      if (this.isDragging) e.preventDefault();
    });
  }

  startDrag(e, thumb) {
    e.preventDefault();
    this.isDragging = true;
    this.activeThumb = thumb;
    this.startX = e.clientX;
    this.startLeft = parseFloat(thumb.style.left) || 0;

    thumb.classList.add('dragging');
    document.body.style.cursor = 'grabbing';
    document.body.style.userSelect = 'none';
  }

  handleDrag(e) {
    if (!this.isDragging || !this.activeThumb) return;

    const thumb = this.activeThumb;
    const container = thumb.closest('.slider-container');
    const track = container.querySelector('.slider-track');
    const trackRect = track.getBoundingClientRect();

    // Calculate new position
    const deltaX = e.clientX - this.startX;
    const trackWidth = trackRect.width;
    const deltaPercent = (deltaX / trackWidth) * 100;
    let newPosition = this.startLeft + deltaPercent;

    // Constrain within bounds
    newPosition = Math.max(0, Math.min(100, newPosition));

    // Apply constraints based on other thumbs
    newPosition = this.constrainThumbPosition(thumb, newPosition);

    // Update thumb position
    thumb.style.left = `${newPosition}%`;
    thumb.dataset.value = Math.round(newPosition);
    thumb.title = `${thumb.dataset.type.charAt(0).toUpperCase() + thumb.dataset.type.slice(1)} Threshold: ${Math.round(newPosition)}%`;

    // Update threshold value
    const indicator = thumb.dataset.indicator;
    const type = thumb.dataset.type;
    this.defaultThresholds[indicator][type] = Math.round(newPosition);

    // Update visual display
    this.updateSliderSegments(indicator);
    this.updateThresholdValues(indicator);

    // Live adjustment
    if (this.liveAdjustment) {
      this.applyThresholds();
    }
  }

  constrainThumbPosition(thumb, newPosition) {
    const indicator = thumb.dataset.indicator;
    const type = thumb.dataset.type;
    const container = thumb.closest('.slider-container');
    const otherThumbs = container.querySelectorAll('.slider-thumb');

    // Get current positions of all thumbs
    const positions = {};
    otherThumbs.forEach(t => {
      if (t !== thumb) {
        positions[t.dataset.type] = parseFloat(t.style.left) || 0;
      }
    });
    positions[type] = newPosition;

    // Enforce ordering: green < blue < orange < red
    const order = ['green', 'blue', 'orange', 'red'];
    const currentIndex = order.indexOf(type);

    // Check constraints with previous thumb
    if (currentIndex > 0) {
      const prevType = order[currentIndex - 1];
      if (positions[prevType] !== undefined) {
        newPosition = Math.max(newPosition, positions[prevType] + 1);
      }
    }

    // Check constraints with next thumb
    if (currentIndex < order.length - 1) {
      const nextType = order[currentIndex + 1];
      if (positions[nextType] !== undefined) {
        newPosition = Math.min(newPosition, positions[nextType] - 1);
      }
    }

    return newPosition;
  }

  endDrag() {
    if (!this.isDragging) return;

    this.isDragging = false;
    if (this.activeThumb) {
      this.activeThumb.classList.remove('dragging');
      this.activeThumb = null;
    }

    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    // Save thresholds
    this.saveThresholds();
  }

  updateSliderSegments(indicator) {
    const container = document.querySelector(`[data-indicator="${indicator}"] .slider-track`);
    if (!container) {
      console.warn(`[EnhancedThresholdSliders] Container not found for indicator: ${indicator}`);
      return;
    }

    const thresholds = this.defaultThresholds[indicator];
    if (!thresholds) {
      console.warn(`[EnhancedThresholdSliders] Thresholds not found for indicator: ${indicator}`);
      return;
    }

    const segments = container.querySelectorAll('.color-segment');
    if (segments.length !== 5) {
      console.warn(`[EnhancedThresholdSliders] Expected 5 segments, found ${segments.length} for indicator: ${indicator}`);
      return;
    }

    // Calculate segment widths based on threshold positions
    const green = thresholds.green;
    const blue = thresholds.blue;
    const orange = thresholds.orange;
    const red = thresholds.red;

    console.log(`[EnhancedThresholdSliders] Updating segments for ${indicator}:`, { green, blue, orange, red });

    // Update segment positions and widths
    if (segments[0]) {
      segments[0].style.left = '0%';
      segments[0].style.width = `${green}%`;
    }

    if (segments[1]) {
      segments[1].style.left = `${green}%`;
      segments[1].style.width = `${blue - green}%`;
    }

    if (segments[2]) {
      segments[2].style.left = `${blue}%`;
      segments[2].style.width = `${orange - blue}%`;
    }

    if (segments[3]) {
      segments[3].style.left = `${orange}%`;
      segments[3].style.width = `${red - orange}%`;
    }

    if (segments[4]) {
      segments[4].style.left = `${red}%`;
      segments[4].style.width = `${100 - red}%`;
    }
  }

  // Alias for compatibility
  updateSegmentPositions(indicator) {
    this.updateSliderSegments(indicator);
  }

  updateThresholdValues(indicator) {
    // Update thumb value labels positioned above each thumb
    const sliderContainer = document.getElementById(`${indicator}-slider`);
    if (!sliderContainer) return;

    const thresholds = this.defaultThresholds[indicator];
    const thumbs = sliderContainer.querySelectorAll('.slider-thumb');

    thumbs.forEach(thumb => {
      const type = thumb.dataset.type;
      const valueLabel = thumb.querySelector('.thumb-value');
      if (thresholds[type] !== undefined && valueLabel) {
        valueLabel.textContent = `${thresholds[type]}%`;
      }
    });
  }

  updateAllSliders() {
    Object.keys(this.defaultThresholds).forEach(indicator => {
      this.updateSliderSegments(indicator);
      this.updateThresholdValues(indicator);
    });
  }

  toggleLiveAdjustment() {
    this.liveAdjustment = !this.liveAdjustment;
    const toggle = document.getElementById('liveAdjustmentToggle');
    if (toggle) {
      toggle.classList.toggle('active', this.liveAdjustment);
    }

    this.showNotification(`Live adjustment ${this.liveAdjustment ? 'enabled' : 'disabled'}`);
  }

  resetAllThresholds() {
    if (!confirm('Reset all thresholds to default values?')) return;

    // Reset to original defaults
    Object.keys(this.defaultThresholds).forEach(indicator => {
      this.resetIndicatorThresholds(indicator, false);
    });

    this.updateAllSliders();
    this.saveThresholds();
    this.applyThresholds();
    this.showNotification('All thresholds reset to defaults');
  }

  resetIndicatorThresholds(indicator, apply = true) {
    const originalDefaults = {
      rsi: { green: 20, blue: 40, orange: 60, red: 80 },
      macd: { green: 15, blue: 35, orange: 65, red: 85 },
      stochRsi: { green: 20, blue: 40, orange: 60, red: 80 },
      bollingerBands: { green: 5, blue: 25, orange: 75, red: 95 },
      atr: { green: 10, blue: 30, orange: 70, red: 90 },
      volume: { green: 20, blue: 40, orange: 60, red: 80 },
      mfi: { green: 20, blue: 40, orange: 60, red: 80 },
      williamsR: { green: 80, blue: 60, orange: 40, red: 20 },
      cci: { green: 15, blue: 35, orange: 65, red: 85 },
      ultimateOscillator: { green: 20, blue: 40, orange: 60, red: 80 },
      adx: { green: 15, blue: 35, orange: 65, red: 85 },
      mlPrediction: { green: 20, blue: 40, orange: 60, red: 80 }
    };

    if (originalDefaults[indicator]) {
      this.defaultThresholds[indicator] = { ...originalDefaults[indicator] };

      // Update thumb positions
      const container = document.querySelector(`[data-indicator="${indicator}"]`);
      if (container) {
        const thumbs = container.querySelectorAll('.slider-thumb');
        thumbs.forEach(thumb => {
          const type = thumb.dataset.type;
          const value = this.defaultThresholds[indicator][type];
          thumb.style.left = `${value}%`;
          thumb.dataset.value = value;
          thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)} Threshold: ${value}%`;
        });
      }

      this.updateSliderSegments(indicator);
      this.updateThresholdValues(indicator);

      if (apply) {
        this.saveThresholds();
        this.applyThresholds();
        this.showNotification(`${this.getIndicatorDisplayName(indicator)} thresholds reset`);
      }
    }
  }

  applyThresholds() {
    // Update global thresholds object if it exists
    if (window.thresholds) {
      Object.assign(window.thresholds, this.defaultThresholds);
    }

    // Update signal lights if function exists
    if (typeof window.updateAllSignalLights === 'function') {
      window.updateAllSignalLights();
    }

    // Update Oracle Matrix if function exists
    if (typeof window.updateSignalMatrix === 'function') {
      window.updateSignalMatrix();
    }

    // Send to server if WebSocket is available
    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
      window.ws.send(JSON.stringify({
        type: 'updateThresholds',
        thresholds: this.defaultThresholds
      }));
    }

    console.log('🎚️ Applied threshold changes to signal system');
  }

  showPresetMenu(indicator, button) {
    const presets = {
      conservative: { green: 15, blue: 35, orange: 65, red: 85 },
      moderate: { green: 20, blue: 40, orange: 60, red: 80 },
      aggressive: { green: 25, blue: 45, orange: 55, red: 75 },
      scalping: { green: 30, blue: 50, orange: 50, red: 70 }
    };

    // Create preset menu
    const menu = document.createElement('div');
    menu.className = 'preset-menu';
    menu.innerHTML = `
      <div class="preset-header">Presets for ${this.getIndicatorDisplayName(indicator)}</div>
      ${Object.entries(presets).map(([name, values]) => `
        <div class="preset-option" data-preset="${name}">
          <span class="preset-name">${name.charAt(0).toUpperCase() + name.slice(1)}</span>
          <span class="preset-values">G:${values.green} B:${values.blue} O:${values.orange} R:${values.red}</span>
        </div>
      `).join('')}
    `;

    // Position menu
    const rect = button.getBoundingClientRect();
    menu.style.position = 'absolute';
    menu.style.top = `${rect.bottom + 5}px`;
    menu.style.left = `${rect.left}px`;
    menu.style.zIndex = '1000';
    menu.style.background = 'rgba(0, 20, 40, 0.95)';
    menu.style.border = '1px solid rgba(0, 255, 255, 0.3)';
    menu.style.borderRadius = '5px';
    menu.style.padding = '10px';
    menu.style.minWidth = '200px';

    document.body.appendChild(menu);

    // Handle preset selection
    menu.addEventListener('click', (e) => {
      const option = e.target.closest('.preset-option');
      if (option) {
        const presetName = option.dataset.preset;
        const preset = presets[presetName];
        this.applyPreset(indicator, preset);
        document.body.removeChild(menu);
      }
    });

    // Close menu when clicking outside
    setTimeout(() => {
      document.addEventListener('click', function closeMenu(e) {
        if (!menu.contains(e.target)) {
          if (document.body.contains(menu)) {
            document.body.removeChild(menu);
          }
          document.removeEventListener('click', closeMenu);
        }
      });
    }, 100);
  }

  applyPreset(indicator, preset) {
    this.defaultThresholds[indicator] = { ...preset };

    // Update thumb positions
    const container = document.querySelector(`[data-indicator="${indicator}"]`);
    if (container) {
      const thumbs = container.querySelectorAll('.slider-thumb');
      thumbs.forEach(thumb => {
        const type = thumb.dataset.type;
        const value = preset[type];
        thumb.style.left = `${value}%`;
        thumb.dataset.value = value;
        thumb.title = `${type.charAt(0).toUpperCase() + type.slice(1)} Threshold: ${value}%`;
      });
    }

    this.updateSliderSegments(indicator);
    this.updateThresholdValues(indicator);
    this.saveThresholds();

    if (this.liveAdjustment) {
      this.applyThresholds();
    }

    this.showNotification(`Applied preset to ${this.getIndicatorDisplayName(indicator)}`);
  }

  getIndicatorDisplayName(indicator) {
    const displayNames = {
      rsi: 'RSI',
      macd: 'MACD',
      stochRsi: 'Stochastic RSI',
      bollingerBands: 'Bollinger Bands',
      atr: 'ATR',
      volume: 'Volume',
      mfi: 'Money Flow Index',
      williamsR: 'Williams %R',
      cci: 'CCI',
      ultimateOscillator: 'Ultimate Oscillator',
      adx: 'ADX',
      mlPrediction: 'ML Prediction',
      sma: 'SMA',
      ema: 'EMA',
      vwap: 'VWAP',
      obv: 'OBV',
      momentum: 'Momentum',
      roc: 'Rate of Change'
    };

    return displayNames[indicator] || indicator.toUpperCase();
  }

  getStrategyName() {
    const strategies = window.TRADING_STRATEGIES || {};
    const strategy = strategies[this.currentStrategy];
    return strategy?.name || this.currentStrategy.replace('_', ' ').toUpperCase();
  }

  showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'threshold-notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 255, 255, 0.9);
      color: #000;
      padding: 10px 15px;
      border-radius: 5px;
      font-family: 'Orbitron', sans-serif;
      font-weight: bold;
      z-index: 10000;
      animation: slideIn 0.3s ease-out;
    `;

    // Add animation keyframes if not already added
    if (!document.getElementById('threshold-notification-styles')) {
      const style = document.createElement('style');
      style.id = 'threshold-notification-styles';
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  exportSettings() {
    const settings = {
      thresholds: this.defaultThresholds,
      strategy: this.currentStrategy,
      liveAdjustment: this.liveAdjustment,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `starcrypt-thresholds-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    this.showNotification('Settings exported successfully');
  }

  importSettings(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const settings = JSON.parse(e.target.result);
        if (settings.thresholds) {
          Object.assign(this.defaultThresholds, settings.thresholds);
          this.saveThresholds();
          this.updateAllSliders();
          this.applyThresholds();
          this.showNotification('Settings imported successfully');
        }
      } catch (error) {
        this.showNotification('Error importing settings');
        console.error('Import error:', error);
      }
    };
    reader.readAsText(file);
  }
}

// Initialize enhanced threshold sliders
window.enhancedThresholdSliders = new EnhancedThresholdSliders();

// Export for global access
window.EnhancedThresholdSliders = EnhancedThresholdSliders;