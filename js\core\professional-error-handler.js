/**
 * StarCrypt Professional Error Handler
 * Enterprise-grade error handling and logging system
 * Ensures trading integrity and system reliability
 */

class ProfessionalErrorHandler {
  constructor() {
    this.errorCategories = {
      TRADING_INTEGRITY: 'CRITICAL',
      DATA_VALIDATION: 'HIGH',
      WEBSOCKET_CONNECTION: 'HIGH',
      UI_RENDERING: 'MEDIUM',
      PERFORMANCE: 'LOW'
    };
    
    this.errorLog = [];
    this.maxLogSize = 1000;
    this.criticalErrorCount = 0;
    this.lastCriticalError = null;
    
    this.init();
  }

  init() {
    console.log('🛡️ PROFESSIONAL ERROR HANDLER: Initializing enterprise-grade error management...');
    
    this.setupGlobalErrorHandling();
    this.setupUnhandledRejectionHandling();
    this.setupConsoleInterception();
    this.setupPerformanceMonitoring();
    
    console.log('✅ PROFESSIONAL ERROR HANDLER: System armed and monitoring');
  }

  setupGlobalErrorHandling() {
    window.addEventListener('error', (event) => {
      this.handleError({
        type: 'JAVASCRIPT_ERROR',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        stack: event.error?.stack,
        timestamp: Date.now()
      });
    });
  }

  setupUnhandledRejectionHandling() {
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: 'UNHANDLED_PROMISE_REJECTION',
        message: event.reason?.message || 'Unhandled promise rejection',
        reason: event.reason,
        stack: event.reason?.stack,
        timestamp: Date.now()
      });
    });
  }

  setupConsoleInterception() {
    // DISABLED - Causing recursive error loops
    // Console interception disabled to prevent infinite recursion
    console.log('🛡️ PROFESSIONAL ERROR HANDLER: Console interception disabled to prevent recursion');
  }

  setupPerformanceMonitoring() {
    // Monitor for performance issues
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 100) { // Log slow operations
            this.handleError({
              type: 'PERFORMANCE_WARNING',
              message: `Slow operation detected: ${entry.name}`,
              duration: entry.duration,
              entryType: entry.entryType,
              timestamp: Date.now()
            });
          }
        }
      });
      
      observer.observe({ entryTypes: ['measure', 'navigation'] });
    }
  }

  handleError(errorData) {
    // Categorize error
    const category = this.categorizeError(errorData);
    const severity = this.errorCategories[category] || 'MEDIUM';
    
    // Create standardized error object
    const standardizedError = {
      id: this.generateErrorId(),
      timestamp: errorData.timestamp || Date.now(),
      category,
      severity,
      type: errorData.type,
      message: errorData.message,
      stack: errorData.stack,
      context: this.gatherContext(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Log error
    this.logError(standardizedError);

    // Handle critical errors
    if (severity === 'CRITICAL') {
      this.handleCriticalError(standardizedError);
    }

    // Attempt recovery
    this.attemptRecovery(standardizedError);

    return standardizedError;
  }

  categorizeError(errorData) {
    const message = errorData.message?.toLowerCase() || '';
    const stack = errorData.stack?.toLowerCase() || '';
    
    // Trading integrity violations
    if (message.includes('trading_integrity_violation') || 
        message.includes('dummy data') || 
        message.includes('fallback') ||
        message.includes('mock')) {
      return 'TRADING_INTEGRITY';
    }
    
    // Data validation errors
    if (message.includes('validation') || 
        message.includes('invalid data') ||
        message.includes('nan') ||
        message.includes('null') ||
        message.includes('undefined')) {
      return 'DATA_VALIDATION';
    }
    
    // WebSocket connection errors
    if (message.includes('websocket') || 
        message.includes('connection') ||
        stack.includes('websocket')) {
      return 'WEBSOCKET_CONNECTION';
    }
    
    // UI rendering errors
    if (message.includes('dom') || 
        message.includes('element') ||
        message.includes('render')) {
      return 'UI_RENDERING';
    }
    
    return 'GENERAL';
  }

  handleCriticalError(errorData) {
    this.criticalErrorCount++;
    this.lastCriticalError = errorData;
    
    console.error('🚨 CRITICAL ERROR DETECTED:', errorData);
    
    // Notify user of critical error
    this.notifyUser(errorData);
    
    // If too many critical errors, suggest page reload
    if (this.criticalErrorCount >= 3) {
      this.suggestPageReload();
    }
  }

  notifyUser(errorData) {
    // Create user-friendly error notification
    const notification = document.createElement('div');
    notification.className = 'critical-error-notification';
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(255, 68, 68, 0.4);
        z-index: 10000;
        max-width: 400px;
        font-family: 'Segoe UI', sans-serif;
      ">
        <div style="font-weight: bold; margin-bottom: 8px;">⚠️ System Alert</div>
        <div style="font-size: 14px; margin-bottom: 10px;">
          A critical error has been detected and logged. 
          ${errorData.category === 'TRADING_INTEGRITY' ? 'Trading operations may be affected.' : 'System functionality may be impaired.'}
        </div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
        ">Dismiss</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 10000);
  }

  suggestPageReload() {
    const shouldReload = confirm(
      'Multiple critical errors detected. Would you like to reload the page to restore system stability?'
    );
    
    if (shouldReload) {
      window.location.reload();
    }
  }

  attemptRecovery(errorData) {
    // Implement recovery strategies based on error category
    switch (errorData.category) {
      case 'WEBSOCKET_CONNECTION':
        this.recoverWebSocketConnection();
        break;
      case 'DATA_VALIDATION':
        this.recoverDataValidation();
        break;
      case 'UI_RENDERING':
        this.recoverUIRendering();
        break;
    }
  }

  recoverWebSocketConnection() {
    console.log('🛠️ RECOVERY: Attempting WebSocket reconnection...');
    
    if (window.wsManager && typeof window.wsManager.reconnect === 'function') {
      window.wsManager.reconnect();
    } else if (typeof window.connectWebSocket === 'function') {
      window.connectWebSocket();
    }
  }

  recoverDataValidation() {
    console.log('🛠️ RECOVERY: Attempting data validation recovery...');
    
    if (window.dataFlowVerifier && typeof window.dataFlowVerifier.forceVerification === 'function') {
      window.dataFlowVerifier.forceVerification();
    }
  }

  recoverUIRendering() {
    console.log('🛠️ RECOVERY: Attempting UI rendering recovery...');
    
    // Clear any stuck loading states
    const loadingElements = document.querySelectorAll('[data-loading="true"]');
    loadingElements.forEach(el => el.removeAttribute('data-loading'));
  }

  logError(errorData) {
    this.errorLog.push(errorData);
    
    // Trim log if too large
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }
    
    // Log to console with appropriate level
    const logMethod = errorData.severity === 'CRITICAL' ? 'error' : 
                     errorData.severity === 'HIGH' ? 'warn' : 'log';
    
    console[logMethod](`[${errorData.severity}] ${errorData.category}: ${errorData.message}`);
  }

  logConsoleMessage(level, args) {
    // DISABLED - This was causing recursive error loops
    // Console message logging disabled to prevent infinite recursion
    return;
  }

  gatherContext() {
    return {
      currentPrice: window.currentPrice,
      wsConnected: window.ws?.readyState === 1,
      indicatorsDataAvailable: !!window.indicatorsData,
      currentStrategy: window.currentStrategy,
      admiralMode: window.admiralMode,
      timestamp: Date.now()
    };
  }

  generateErrorId() {
    return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API
  getErrorLog() {
    return this.errorLog;
  }

  getErrorStats() {
    const stats = {
      total: this.errorLog.length,
      critical: this.errorLog.filter(e => e.severity === 'CRITICAL').length,
      high: this.errorLog.filter(e => e.severity === 'HIGH').length,
      medium: this.errorLog.filter(e => e.severity === 'MEDIUM').length,
      low: this.errorLog.filter(e => e.severity === 'LOW').length,
      lastCritical: this.lastCriticalError
    };
    
    return stats;
  }

  clearErrorLog() {
    this.errorLog = [];
    this.criticalErrorCount = 0;
    this.lastCriticalError = null;
    console.log('🛡️ ERROR HANDLER: Error log cleared');
  }
}

// Initialize professional error handler
window.professionalErrorHandler = new ProfessionalErrorHandler();

// Export for global access
window.ProfessionalErrorHandler = ProfessionalErrorHandler;

console.log('🛡️ PROFESSIONAL ERROR HANDLER: Enterprise-grade error management active');
