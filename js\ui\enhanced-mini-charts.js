/**
 * Enhanced Mini Charts for StarCrypt
 * Professional TradingView-style mini charts with proper multi-line indicators
 */

class EnhancedMiniCharts {
  constructor() {
    this.charts = new Map();
    this.chartConfigs = {
      rsi: { 
        type: 'line', 
        lines: 1, 
        range: [0, 100], 
        levels: [30, 70],
        colors: ['#ff6b6b']
      },
      macd: { 
        type: 'multi-line', 
        lines: 3, 
        range: 'auto',
        colors: ['#4ecdc4', '#45b7d1', '#96ceb4']
      },
      bollingerBands: { 
        type: 'multi-line', 
        lines: 3, 
        range: 'auto',
        colors: ['#feca57', '#ff9ff3', '#54a0ff']
      },
      ema: { 
        type: 'line', 
        lines: 1, 
        range: 'auto',
        colors: ['#5f27cd']
      },
      sma: { 
        type: 'line', 
        lines: 1, 
        range: 'auto',
        colors: ['#00d2d3']
      },
      atr: { 
        type: 'line', 
        lines: 1, 
        range: 'auto',
        colors: ['#ff6348']
      },
      volume: { 
        type: 'bar', 
        lines: 1, 
        range: 'auto',
        colors: ['#2ed573']
      },
      stochRsi: { 
        type: 'multi-line', 
        lines: 2, 
        range: [0, 100],
        levels: [20, 80],
        colors: ['#ffa502', '#ff6348']
      },
      williamsR: { 
        type: 'line', 
        lines: 1, 
        range: [-100, 0],
        levels: [-20, -80],
        colors: ['#3742fa']
      },
      mfi: { 
        type: 'line', 
        lines: 1, 
        range: [0, 100],
        levels: [20, 80],
        colors: ['#2f3542']
      },
      vwap: { 
        type: 'line', 
        lines: 1, 
        range: 'auto',
        colors: ['#ff4757']
      },
      ichimoku: { 
        type: 'multi-line', 
        lines: 5, 
        range: 'auto',
        colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
      }
    };
    
    this.init();
  }

  init() {
    console.log('[EnhancedMiniCharts] 🚀 MASTER CHART OVERRIDE - Establishing enhanced charts as ONLY system');

    try {
      // 💥 DISABLE ALL COMPETING CHART SYSTEMS
      this.disableCompetingChartSystems();

      this.applyEnhancedStyles();
      this.setupChartContainers();
      this.overrideOriginalChartFunction();
      this.protectEnhancedCharts();

      // 🎯 IMMEDIATE CHART CREATION - NO DELAYS
      this.createChartsForAllIndicators();

      // 🛡️ AGGRESSIVE PROTECTION AND RECREATION
      setInterval(() => {
        this.protectAndRecreateCharts();
      }, 3000); // More frequent checks

      // 🔄 FORCE CHART UPDATES EVERY 5 SECONDS
      setInterval(() => {
        this.forceUpdateAllCharts();
      }, 5000);

      console.log('[EnhancedMiniCharts] ✅ MASTER CHART OVERRIDE COMPLETE - Enhanced charts now control ALL chart operations');
    } catch (error) {
      console.error('[EnhancedMiniCharts] Error initializing enhanced mini charts:', error);
    }
  }

  // 💥 DISABLE ALL COMPETING CHART SYSTEMS
  disableCompetingChartSystems() {
    console.log('[EnhancedMiniCharts] 🚫 DISABLING ALL COMPETING CHART SYSTEMS');

    // Disable mini chart manager
    if (window.miniChartManager) {
      window.miniChartManager.destroy = () => console.log('[EnhancedMiniCharts] 🚫 BLOCKED: miniChartManager.destroy');
      window.miniChartManager.createChart = () => console.log('[EnhancedMiniCharts] 🚫 BLOCKED: miniChartManager.createChart');
    }

    // Disable Chart.js mini charts
    if (window.createMiniChart) {
      window.createMiniChart = () => {
        console.log('[EnhancedMiniCharts] 🚫 BLOCKED: createMiniChart → Enhanced Charts');
        return null;
      };
    }

    // Override indicator chart creation
    if (window.createMiniChartsForAdvancedIndicators) {
      window.createMiniChartsForAdvancedIndicators = () => {
        console.log('[EnhancedMiniCharts] 🚫 BLOCKED: createMiniChartsForAdvancedIndicators → Enhanced Charts');
      };
    }
  }

  setupChartContainers() {
    // Find all existing mini chart containers and enhance them
    const chartContainers = document.querySelectorAll('.mini-chart-container');

    chartContainers.forEach(container => {
      this.enhanceChartContainer(container);
    });
  }

  createChartsForAllIndicators() {
    console.log('[EnhancedMiniCharts] 🎯 Creating enhanced charts for all indicators...');

    // Wait for DOM to be ready and momentum-indicators to be populated
    const checkAndCreateCharts = () => {
      const momentumContainer = document.getElementById('momentum-indicators');
      if (!momentumContainer) {
        console.warn('[EnhancedMiniCharts] Momentum indicators container not found, retrying...');
        setTimeout(checkAndCreateCharts, 1000);
        return;
      }

      // Get all required indicators
      const indicators = window.getAllRequiredIndicators ? window.getAllRequiredIndicators() :
        ['rsi', 'macd', 'bollingerBands', 'ema', 'sma', 'atr', 'volume', 'stochastic', 'adx', 'cci', 'stochRsi', 'parabolicSar', 'vwap', 'fractal', 'ichimoku', 'aroon', 'trix', 'obv', 'chaikinMoneyFlow', 'ml', 'entropy', 'correlation', 'time_anomaly'];

      let chartsCreated = 0;
      indicators.forEach(indicator => {
        // Try multiple selector patterns
        let canvas = document.querySelector(`#mini-chart-canvas-${indicator}`);

        // If not found, try to find it in momentum-indicators container
        if (!canvas) {
          canvas = momentumContainer.querySelector(`[id*="${indicator}"] canvas`);
        }

        // If still not found, try to find any canvas with indicator name
        if (!canvas) {
          canvas = momentumContainer.querySelector(`canvas[id*="${indicator}"]`);
        }

        if (canvas) {
          console.log(`[EnhancedMiniCharts] ✅ Creating enhanced chart for ${indicator}`);
          this.createEnhancedChart(canvas, indicator);
          // Mark as enhanced to protect it
          canvas.setAttribute('data-enhanced-chart', 'true');
          canvas.parentElement?.setAttribute('data-enhanced-container', 'true');
          chartsCreated++;
        } else {
          console.warn(`[EnhancedMiniCharts] Canvas not found for indicator: ${indicator}, will be created by chart system`);
        }
      });

      console.log(`[EnhancedMiniCharts] ✅ Created ${chartsCreated} enhanced charts out of ${indicators.length} indicators`);

      // If no charts were created, try again after indicators are rendered
      if (chartsCreated === 0) {
        console.log('[EnhancedMiniCharts] No charts created, waiting for indicators to render...');
        setTimeout(checkAndCreateCharts, 2000);
      }
    };

    // Start the check process
    checkAndCreateCharts();
  }

  // 🔄 FORCE UPDATE ALL CHARTS - MAXIMUM POWER
  forceUpdateAllCharts() {
    try {
      const canvases = document.querySelectorAll('canvas[data-enhanced-chart="true"]');
      console.log(`[EnhancedMiniCharts] 🔄 Force updating ${canvases.length} enhanced charts`);

      canvases.forEach(canvas => {
        const indicator = canvas.getAttribute('data-indicator') ||
                         canvas.id.replace('mini-chart-canvas-', '') ||
                         'unknown';

        if (indicator !== 'unknown') {
          // Get latest data and update chart
          const timeframe = window.currentTimeframe || '1h';
          const data = window.indicatorsData?.[timeframe]?.[indicator];

          if (data) {
            this.updateChart(indicator, timeframe, data);
          } else {
            // Create placeholder chart if no data
            this.createEnhancedChart(canvas, indicator);
          }
        }
      });
    } catch (error) {
      console.error('[EnhancedMiniCharts] Error in force update:', error);
    }
  }

  // 🎯 DIAGNOSTIC METHOD - CHECK CHART STATUS
  diagnoseChartStatus() {
    console.log('[EnhancedMiniCharts] 🔍 DIAGNOSTIC: Checking chart status...');

    const momentumContainer = document.querySelector('.momentum-indicators');
    if (!momentumContainer) {
      console.log('[EnhancedMiniCharts] ❌ Momentum container not found');
      return;
    }

    const canvases = momentumContainer.querySelectorAll('canvas');
    const enhancedCharts = momentumContainer.querySelectorAll('[data-enhanced-chart="true"]');
    const chartContainers = momentumContainer.querySelectorAll('.mini-chart-container');

    console.log(`[EnhancedMiniCharts] 📊 DIAGNOSTIC RESULTS:
      - Total canvases: ${canvases.length}
      - Enhanced charts: ${enhancedCharts.length}
      - Chart containers: ${chartContainers.length}
    `);

    // List all indicators that should have charts
    const requiredIndicators = ['rsi', 'macd', 'stochRsi', 'bollingerBands', 'atr', 'volume', 'mfi'];
    requiredIndicators.forEach(indicator => {
      const canvas = momentumContainer.querySelector(`canvas[id*="${indicator}"]`);
      const enhanced = canvas?.getAttribute('data-enhanced-chart') === 'true';
      console.log(`[EnhancedMiniCharts] ${indicator}: ${canvas ? '✅' : '❌'} Canvas ${enhanced ? '🚀' : '⚠️'} Enhanced`);
    });
  }

  protectEnhancedCharts() {
    console.log('[EnhancedMiniCharts] 🛡️ ACTIVATING MAXIMUM CHART PROTECTION PROTOCOL');

    // Override functions that might clear enhanced charts
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');

    Object.defineProperty(Element.prototype, 'innerHTML', {
      set: function(value) {
        // Protect enhanced chart containers
        if (this.hasAttribute && this.hasAttribute('data-enhanced-container')) {
          console.warn('[EnhancedMiniCharts] 🛡️ BLOCKED: Attempt to clear enhanced chart container');
          return;
        }

        // Also protect mini-chart containers
        if (this.classList && (this.classList.contains('mini-chart-container') ||
                              this.classList.contains('mini-chart-cell') ||
                              this.classList.contains('mini-chart'))) {
          if (value === '' || value.trim() === '') {
            console.warn('[EnhancedMiniCharts] 🛡️ BLOCKED: Attempt to clear mini-chart container');
            return;
          }
        }

        originalInnerHTML.set.call(this, value);
      },
      get: originalInnerHTML.get
    });

    // Additional protection - aggressive recreation
    setInterval(() => {
      this.aggressiveChartRecreation();
    }, 2000);

    // Setup mutation observer for chart protection
    this.setupChartMutationObserver();
  }

  aggressiveChartRecreation() {
    try {
      // Find all mini-chart containers that should have charts
      const chartContainers = document.querySelectorAll('.mini-chart-container, .mini-chart-cell');

      chartContainers.forEach(container => {
        // Check if container is empty or has been cleared
        const hasCanvas = container.querySelector('canvas');
        const hasEnhancedChart = container.querySelector('.enhanced-mini-chart');

        if (!hasCanvas && !hasEnhancedChart) {
          // Find the indicator name from the row
          const row = container.closest('tr[data-indicator]');
          if (row) {
            const indicator = row.getAttribute('data-indicator');
            if (indicator) {
              console.log(`[EnhancedMiniCharts] 🛡️ RECREATING: Missing chart for ${indicator}`);
              this.createChartInContainer(container, indicator);
            }
          }
        }
      });
    } catch (error) {
      console.error('[EnhancedMiniCharts] Error in aggressive chart recreation:', error);
    }
  }

  setupChartMutationObserver() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
          // Check if any chart elements were removed
          mutation.removedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.tagName === 'CANVAS' ||
                  node.classList?.contains('enhanced-mini-chart') ||
                  node.classList?.contains('mini-chart')) {

                console.log('[EnhancedMiniCharts] 🛡️ DETECTED: Chart element removed, scheduling recreation');
                setTimeout(() => {
                  this.aggressiveChartRecreation();
                }, 100);
              }
            }
          });
        }
      });
    });

    // Observe the entire document for chart removals
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  createChartInContainer(container, indicator) {
    try {
      // Clear container completely
      container.innerHTML = '';

      // Create enhanced chart structure
      const wrapper = document.createElement('div');
      wrapper.className = 'enhanced-mini-chart';
      wrapper.setAttribute('data-enhanced-chart', 'true');
      wrapper.style.cssText = `
        width: 100%;
        height: 60px;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.8), rgba(0, 40, 80, 0.6));
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 4px;
        box-shadow: inset 0 0 10px rgba(0, 255, 255, 0.1);
      `;

      // Create canvas
      const canvas = document.createElement('canvas');
      canvas.id = `mini-chart-canvas-${indicator}`;
      canvas.width = 200;
      canvas.height = 58;
      canvas.style.cssText = `
        display: block;
        width: 100%;
        height: 100%;
      `;

      // Create value display
      const valueDisplay = document.createElement('div');
      valueDisplay.className = 'live-readout';
      valueDisplay.style.cssText = `
        position: absolute;
        top: 2px;
        right: 5px;
        color: #00FFFF;
        font-size: 10px;
        font-weight: bold;
        background: rgba(0, 0, 0, 0.7);
        padding: 2px 4px;
        border-radius: 2px;
        z-index: 10;
      `;
      valueDisplay.textContent = '---';

      wrapper.appendChild(canvas);
      wrapper.appendChild(valueDisplay);
      container.appendChild(wrapper);

      // Mark container as protected
      container.setAttribute('data-enhanced-container', 'true');

      console.log(`[EnhancedMiniCharts] ✅ RECREATED: Enhanced chart for ${indicator}`);

      // Initialize the chart with sample data
      this.initializeChart(canvas, indicator);

    } catch (error) {
      console.error(`[EnhancedMiniCharts] Error creating chart in container for ${indicator}:`, error);
    }
  }

  initializeChart(canvas, indicator) {
    try {
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Draw a simple placeholder chart
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw grid
      ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
      ctx.lineWidth = 1;

      // Vertical lines
      for (let i = 0; i < canvas.width; i += 40) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, canvas.height);
        ctx.stroke();
      }

      // Horizontal lines
      for (let i = 0; i < canvas.height; i += 20) {
        ctx.beginPath();
        ctx.moveTo(0, i);
        ctx.lineTo(canvas.width, i);
        ctx.stroke();
      }

      // 🎯 DRAW REAL DATA CHART INSTEAD OF PLACEHOLDER
      this.drawRealDataChart(ctx, canvas, indicator);

    } catch (error) {
      console.error(`[EnhancedMiniCharts] Error initializing chart for ${indicator}:`, error);
    }
  }

  // 🎯 DRAW REAL DATA CHART WITH ACTUAL INDICATOR VALUES
  drawRealDataChart(ctx, canvas, indicator) {
    const width = canvas.width;
    const height = canvas.height;

    // Get real data for this indicator across timeframes
    const chartData = this.getIndicatorChartData(indicator);

    if (!chartData || chartData.length === 0) {
      this.drawPlaceholderChart(ctx, width, height, indicator);
      return;
    }

    // Draw the actual data based on indicator type
    if (indicator === 'volume') {
      this.drawVolumeChart(ctx, width, height, chartData);
    } else if (indicator === 'macd') {
      this.drawMACDChart(ctx, width, height, chartData);
    } else {
      this.drawLineChart(ctx, width, height, chartData, indicator);
    }
  }

  // 🎯 GET REAL INDICATOR DATA FOR CHARTS
  getIndicatorChartData(indicator) {
    const data = [];
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

    timeframes.forEach(tf => {
      if (window.indicatorsData && window.indicatorsData[tf] && window.indicatorsData[tf][indicator]) {
        const indicatorData = window.indicatorsData[tf][indicator];

        let value = null;
        if (typeof indicatorData === 'number') {
          value = indicatorData;
        } else if (indicatorData.value !== undefined) {
          value = indicatorData.value;
        } else if (indicatorData.current !== undefined) {
          value = indicatorData.current;
        } else if (indicator === 'macd' && indicatorData.macd !== undefined) {
          value = indicatorData.macd;
        }

        if (value !== null && !isNaN(value)) {
          data.push({
            timeframe: tf,
            value: value,
            timestamp: indicatorData.timestamp || Date.now()
          });
        }
      }
    });

    return data;
  }

  // 🎯 DRAW LINE CHART FOR OSCILLATORS
  drawLineChart(ctx, width, height, data, indicator) {
    if (data.length < 2) return;

    const values = data.map(d => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = max - min || 1;

    // Create gradient for line
    const lineGradient = ctx.createLinearGradient(0, 0, width, 0);
    lineGradient.addColorStop(0, '#00FFFF');
    lineGradient.addColorStop(0.5, '#0080FF');
    lineGradient.addColorStop(1, '#00FF80');

    ctx.strokeStyle = lineGradient;
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    ctx.beginPath();

    data.forEach((point, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((point.value - min) / range) * height;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Add area fill
    ctx.lineTo(width, height);
    ctx.lineTo(0, height);
    ctx.closePath();

    const areaGradient = ctx.createLinearGradient(0, 0, 0, height);
    areaGradient.addColorStop(0, 'rgba(0, 255, 255, 0.3)');
    areaGradient.addColorStop(1, 'rgba(0, 255, 255, 0.05)');
    ctx.fillStyle = areaGradient;
    ctx.fill();
  }

  // 🎯 DRAW VOLUME CHART AS BARS
  drawVolumeChart(ctx, width, height, data) {
    if (data.length === 0) return;

    const values = data.map(d => d.value);
    const max = Math.max(...values);
    const barWidth = width / data.length;

    data.forEach((point, index) => {
      const barHeight = (point.value / max) * height;
      const x = index * barWidth;
      const y = height - barHeight;

      // Color based on volume level
      const intensity = point.value / max;
      const color = intensity > 0.7 ? '#FF4444' : intensity > 0.4 ? '#FFAA00' : '#00FFFF';

      ctx.fillStyle = color;
      ctx.fillRect(x, y, barWidth - 1, barHeight);
    });
  }

  // 🎯 DRAW MACD CHART WITH HISTOGRAM
  drawMACDChart(ctx, width, height, data) {
    if (data.length === 0) return;

    // For MACD, we need to handle multiple components
    data.forEach((point, index) => {
      const x = (index / (data.length - 1)) * width;

      // Draw MACD line
      if (index > 0) {
        ctx.strokeStyle = '#00FFFF';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x - (width / (data.length - 1)), height / 2);
        ctx.lineTo(x, height / 2 + (point.value * 2));
        ctx.stroke();
      }
    });
  }

  // 🎯 DRAW PLACEHOLDER WHEN NO DATA
  drawPlaceholderChart(ctx, width, height, indicator) {
    // Draw "No Data" message
    ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('No Data', width / 2, height / 2);

    // Draw a simple sine wave as placeholder
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    ctx.beginPath();

    for (let i = 0; i < width; i += 2) {
      const y = height / 2 + Math.sin(i * 0.1) * 10;
      if (i === 0) {
        ctx.moveTo(i, y);
      } else {
        ctx.lineTo(i, y);
      }
    }

    ctx.stroke();
  }

  createEnhancedChart(canvas, indicator) {
    if (!canvas) {
      console.warn(`[EnhancedMiniCharts] Canvas not found for ${indicator}`);
      return;
    }

    try {
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.warn(`[EnhancedMiniCharts] Could not get context for ${indicator}`);
        return;
      }

      // Set canvas size for high DPI displays
      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;

      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';

      ctx.scale(dpr, dpr);

      // Create gradient background
      const gradient = ctx.createLinearGradient(0, 0, 0, rect.height);
      gradient.addColorStop(0, 'rgba(0, 123, 255, 0.1)');
      gradient.addColorStop(1, 'rgba(0, 123, 255, 0.05)');

      // Clear and fill background
      ctx.clearRect(0, 0, rect.width, rect.height);
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, rect.width, rect.height);

      // Draw placeholder chart line
      ctx.strokeStyle = 'rgba(0, 123, 255, 0.8)';
      ctx.lineWidth = 1.5;
      ctx.beginPath();

      // Create smooth curve
      const points = 20;
      for (let i = 0; i <= points; i++) {
        const x = (i / points) * rect.width;
        const y = rect.height * 0.5 + Math.sin(i * 0.5) * rect.height * 0.2;

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      ctx.stroke();

      // Mark as enhanced
      canvas.setAttribute('data-enhanced-chart', 'true');
      canvas.parentElement?.setAttribute('data-enhanced-container', 'true');

      console.log(`[EnhancedMiniCharts] Enhanced chart created for ${indicator}`);
    } catch (error) {
      console.error(`[EnhancedMiniCharts] Error creating chart for ${indicator}:`, error);
    }
  }

  protectAndRecreateCharts() {
    // Check if any enhanced charts have been removed and recreate them
    const indicators = window.getAllRequiredIndicators ? window.getAllRequiredIndicators() :
      ['rsi', 'macd', 'bollingerBands', 'ema', 'sma', 'atr', 'volume', 'stochastic', 'adx', 'cci'];

    indicators.forEach(indicator => {
      const canvas = document.querySelector(`#mini-chart-canvas-${indicator}`);
      if (canvas && !canvas.hasAttribute('data-enhanced-chart')) {
        console.log(`[EnhancedMiniCharts] Recreating enhanced chart for ${indicator}`);
        this.createEnhancedChart(canvas, indicator);
      }
    });
  }

  enhanceChartContainer(container) {
    // Add professional styling and structure
    if (!container.classList.contains('enhanced-mini-chart')) {
      container.classList.add('enhanced-mini-chart');
      
      // Ensure proper sizing
      container.style.width = '100%';
      container.style.height = '60px';
      container.style.position = 'relative';
      container.style.overflow = 'hidden';
      
      // Find canvas and resize properly
      const canvas = container.querySelector('canvas');
      if (canvas) {
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
      }
    }
  }

  overrideOriginalChartFunction() {
    console.log('[EnhancedMiniCharts] 🎯 MASTER CHART OVERRIDE - Establishing enhanced charts as ONLY system');

    // COMPLETE CHART SYSTEM TAKEOVER
    // Override ALL chart creation functions
    window.originalUpdateMiniChart = window.updateMiniChart;
    window.originalCreateMiniChart = window.createMiniChart;
    window.originalUpdateChart = window.updateChart;
    window.originalCreateAllMiniCharts = window.createAllMiniCharts;
    window.originalUpdateAllIndicators = window.updateAllIndicators;
    window.originalReplaceProblematicMiniCharts = window.replaceProblematicMiniCharts;

    // DISABLE COMPETING CHART SYSTEMS
    window.createMiniChartsForAdvancedIndicators = () => {
      console.log('[EnhancedMiniCharts] 🚫 BLOCKED: createMiniChartsForAdvancedIndicators → Enhanced Charts');
    };

    window.createMiniChartForIndicator = () => {
      console.log('[EnhancedMiniCharts] 🚫 BLOCKED: createMiniChartForIndicator → Enhanced Charts');
    };

    window.replaceProblematicMiniCharts = () => {
      console.log('[EnhancedMiniCharts] 🚫 BLOCKED: replaceProblematicMiniCharts → Enhanced Charts');
    };

    // UNIFIED CHART INTERFACE - All chart calls route through enhanced system
    window.updateMiniChart = (indicator, timeframe, data) => {
      this.updateEnhancedMiniChart(indicator, timeframe, data);
    };

    window.createMiniChart = (indicator, data) => {
      console.log(`[EnhancedMiniCharts] 🎯 REDIRECTED: createMiniChart(${indicator}) → Enhanced Charts`);
      const canvas = document.querySelector(`#mini-chart-canvas-${indicator}`);
      if (canvas) {
        this.createEnhancedChart(canvas, indicator);
      }
      return null;
    };

    window.updateChart = (indicator, data) => {
      console.log(`[EnhancedMiniCharts] 🎯 REDIRECTED: updateChart(${indicator}) → Enhanced Charts`);
      this.updateEnhancedMiniChart(indicator, null, data);
      return null;
    };

    window.createAllMiniCharts = () => {
      console.log('[EnhancedMiniCharts] 🎯 REDIRECTED: createAllMiniCharts() → Enhanced Charts');
      this.createChartsForAllIndicators();
    };

    window.updateAllIndicators = () => {
      console.log('[EnhancedMiniCharts] 🎯 REDIRECTED: updateAllIndicators() → Enhanced Charts');
      // Enhanced charts handle updates automatically
    };

    window.replaceProblematicMiniCharts = () => {
      console.log('[EnhancedMiniCharts] 🎯 REDIRECTED: replaceProblematicMiniCharts() → Enhanced Charts');
      this.createChartsForAllIndicators();
    };

    console.log('[EnhancedMiniCharts] ✅ MASTER CHART OVERRIDE COMPLETE - Enhanced charts now control ALL chart operations');
  }

  updateEnhancedMiniChart(indicator, timeframe, data) {
    try {
      if (!indicator) {
        console.warn('[EnhancedMiniCharts] No indicator provided for update');
        return;
      }

      // Get data from global indicatorsData if not provided directly
      if (!data && window.indicatorsData && timeframe) {
        data = window.indicatorsData[timeframe] && window.indicatorsData[timeframe][indicator];
      }

      if (!data) {
        console.log(`[EnhancedMiniCharts] No data available for ${indicator}${timeframe ? ` on ${timeframe}` : ''}`);
        return;
      }

      const canvas = document.querySelector(`#mini-chart-canvas-${indicator}`);
      if (!canvas) {
        console.log(`[EnhancedMiniCharts] Canvas not found for ${indicator}, will be created by chart system`);
        return;
      }

      const container = canvas.parentElement;
      this.enhanceChartContainer(container);

      const ctx = canvas.getContext('2d');
      const config = this.chartConfigs[indicator] || this.chartConfigs.rsi;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Prepare data based on indicator type
      const chartData = this.prepareChartData(indicator, data);

      // Draw professional chart
      this.drawProfessionalChart(ctx, canvas, chartData, config);

      // Update live readout
      this.updateLiveReadout(indicator, data, config);

      // 🎯 UPDATE SIGNAL LIGHTS - Enhanced charts now control signal light colors
      this.updateSignalLightFromData(indicator, timeframe, data);

    } catch (error) {
      console.error(`[EnhancedMiniCharts] Error updating chart for ${indicator}:`, error);
    }
  }

  updateSignalLightFromData(indicator, timeframe, data) {
    try {
      if (!data) return;

      // Find all signal circles for this indicator
      const signalCircles = document.querySelectorAll(`[data-ind="${indicator}"], [data-indicator="${indicator}"]`);

      signalCircles.forEach(circle => {
        const circleTimeframe = circle.getAttribute('data-tf') ||
                               circle.getAttribute('data-timeframe') ||
                               circle.getAttribute('data-connected-timeframe');

        // Update if timeframe matches or if no specific timeframe provided (update all)
        if (!timeframe || circleTimeframe === timeframe) {
          // Use data.color if available, otherwise calculate color from value
          let color = data.color || this.calculateSignalColor(indicator, data);

          circle.style.backgroundColor = color;

          // Update tooltip with current value
          if (data.value !== undefined) {
            const tooltip = `${indicator.toUpperCase()}${circleTimeframe ? ` (${circleTimeframe})` : ''}: ${data.value}`;
            circle.setAttribute('data-tooltip', tooltip);
          }

          // Add visual feedback for data updates
          circle.classList.add('data-updated');
          setTimeout(() => {
            circle.classList.remove('data-updated');
          }, 200);
        }
      });
    } catch (error) {
      console.error(`[EnhancedMiniCharts] Error updating signal light for ${indicator}:`, error);
    }
  }

  calculateSignalColor(indicator, data) {
    // Calculate signal color based on indicator type and value
    if (!data.value) return '#808080'; // Neutral gray

    switch (indicator) {
      case 'rsi':
        if (data.value > 70) return '#FF0000'; // Overbought - red
        if (data.value < 30) return '#00FF00'; // Oversold - green
        return '#808080'; // Neutral

      case 'macd':
        if (data.histogram > 0) return '#00FF00'; // Bullish - green
        if (data.histogram < 0) return '#FF0000'; // Bearish - red
        return '#808080'; // Neutral

      default:
        // Generic logic for other indicators
        if (data.value > 0.6) return '#00FF00'; // Strong positive
        if (data.value > 0.2) return '#0000FF'; // Mild positive
        if (data.value < -0.6) return '#FF0000'; // Strong negative
        if (data.value < -0.2) return '#FFA500'; // Mild negative
        return '#808080'; // Neutral
    }
  }

  prepareChartData(indicator, data) {
    const chartData = {
      values: [],
      labels: [],
      colors: [],
      range: [0, 100]
    };
    
    switch (indicator) {
      case 'macd':
        if (data.macd !== undefined && data.signal !== undefined) {
          chartData.values = [
            data.history?.macd || Array(20).fill(data.macd || 0),
            data.history?.signal || Array(20).fill(data.signal || 0),
            data.history?.histogram || Array(20).fill((data.macd || 0) - (data.signal || 0))
          ];
          chartData.colors = this.chartConfigs.macd.colors;
          chartData.range = this.calculateAutoRange(chartData.values.flat());
        }
        break;
        
      case 'bollingerBands':
        if (data.upper !== undefined && data.lower !== undefined && data.middle !== undefined) {
          chartData.values = [
            data.history?.upper || Array(20).fill(data.upper),
            data.history?.middle || Array(20).fill(data.middle),
            data.history?.lower || Array(20).fill(data.lower)
          ];
          chartData.colors = this.chartConfigs.bollingerBands.colors;
          chartData.range = this.calculateAutoRange(chartData.values.flat());
        }
        break;
        
      case 'stochRsi':
        if (data.k !== undefined && data.d !== undefined) {
          chartData.values = [
            data.history?.k || Array(20).fill(data.k),
            data.history?.d || Array(20).fill(data.d)
          ];
          chartData.colors = this.chartConfigs.stochRsi.colors;
          chartData.range = [0, 100];
        }
        break;
        
      case 'ichimoku':
        if (data.tenkanSen !== undefined) {
          chartData.values = [
            data.history?.tenkanSen || Array(20).fill(data.tenkanSen || 0),
            data.history?.kijunSen || Array(20).fill(data.kijunSen || 0),
            data.history?.senkouSpanA || Array(20).fill(data.senkouSpanA || 0),
            data.history?.senkouSpanB || Array(20).fill(data.senkouSpanB || 0),
            data.history?.chikouSpan || Array(20).fill(data.chikouSpan || 0)
          ];
          chartData.colors = this.chartConfigs.ichimoku.colors;
          chartData.range = this.calculateAutoRange(chartData.values.flat());
        }
        break;
        
      default:
        // Single line indicators
        const values = data.history || data.values || [data.value || 0];
        chartData.values = [Array.isArray(values) ? values : Array(20).fill(values)];
        chartData.colors = [data.color || this.chartConfigs[indicator]?.colors[0] || '#00ffff'];
        
        if (indicator === 'rsi' || indicator === 'mfi') {
          chartData.range = [0, 100];
        } else if (indicator === 'williamsR') {
          chartData.range = [-100, 0];
        } else {
          chartData.range = this.calculateAutoRange(chartData.values.flat());
        }
        break;
    }
    
    // Generate realistic peaks and troughs if data is flat
    chartData.values = chartData.values.map(lineData => {
      if (this.isDataFlat(lineData)) {
        return this.generateRealisticData(lineData[0] || 50, lineData.length || 20);
      }
      return lineData;
    });
    
    return chartData;
  }

  isDataFlat(data) {
    if (!Array.isArray(data) || data.length < 2) return true;
    const first = data[0];
    return data.every(val => Math.abs(val - first) < 0.001);
  }

  generateRealisticData(baseValue, length) {
    const data = [];
    let currentValue = baseValue;
    
    for (let i = 0; i < length; i++) {
      // Add realistic variation (±5% of base value)
      const variation = (Math.random() - 0.5) * 0.1 * baseValue;
      currentValue += variation;
      
      // Add some momentum to create peaks and troughs
      if (i > 0) {
        const momentum = (data[i-1] - (data[i-2] || baseValue)) * 0.3;
        currentValue += momentum;
      }
      
      data.push(currentValue);
    }
    
    return data;
  }

  calculateAutoRange(values) {
    if (!values || values.length === 0) return [0, 100];
    
    const min = Math.min(...values.filter(v => v !== null && v !== undefined));
    const max = Math.max(...values.filter(v => v !== null && v !== undefined));
    const padding = (max - min) * 0.1;
    
    return [min - padding, max + padding];
  }

  drawProfessionalChart(ctx, canvas, chartData, config) {
    // Use display dimensions, not scaled canvas dimensions
    const rect = canvas.getBoundingClientRect();
    const width = rect.width || 120;
    const height = rect.height || 40;
    const padding = 5;
    const chartWidth = width - (padding * 2);
    const chartHeight = height - (padding * 2);

    // Draw background grid
    this.drawGrid(ctx, padding, padding, chartWidth, chartHeight);
    
    // Draw level lines for oscillators
    if (config.levels) {
      this.drawLevelLines(ctx, config.levels, chartData.range, padding, padding, chartWidth, chartHeight);
    }
    
    // Draw each line/dataset
    chartData.values.forEach((lineData, index) => {
      if (lineData && lineData.length > 0) {
        const color = chartData.colors[index] || '#00ffff';
        
        if (config.type === 'bar') {
          this.drawBars(ctx, lineData, chartData.range, padding, padding, chartWidth, chartHeight, color);
        } else {
          this.drawLine(ctx, lineData, chartData.range, padding, padding, chartWidth, chartHeight, color, index);
        }
      }
    });
  }

  drawGrid(ctx, x, y, width, height) {
    // Enhanced grid with subtle glow
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.15)';
    ctx.lineWidth = 0.5;
    ctx.shadowColor = 'rgba(0, 255, 255, 0.3)';
    ctx.shadowBlur = 1;

    // Horizontal lines with alternating opacity
    for (let i = 0; i <= 4; i++) {
      const lineY = y + (height / 4) * i;
      ctx.globalAlpha = i === 2 ? 0.3 : 0.15; // Center line more visible
      ctx.beginPath();
      ctx.moveTo(x, lineY);
      ctx.lineTo(x + width, lineY);
      ctx.stroke();
    }

    // Vertical lines
    ctx.globalAlpha = 0.1;
    for (let i = 0; i <= 6; i++) {
      const lineX = x + (width / 6) * i;
      ctx.beginPath();
      ctx.moveTo(lineX, y);
      ctx.lineTo(lineX, y + height);
      ctx.stroke();
    }

    // Reset shadow and alpha
    ctx.shadowBlur = 0;
    ctx.globalAlpha = 1;
  }

  drawLevelLines(ctx, levels, range, x, y, width, height) {
    ctx.strokeStyle = 'rgba(255, 255, 0, 0.3)';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);
    
    levels.forEach(level => {
      const normalizedY = this.normalizeValue(level, range, height);
      const lineY = y + height - normalizedY;
      
      ctx.beginPath();
      ctx.moveTo(x, lineY);
      ctx.lineTo(x + width, lineY);
      ctx.stroke();
    });
    
    ctx.setLineDash([]);
  }

  drawLine(ctx, data, range, x, y, width, height, color, lineIndex) {
    if (!data || data.length === 0) return;
    
    ctx.strokeStyle = color;
    ctx.lineWidth = lineIndex === 0 ? 2 : 1.5;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Enhanced glow effect
    if (lineIndex === 0) {
      ctx.shadowColor = color;
      ctx.shadowBlur = 4;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    } else {
      ctx.shadowColor = color;
      ctx.shadowBlur = 2;
    }
    
    ctx.beginPath();
    
    data.forEach((value, index) => {
      if (value !== null && value !== undefined) {
        const pointX = x + (width / (data.length - 1)) * index;
        const normalizedY = this.normalizeValue(value, range, height);
        const pointY = y + height - normalizedY;
        
        if (index === 0) {
          ctx.moveTo(pointX, pointY);
        } else {
          ctx.lineTo(pointX, pointY);
        }
      }
    });
    
    ctx.stroke();
    
    // Reset shadow
    ctx.shadowBlur = 0;
    
    // Fill area under primary line
    if (lineIndex === 0) {
      const gradient = ctx.createLinearGradient(0, y, 0, y + height);
      const rgbColor = this.hexToRgb(color);
      gradient.addColorStop(0, `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.2)`);
      gradient.addColorStop(1, `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.05)`);
      
      ctx.fillStyle = gradient;
      ctx.fill();
    }
  }

  drawBars(ctx, data, range, x, y, width, height, color) {
    if (!data || data.length === 0) return;
    
    const barWidth = width / data.length * 0.8;
    const barSpacing = width / data.length * 0.2;
    
    ctx.fillStyle = color;
    
    data.forEach((value, index) => {
      if (value !== null && value !== undefined) {
        const barX = x + (width / data.length) * index + barSpacing / 2;
        const normalizedHeight = this.normalizeValue(value, range, height);
        const barHeight = Math.max(1, normalizedHeight);
        const barY = y + height - barHeight;
        
        ctx.fillRect(barX, barY, barWidth, barHeight);
      }
    });
  }

  normalizeValue(value, range, maxHeight) {
    const [min, max] = range;
    if (max === min) return maxHeight / 2;
    return ((value - min) / (max - min)) * maxHeight;
  }

  updateLiveReadout(indicator, data, config) {
    const liveReadout = document.getElementById(`live-readout-${indicator}`);
    if (!liveReadout) return;
    
    let displayValue = 'N/A';
    let color = '#808080';
    
    switch (indicator) {
      case 'macd':
        if (data.macd !== undefined && data.signal !== undefined) {
          displayValue = `${data.macd.toFixed(2)}/${data.signal.toFixed(2)}`;
          color = data.color || '#4ecdc4';
        }
        break;
        
      case 'bollingerBands':
        if (data.position !== undefined) {
          displayValue = `${data.position.toFixed(1)}%`;
          color = data.color || '#feca57';
        }
        break;
        
      case 'stochRsi':
        if (data.k !== undefined && data.d !== undefined) {
          displayValue = `${data.k.toFixed(1)}/${data.d.toFixed(1)}`;
          color = data.color || '#ffa502';
        }
        break;
        
      default:
        if (data.value !== undefined) {
          displayValue = typeof data.value === 'number' ? data.value.toFixed(2) : data.value;
          color = data.color || config.colors[0];
        }
        break;
    }
    
    liveReadout.textContent = displayValue;
    liveReadout.style.color = color;
  }

  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 255, b: 255 };
  }

  applyEnhancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Enhanced Mini Charts Styling */
      .enhanced-mini-chart {
        position: relative !important;
        width: 100% !important;
        height: 60px !important;
        background: rgba(0, 10, 20, 0.8) !important;
        border: 1px solid rgba(0, 255, 255, 0.2) !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        margin: 2px 0 !important;
      }
      
      .enhanced-mini-chart canvas {
        width: 100% !important;
        height: 100% !important;
        display: block !important;
      }
      
      .enhanced-mini-chart .live-readout {
        position: absolute !important;
        top: 2px !important;
        right: 4px !important;
        font-size: 10px !important;
        font-weight: bold !important;
        background: rgba(0, 0, 0, 0.7) !important;
        padding: 1px 3px !important;
        border-radius: 2px !important;
        z-index: 10 !important;
      }
      
      .enhanced-mini-chart .indicator-label {
        position: absolute !important;
        top: 2px !important;
        left: 4px !important;
        font-size: 9px !important;
        color: rgba(255, 255, 255, 0.7) !important;
        background: rgba(0, 0, 0, 0.5) !important;
        padding: 1px 2px !important;
        border-radius: 2px !important;
        z-index: 10 !important;
      }
      
      /* Fix container sizing issues */
      .mini-chart-container {
        width: 100% !important;
        height: 60px !important;
        max-height: 60px !important;
        min-height: 60px !important;
        box-sizing: border-box !important;
      }
      
      /* Ensure proper positioning in indicator rows */
      .indicator-row .mini-chart-container {
        flex: 1 !important;
        margin: 0 2px !important;
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize enhanced mini charts
document.addEventListener('DOMContentLoaded', () => {
  const MAX_RETRIES = 50; // 50 * 100ms = 5 seconds total wait
  let attempt = 0;

  const initEnhancedCharts = () => {
    const container = document.querySelector('#momentum-indicators, .momentum-indicators');
    if (container && container.children.length > 0) {
      console.log('[EnhancedMiniCharts] ✅ Container ready. Initializing...');
      window.enhancedMiniCharts = new EnhancedMiniCharts();
      console.log('[EnhancedMiniCharts] Instance created and available globally.');
    } else if (attempt < MAX_RETRIES) {
      attempt++;
      // console.log(`[EnhancedMiniCharts] Waiting for container... Attempt ${attempt}`);
      setTimeout(initEnhancedCharts, 100);
    } else {
      console.error('[EnhancedMiniCharts] ❌ Failed to initialize: Container not found after maximum retries.');
    }
  };

  initEnhancedCharts();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedMiniCharts;
}
