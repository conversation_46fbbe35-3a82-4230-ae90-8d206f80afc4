module.exports = {
  // Kraken API Configuration
  KRAKEN: {
    BASE_URL: 'https://api.kraken.com/0/public',
    WS_URL: 'wss://ws.kraken.com',
    // Rate limiting (3 requests per second, 15 per 10-second window for public API)
    RATE_LIMIT: {
      REQUESTS_PER_INTERVAL: 3,
      INTERVAL_MS: 1000,
      MAX_RETRIES: 3,
      RETRY_DELAY: 1000,
    },
    // WebSocket configuration
    WS: {
      PING_INTERVAL: 30000,    // 30 seconds
      PONG_TIMEOUT: 10000,     // 10 seconds
      RECONNECT_DELAY: 5000,   // 5 seconds
      MAX_RECONNECT_ATTEMPTS: 5,
    },
    // Data fetching
    FETCH: {
      COOLDOWN: 12000,         // 12 seconds between fetches for same pair/timeframe
      MAX_CANDLES: 720,        // Maximum candles to fetch per request
    },
  },
  
  // Timeframes and their corresponding Kraken intervals (in minutes)
  TIMEFRAMES: {
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '4h': 240,
    '1d': 1440,
    '1w': 10080,
  },
  
  // Cryptocurrency pairs and their Kraken equivalents
  PAIRS: {
    'BTC/USD': 'XBTUSD',
    'ETH/USD': 'ETHUSD',
    'XRP/USD': 'XRPUSD',
    'LTC/USD': 'LTCUSD',
    'BCH/USD': 'BCHUSD',
    'LINK/USD': 'LINKUSD',
    'DOT/USD': 'DOTUSD',
    'ADA/USD': 'ADAUSD',
    'XLM/USD': 'XXLMZUSD',
    'EOS/USD': 'EOSUSD',
    'XMR/USD': 'XXMRZUSD',
    'ZEC/USD': 'XZECZUSD',
    'ETC/USD': 'XETCZUSD',
    'TRX/USD': 'TRXUSD',
    'XRP/BTC': 'XRPXBT',
    'ETH/BTC': 'XBTETH',
    'LTC/BTC': 'XLTCXXBT',
  },
  
  // Default values
  DEFAULTS: {
    PAIR: 'BTC/USD',
    TIMEFRAME: '1h',
    STRATEGY: 'neural_network_navigator',
  },
  
  // Indicator settings
  INDICATORS: {
    RSI_PERIOD: 14,
    BB_PERIOD: 20,
    BB_STDDEV: 2,
    MACD_FAST: 12,
    MACD_SLOW: 26,
    MACD_SIGNAL: 9,
    ATR_PERIOD: 14,
    ADX_PERIOD: 14,
    STOCH_RSI_PERIOD: 14,
    WILLIAMS_R_PERIOD: 14,
    MFI_PERIOD: 14,
  },
};
