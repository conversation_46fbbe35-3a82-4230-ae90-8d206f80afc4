/**
 * StarCrypt Professional WebSocket Manager
 * Enterprise-grade WebSocket connection management
 * Ensures reliable real-time data flow for trading operations
 */

class ProfessionalWebSocketManager {
  constructor() {
    this.ws = null;
    this.connectionState = 'DISCONNECTED';
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 1000;
    this.maxReconnectDelay = 30000;
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;
    this.messageQueue = [];
    this.connectionMetrics = {
      connectTime: null,
      lastMessageTime: null,
      messagesReceived: 0,
      messagesSent: 0,
      reconnectCount: 0,
      totalDowntime: 0
    };
    
    this.eventHandlers = new Map();
    this.dataValidation = true;
    
    this.init();
  }

  init() {
    console.log('🔗 WEBSOCKET MANAGER: Initializing professional WebSocket management...');
    
    this.setupEventHandlers();
    this.connect();
    
    console.log('✅ WEBSOCKET MANAGER: Professional connection management active');
  }

  setupEventHandlers() {
    // Register default event handlers
    this.on('indicators', (data) => {
      if (this.validateIncomingData(data, 'indicators')) {
        this.updateIndicatorsData(data);
      }
    });

    this.on('livePrice', (data) => {
      if (this.validateIncomingData(data, 'price')) {
        this.updateCurrentPrice(data);
      }
    });

    this.on('ticker', (data) => {
      if (this.validateIncomingData(data, 'ticker')) {
        this.updateTickerData(data);
      }
    });
  }

  connect() {
    if (this.connectionState === 'CONNECTING' || this.connectionState === 'CONNECTED') {
      console.log('🔗 WEBSOCKET: Connection already in progress or established');
      return;
    }

    this.connectionState = 'CONNECTING';
    // 🎖️ FLEET COMMANDER: Force connection to enhanced frontend handler on port 8080
    const wsUrl = window.KRAKEN_WS_URL || 'ws://localhost:8080';
    
    console.log(`🔗 WEBSOCKET: Connecting to ${wsUrl}...`);

    try {
      this.ws = new WebSocket(wsUrl);
      this.setupWebSocketEventHandlers();
    } catch (error) {
      console.error('🔗 WEBSOCKET: Connection failed:', error);
      this.handleConnectionError(error);
    }
  }

  setupWebSocketEventHandlers() {
    this.ws.onopen = (event) => {
      console.log('🔗 WEBSOCKET: Connection established');
      this.connectionState = 'CONNECTED';
      this.connectionMetrics.connectTime = Date.now();
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      
      this.startHeartbeat();
      this.processMessageQueue();
      this.emit('connected', event);
    };

    this.ws.onmessage = (event) => {
      this.connectionMetrics.messagesReceived++;
      this.connectionMetrics.lastMessageTime = Date.now();
      
      try {
        const data = JSON.parse(event.data);
        this.handleIncomingMessage(data);
      } catch (error) {
        console.error('🔗 WEBSOCKET: Failed to parse message:', error);
        
        if (window.professionalErrorHandler) {
          window.professionalErrorHandler.handleError({
            type: 'WEBSOCKET_PARSE_ERROR',
            message: 'Failed to parse WebSocket message',
            rawData: event.data,
            error: error
          });
        }
      }
    };

    this.ws.onclose = (event) => {
      console.log('🔗 WEBSOCKET: Connection closed:', event.code, event.reason);
      this.connectionState = 'DISCONNECTED';
      this.stopHeartbeat();
      
      if (!event.wasClean) {
        this.handleConnectionError(new Error(`Connection closed unexpectedly: ${event.reason}`));
      }
      
      this.emit('disconnected', event);
    };

    this.ws.onerror = (error) => {
      console.error('🔗 WEBSOCKET: Connection error:', error);
      this.handleConnectionError(error);
    };
  }

  handleIncomingMessage(data) {
    // Validate message structure
    if (!data || typeof data !== 'object') {
      console.warn('🔗 WEBSOCKET: Invalid message structure received');
      return;
    }

    // Validate with data integrity system
    if (window.dataIntegrityValidator && this.dataValidation) {
      const validation = window.dataIntegrityValidator.validateData(data, 'websocket', 'kraken_websocket');
      if (!validation.valid) {
        console.error('🔗 WEBSOCKET: Message failed validation:', validation.reason);
        return;
      }
    }

    // Route message to appropriate handler
    if (data.type && this.eventHandlers.has(data.type)) {
      const handler = this.eventHandlers.get(data.type);
      try {
        handler(data);
      } catch (error) {
        console.error(`🔗 WEBSOCKET: Handler error for ${data.type}:`, error);
        
        if (window.professionalErrorHandler) {
          window.professionalErrorHandler.handleError({
            type: 'WEBSOCKET_HANDLER_ERROR',
            message: `WebSocket handler error for ${data.type}`,
            error: error,
            messageType: data.type
          });
        }
      }
    } else {
      console.warn(`🔗 WEBSOCKET: No handler for message type: ${data.type}`);
    }
  }

  validateIncomingData(data, expectedType) {
    if (!data || typeof data !== 'object') {
      return false;
    }

    switch (expectedType) {
      case 'indicators':
        return data.data && typeof data.data === 'object' && data.timeframe;
      case 'price':
        return data.data && typeof data.data.price === 'number';
      case 'ticker':
        return data.data && Array.isArray(data.data);
      default:
        return true;
    }
  }

  updateIndicatorsData(data) {
    if (!window.indicatorsData) {
      window.indicatorsData = {};
    }

    if (data.timeframe && data.data) {
      window.indicatorsData[data.timeframe] = data.data;
      
      // Trigger signal lights update
      if (typeof window.updateAllSignalLights === 'function') {
        window.updateAllSignalLights();
      }
    }
  }

  updateCurrentPrice(data) {
    if (data.data && typeof data.data.price === 'number') {
      window.currentPrice = data.data.price;
      
      // Update price display
      if (typeof window.updatePriceDisplay === 'function') {
        window.updatePriceDisplay(data.data.price);
      }
    }
  }

  updateTickerData(data) {
    if (data.data && Array.isArray(data.data) && data.data.length > 0) {
      const tickerData = data.data[0];
      if (tickerData.c && Array.isArray(tickerData.c) && tickerData.c.length > 0) {
        const price = parseFloat(tickerData.c[0]);
        if (!isNaN(price)) {
          window.currentPrice = price;
          
          if (typeof window.updatePriceDisplay === 'function') {
            window.updatePriceDisplay(price);
          }
        }
      }
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.connectionState === 'CONNECTED') {
        this.send({ type: 'ping', timestamp: Date.now() });
        
        // Set timeout for pong response
        this.heartbeatTimeout = setTimeout(() => {
          console.warn('🔗 WEBSOCKET: Heartbeat timeout - connection may be stale');
          this.reconnect();
        }, 10000);
      }
    }, 30000);
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  handleConnectionError(error) {
    console.error('🔗 WEBSOCKET: Handling connection error:', error);
    
    if (window.professionalErrorHandler) {
      window.professionalErrorHandler.handleError({
        type: 'WEBSOCKET_CONNECTION_ERROR',
        message: error.message || 'WebSocket connection error',
        error: error,
        reconnectAttempts: this.reconnectAttempts
      });
    }

    this.scheduleReconnect();
  }

  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🔗 WEBSOCKET: Maximum reconnection attempts reached');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.connectionMetrics.reconnectCount++;
    
    console.log(`🔗 WEBSOCKET: Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);
    
    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay);

    // Exponential backoff with jitter
    this.reconnectDelay = Math.min(
      this.reconnectDelay * 2 + Math.random() * 1000,
      this.maxReconnectDelay
    );
  }

  reconnect() {
    console.log('🔗 WEBSOCKET: Manual reconnection triggered');
    this.disconnect();
    this.connect();
  }

  disconnect() {
    if (this.ws) {
      this.connectionState = 'DISCONNECTING';
      this.stopHeartbeat();
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  send(data) {
    if (this.connectionState !== 'CONNECTED') {
      console.warn('🔗 WEBSOCKET: Queueing message - not connected');
      this.messageQueue.push(data);
      return false;
    }

    try {
      this.ws.send(JSON.stringify(data));
      this.connectionMetrics.messagesSent++;
      return true;
    } catch (error) {
      console.error('🔗 WEBSOCKET: Failed to send message:', error);
      this.messageQueue.push(data);
      return false;
    }
  }

  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.connectionState === 'CONNECTED') {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  // Event system
  on(event, handler) {
    this.eventHandlers.set(event, handler);
  }

  off(event) {
    this.eventHandlers.delete(event);
  }

  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event)(data);
    }
  }

  // Public API
  getConnectionState() {
    return this.connectionState;
  }

  getConnectionMetrics() {
    return {
      ...this.connectionMetrics,
      currentState: this.connectionState,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length
    };
  }

  isConnected() {
    return this.connectionState === 'CONNECTED';
  }

  enableDataValidation() {
    this.dataValidation = true;
    console.log('🔗 WEBSOCKET: Data validation enabled');
  }

  disableDataValidation() {
    this.dataValidation = false;
    console.log('🔗 WEBSOCKET: Data validation disabled');
  }
}

// Initialize professional WebSocket manager
window.professionalWebSocketManager = new ProfessionalWebSocketManager();

// Export for global access
window.ProfessionalWebSocketManager = ProfessionalWebSocketManager;

// Backward compatibility
window.wsManager = window.professionalWebSocketManager;

console.log('🔗 WEBSOCKET MANAGER: Professional WebSocket management active');
