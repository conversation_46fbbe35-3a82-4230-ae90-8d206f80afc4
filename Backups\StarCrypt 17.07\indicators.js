const { TIMEFRAMES } = require('./config')

// Simple Moving Average
function calculateSMA(data, period) {
  if (data.length < period) return null

  const sum = data
    .slice(-period)
    .reduce((acc, val) => acc + val.close, 0)

  return sum / period
}

// Exponential Moving Average
function calculateEMA(data, period) {
  if (data.length < period) return null

  const k = 2 / (period + 1)
  let ema = data[0].close

  for (let i = 1; i < data.length; i++) {
    ema = (data[i].close - ema) * k + ema
  }

  return ema
}

// Relative Strength Index
function calculateRSI(data, period = 14) {
  if (data.length < period + 1) return null

  let gains = 0
  let losses = 0

  // Calculate initial average gains and losses
  for (let i = 1; i <= period; i++) {
    const diff = data[i].close - data[i - 1].close
    if (diff >= 0) {
      gains += diff
    } else {
      losses -= diff
    }
  }

  let avgGain = gains / period
  let avgLoss = losses / period || 1 // Avoid division by zero

  // Calculate subsequent values
  for (let i = period + 1; i < data.length; i++) {
    const diff = data[i].close - data[i - 1].close

    if (diff >= 0) {
      avgGain = (avgGain * (period - 1) + diff) / period
      avgLoss = (avgLoss * (period - 1)) / period
    } else {
      avgGain = (avgGain * (period - 1)) / period
      avgLoss = (avgLoss * (period - 1) - diff) / period
    }
  }

  const rs = avgGain / (avgLoss || 0.0001) // Avoid division by zero
  return 100 - (100 / (1 + rs))
}

// Moving Average Convergence Divergence
function calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
  if (data.length < slowPeriod + signalPeriod) return null

  const emaFast = calculateEMA(data, fastPeriod)
  const emaSlow = calculateEMA(data, slowPeriod)

  if (emaFast === null || emaSlow === null) return null

  const macdLine = emaFast - emaSlow

  // For signal line, we'd need more data points
  // This is a simplified version
  const signalLine = calculateEMA(
    data.slice(-(slowPeriod + signalPeriod)).map((d, i) => ({
      ...d,
      close: macdLine, // Use MACD line as close price for signal calculation
    })),
    signalPeriod,
  )

  const histogram = macdLine - signalLine

  return {
    macd: macdLine,
    signal: signalLine,
    histogram,
  }
}

// Calculate all indicators for a given pair and timeframe
function calculateAllIndicators(pair, timeframe, data) {
  if (!data || data.length === 0) {
    console.warn(`No data available for ${pair}/${timeframe}`)
    return {}
  }

  const closes = data.map(d => d.close)
  const highs = data.map(d => d.high)
  const lows = data.map(d => d.low)
  const volumes = data.map(d => d.volume)

  // Calculate indicators with different periods
  const indicators = {
    // Moving Averages
    sma20: calculateSMA(data, 20),
    sma50: calculateSMA(data, 50),
    sma200: calculateSMA(data, 200),
    ema9: calculateEMA(data, 9),
    ema21: calculateEMA(data, 21),
    ema50: calculateEMA(data, 50),
    ema200: calculateEMA(data, 200),

    // Oscillators
    rsi: calculateRSI(data, 14),
    macd: calculateMACD(data),

    // Volume
    volume: volumes[volumes.length - 1],
    volumeSMA20: calculateSMA(
      volumes.map((v, i) => ({ close: v })),
      20,
    ),

    // Price data
    currentPrice: data[data.length - 1].close,
    open: data[0].open,
    high: Math.max(...highs),
    low: Math.min(...lows),
    close: data[data.length - 1].close,
    timestamp: data[data.length - 1].time,
  }

  return indicators
}

// Update indicators for all timeframes of a pair
function updateAllIndicators(pair, historicalData, indicatorsData) {
  if (!historicalData[pair]) return

  TIMEFRAMES.forEach(timeframe => {
    const data = historicalData[pair][timeframe]
    if (data && data.length > 0) {
      indicatorsData[pair][timeframe] = calculateAllIndicators(pair, timeframe, data)
    }
  })
}

module.exports = {
  calculateSMA,
  calculateEMA,
  calculateRSI,
  calculateMACD,
  calculateAllIndicators,
  updateAllIndicators,
}
