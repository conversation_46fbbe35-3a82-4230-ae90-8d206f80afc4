/* Loading Overlay Styles */
#loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 14, 23, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 5px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1.5rem;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 5px solid transparent;
  border-radius: 50%;
  border-top-color: #00B4D8;
  animation: spin 1.5s ease-in-out infinite reverse;
  filter: blur(1px);
}

.loading-text {
  color: #00FFFF;
  font-size: 1.5rem;
  font-weight: 500;
  margin-top: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-align: center;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation: pulse 2s infinite;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: rgba(0, 180, 216, 0.2);
  border-radius: 2px;
  margin-top: 2rem;
  overflow: hidden;
  position: relative;
}

.loading-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, #00B4D8, #00FFFF);
  transition: width 0.3s ease;
  animation: progressPulse 2s infinite;
}

.loading-details {
  color: #A0A0A0;
  font-size: 0.9rem;
  margin-top: 1rem;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

.loading-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  color: #00FFFF;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.stat-label {
  color: #A0A0A0;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes progressPulse {
  0% { box-shadow: 0 0 5px rgba(0, 180, 216, 0.5); }
  50% { box-shadow: 0 0 20px rgba(0, 180, 216, 0.8); }
  100% { box-shadow: 0 0 5px rgba(0, 180, 216, 0.5); }
}

/* Loading Complete State */
#loading-overlay.fade-out {
  opacity: 0;
  visibility: hidden;
}

/* Loading Error State */
.loading-error {
  color: #FF6B6B;
  text-align: center;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 4px;
  max-width: 80%;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .loading-text {
    font-size: 1.2rem;
  }
  
  .loading-details {
    font-size: 0.8rem;
  }
  
  .loading-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
}
