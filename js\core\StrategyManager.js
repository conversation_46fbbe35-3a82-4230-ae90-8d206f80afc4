/**
 * StrategyManager - Centralized strategy management for StarCrypt
 * Handles all strategy-related operations and state management
 */

class StrategyManager {
  constructor() {
    // Singleton pattern
    if (StrategyManager.instance) {
      return StrategyManager.instance;
    }
    StrategyManager.instance = this;

    // State
    this.currentStrategy = null;
    this.previousStrategy = null;
    this.strategies = window.TRADING_STRATEGIES || {};
    this.isInitialized = false;
    
    // Track notification state to prevent duplicates
    this.lastNotificationTime = 0;
    this.NOTIFICATION_THROTTLE_MS = 2000; // 2 seconds between notifications
    
    // Bind methods
    this.init = this.init.bind(this);
    this.handleStrategyChange = this.handleStrategyChange.bind(this);
    this.updateUI = this.updateUI.bind(this);
    this.notifyStrategyChange = this.notifyStrategyChange.bind(this);
    
    // Initialize
    this.init();
  }

  /**
   * Initialize the strategy manager
   */
  init() {
    if (this.isInitialized) return;
    
    // Load saved strategy or use default
    this.currentStrategy = localStorage.getItem('currentStrategy') || 'admiral_toa';
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Apply initial strategy
    this.applyStrategy(this.currentStrategy, { source: 'init' });
    
    this.isInitialized = true;
    console.log('[StrategyManager] Initialized');
  }

  /**
   * Set up event listeners
   * @private
   */
  setupEventListeners() {
    // Remove any existing listeners to prevent duplicates
    this.removeEventListeners();
    
    // Strategy selector changes
    document.addEventListener('change', (e) => {
      if (e.target.matches('#strategySelector, .strategy-selector')) {
        this.handleStrategyChange({
          target: { value: e.target.value },
          preventDefault: () => e.preventDefault(),
          stopPropagation: () => e.stopPropagation()
        });
      }
    });
    
    // Strategy change events
    document.addEventListener('strategyChanged', (e) => {
      if (e.detail?.fromStrategyManager) return; // Prevent loops
      this.handleStrategyChange(e);
    });
  }

  /**
   * Remove event listeners
   * @private
   */
  removeEventListeners() {
    // Clone and replace elements to remove all event listeners
    const selectors = document.querySelectorAll('#strategySelector, .strategy-selector');
    selectors.forEach(select => {
      const clone = select.cloneNode(true);
      select.parentNode.replaceChild(clone, select);
    });
    
    // Remove global listeners
    document.removeEventListener('strategyChanged', this.handleStrategyChange);
  }

  /**
   * Handle strategy change events - COMPLETELY DISABLED
   */
  handleStrategyChange(event) {
    console.log('[StrategyManager] 🚫 COMPLETELY DISABLED - Unified Signal Commander has full control');
    return;
  }

  initialize() {
    console.log('[StrategyManager] 🚫 COMPLETELY DISABLED - Unified Signal Commander has full control');
    return;
  }

  applyStrategy() {
    console.log('[StrategyManager] 🚫 COMPLETELY DISABLED - Unified Signal Commander has full control');
    return;
  }

  /**
   * Apply a strategy
   * @param {string} strategyId - The strategy ID to apply
   * @param {Object} options - Additional options
   * @returns {boolean} Whether the strategy was applied
   */
  applyStrategy(strategyId, options = {}) {
    try {
      if (!strategyId || !this.strategies[strategyId]) {
        console.warn(`[StrategyManager] Invalid strategy: ${strategyId}`);
        return false;
      }

      // Don't re-apply the same strategy unless forced
      if (strategyId === this.currentStrategy && !options.force) {
        // Only log if we're not in the middle of initialization
        if (options.source !== 'init') {
          console.log(`[StrategyManager] Strategy ${strategyId} is already active`);
        }
        return false;
      }

      console.log(`[StrategyManager] Applying strategy: ${strategyId}`, options);
      
      // Save the previous strategy
      this.previousStrategy = this.currentStrategy;
      this.currentStrategy = strategyId;
      
      // Update storage
      localStorage.setItem('currentStrategy', strategyId);
      
      // Update UI
      this.updateUI();
      
      // Update indicators if the function exists
      if (typeof window.updateIndicatorsForStrategy === 'function') {
        window.updateIndicatorsForStrategy(strategyId);
      }
      
      // Notify other components
      this.notifyStrategyChange(strategyId, this.previousStrategy, options.source);
      
      return true;
    } catch (error) {
      console.error('[StrategyManager] Error applying strategy:', error);
      return false;
    }
  }

  /**
   * Update UI elements for the current strategy
   */
  updateUI() {
    try {
      const strategy = this.strategies[this.currentStrategy];
      if (!strategy) return;
      
      // Update strategy selectors
      const selectors = document.querySelectorAll('#strategySelector, .strategy-selector');
      selectors.forEach(select => {
        if (select.value !== this.currentStrategy) {
          select.value = this.currentStrategy;
        }
      });
      
      // Update strategy name display
      const nameElements = document.querySelectorAll('.strategy-name, [data-strategy-name]');
      nameElements.forEach(el => {
        el.textContent = strategy.name || this.currentStrategy;
      });
      
      // Update strategy description
      const descElements = document.querySelectorAll('.strategy-description, [data-strategy-description]');
      descElements.forEach(el => {
        el.textContent = strategy.description || 'No description available.';
      });
      
      // Update indicator displays if the function exists
      if (typeof window.updateIndicatorDisplays === 'function') {
        window.updateIndicatorDisplays(strategy.indicators || []);
      }
      
      // Update any other UI elements that depend on the strategy
      this.updateStrategySpecificUI(strategy);
      
    } catch (error) {
      console.error('[StrategyManager] Error updating UI:', error);
    }
  }
  
  /**
   * Update strategy-specific UI elements
   * @param {Object} strategy - The strategy object
   * @private
   */
  updateStrategySpecificUI(strategy) {
    // Add any strategy-specific UI updates here
    // This is a placeholder for future expansion
  }

  /**
   * Notify other components about strategy changes
   * @param {string} newStrategy - The new strategy ID
   * @param {string} previousStrategy - The previous strategy ID
   * @param {string} source - The source of the change
   */
  notifyStrategyChange(newStrategy, previousStrategy, source = 'unknown') {
    try {
      // Skip if this is just an initialization
      if (source === 'init') {
        return;
      }
      
      // Get strategy info
      const strategy = this.strategies[newStrategy] || {};
      const strategyName = strategy.name || newStrategy;
      
      // Show notification
      if (window.NotificationManager) {
        window.NotificationManager.show(
          `Strategy changed to: ${strategyName}`,
          { 
            type: 'info',
            duration: 3000,
            id: 'strategy-change-notification' // Use a fixed ID to prevent duplicates
          }
        );
      } else {
        console.log(`[StrategyManager] Strategy changed: ${previousStrategy || 'none'} -> ${newStrategy} (${source})`);
      }
      
      // Dispatch custom event
      const event = new CustomEvent('strategyChanged', {
        detail: {
          strategy: newStrategy,
          previous: previousStrategy,
          source: source,
          timestamp: Date.now(),
          fromStrategyManager: true
        },
        bubbles: true,
        cancelable: true
      });
      
      document.dispatchEvent(event);
      
      // Call any registered callbacks
      if (typeof window.onStrategyChanged === 'function') {
        window.onStrategyChanged(newStrategy, previousStrategy, source);
      }
    } catch (error) {
      console.error('[StrategyManager] Error notifying about strategy change:', error);
    }
  }
  
  /**
   * Get the current strategy
   * @returns {Object} The current strategy object
   */
  getCurrentStrategy() {
    return this.strategies[this.currentStrategy] || {};
  }
  
  /**
   * Get all available strategies
   * @returns {Object} All available strategies
   */
  getAllStrategies() {
    return { ...this.strategies };
  }
  
  /**
   * Clean up resources
   */
  destroy() {
    this.removeEventListeners();
    this.isInitialized = false;
    StrategyManager.instance = null;
  }
}

// Initialize and export singleton instance
window.StrategyManager = new StrategyManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => window.StrategyManager.init());
} else {
  window.StrategyManager.init();
}

export default window.StrategyManager;
