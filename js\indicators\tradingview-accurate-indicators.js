/**
 * TradingView-Accurate Technical Indicators
 * Calculations that match TradingView's exact formulas and standards
 * Critical for accurate 5-color signal system
 */

class TradingViewAccurateIndicators {
  /**
   * RSI - Relative Strength Index (TradingView Standard)
   * Uses <PERSON>'s smoothing (modified EMA) exactly like TradingView
   */
  static calculateRSI(closes, period = 14) {
    if (!Array.isArray(closes) || closes.length < period + 1) {
      return { value: null, error: 'Insufficient data' };
    }

    const changes = [];
    for (let i = 1; i < closes.length; i++) {
      changes.push(closes[i] - closes[i - 1]);
    }

    if (changes.length < period) {
      return { value: null, error: 'Insufficient price changes' };
    }

    // Initial averages (first period)
    let avgGain = 0;
    let avgLoss = 0;
    
    for (let i = 0; i < period; i++) {
      if (changes[i] > 0) {
        avgGain += changes[i];
      } else {
        avgLoss += Math.abs(changes[i]);
      }
    }
    
    avgGain /= period;
    avgLoss /= period;

    // <PERSON>'s smoothing for subsequent periods (exactly like TradingView)
    for (let i = period; i < changes.length; i++) {
      const gain = changes[i] > 0 ? changes[i] : 0;
      const loss = changes[i] < 0 ? Math.abs(changes[i]) : 0;
      
      // Wilder's smoothing: (previous_avg * (period - 1) + current_value) / period
      avgGain = (avgGain * (period - 1) + gain) / period;
      avgLoss = (avgLoss * (period - 1) + loss) / period;
    }

    if (avgLoss === 0) {
      return { value: 100, overbought: true, oversold: false };
    }

    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));

    return {
      value: Math.round(rsi * 100) / 100, // Round to 2 decimal places
      overbought: rsi > 70,
      oversold: rsi < 30,
      signalClass: rsi > 70 ? 'strong-sell' : rsi > 60 ? 'mild-sell' : 
                   rsi < 30 ? 'strong-buy' : rsi < 40 ? 'mild-buy' : 'neutral'
    };
  }

  /**
   * MACD - Moving Average Convergence Divergence (TradingView Standard)
   * Uses exact EMA calculations matching TradingView
   */
  static calculateMACD(closes, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
    if (!Array.isArray(closes) || closes.length < slowPeriod) {
      return { value: null, error: 'Insufficient data' };
    }

    // Calculate EMAs using TradingView method
    const fastEMA = this.calculateEMA(closes, fastPeriod);
    const slowEMA = this.calculateEMA(closes, slowPeriod);

    if (!fastEMA.length || !slowEMA.length) {
      return { value: null, error: 'EMA calculation failed' };
    }

    // Calculate MACD line
    const macdLine = fastEMA[fastEMA.length - 1] - slowEMA[slowEMA.length - 1];

    // Calculate MACD history for signal line
    const macdHistory = [];
    const minLength = Math.min(fastEMA.length, slowEMA.length);
    
    for (let i = 0; i < minLength; i++) {
      macdHistory.push(fastEMA[i] - slowEMA[i]);
    }

    // Calculate signal line (EMA of MACD)
    const signalEMA = this.calculateEMA(macdHistory, signalPeriod);
    const signalLine = signalEMA.length > 0 ? signalEMA[signalEMA.length - 1] : 0;

    // Calculate histogram
    const histogram = macdLine - signalLine;

    return {
      macd: Math.round(macdLine * 100000) / 100000, // 5 decimal precision
      signal: Math.round(signalLine * 100000) / 100000,
      histogram: Math.round(histogram * 100000) / 100000,
      signalClass: histogram > 0 ? (histogram > 0.1 ? 'strong-buy' : 'mild-buy') :
                   histogram < 0 ? (histogram < -0.1 ? 'strong-sell' : 'mild-sell') : 'neutral'
    };
  }

  /**
   * EMA - Exponential Moving Average (TradingView Standard)
   * Exact formula used by TradingView
   */
  static calculateEMA(data, period) {
    if (!Array.isArray(data) || data.length < period) {
      return [];
    }

    const ema = [];
    const multiplier = 2 / (period + 1);

    // First EMA value is SMA of first 'period' values
    let sum = 0;
    for (let i = 0; i < period; i++) {
      sum += data[i];
    }
    ema.push(sum / period);

    // Calculate subsequent EMA values
    for (let i = period; i < data.length; i++) {
      const emaValue = (data[i] * multiplier) + (ema[ema.length - 1] * (1 - multiplier));
      ema.push(emaValue);
    }

    return ema;
  }

  /**
   * Stochastic RSI (TradingView Standard)
   */
  static calculateStochRSI(closes, rsiPeriod = 14, stochPeriod = 14) {
    if (!Array.isArray(closes) || closes.length < rsiPeriod + stochPeriod) {
      return { value: null, error: 'Insufficient data' };
    }

    // Calculate RSI values for the entire dataset
    const rsiValues = [];
    for (let i = rsiPeriod; i < closes.length; i++) {
      const rsiData = this.calculateRSI(closes.slice(0, i + 1), rsiPeriod);
      if (rsiData.value !== null) {
        rsiValues.push(rsiData.value);
      }
    }

    if (rsiValues.length < stochPeriod) {
      return { value: null, error: 'Insufficient RSI values' };
    }

    // Calculate Stochastic of RSI
    const recentRSI = rsiValues.slice(-stochPeriod);
    const highestRSI = Math.max(...recentRSI);
    const lowestRSI = Math.min(...recentRSI);
    const currentRSI = rsiValues[rsiValues.length - 1];

    if (highestRSI === lowestRSI) {
      return { value: 50, signalClass: 'neutral' };
    }

    const stochRSI = ((currentRSI - lowestRSI) / (highestRSI - lowestRSI)) * 100;

    return {
      value: Math.round(stochRSI * 100) / 100,
      overbought: stochRSI > 80,
      oversold: stochRSI < 20,
      signalClass: stochRSI > 80 ? 'strong-sell' : stochRSI > 70 ? 'mild-sell' :
                   stochRSI < 20 ? 'strong-buy' : stochRSI < 30 ? 'mild-buy' : 'neutral'
    };
  }

  /**
   * Williams %R (TradingView Standard)
   */
  static calculateWilliamsR(highs, lows, closes, period = 14) {
    if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes) ||
        highs.length < period || lows.length < period || closes.length < period) {
      return { value: null, error: 'Insufficient data' };
    }

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    if (highestHigh === lowestLow) {
      return { value: -50, signalClass: 'neutral' };
    }

    const williamsR = ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;

    return {
      value: Math.round(williamsR * 100) / 100,
      overbought: williamsR > -20,
      oversold: williamsR < -80,
      signalClass: williamsR > -20 ? 'strong-sell' : williamsR > -30 ? 'mild-sell' :
                   williamsR < -80 ? 'strong-buy' : williamsR < -70 ? 'mild-buy' : 'neutral'
    };
  }

  /**
   * ATR - Average True Range (TradingView Standard)
   */
  static calculateATR(highs, lows, closes, period = 14) {
    if (!Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes) ||
        highs.length < period + 1 || lows.length < period + 1 || closes.length < period + 1) {
      return { value: null, error: 'Insufficient data' };
    }

    const trueRanges = [];
    
    for (let i = 1; i < closes.length; i++) {
      const high = highs[i];
      const low = lows[i];
      const prevClose = closes[i - 1];
      
      const tr1 = high - low;
      const tr2 = Math.abs(high - prevClose);
      const tr3 = Math.abs(low - prevClose);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    if (trueRanges.length < period) {
      return { value: null, error: 'Insufficient true range values' };
    }

    // Use Wilder's smoothing (same as RSI)
    let atr = 0;
    for (let i = 0; i < period; i++) {
      atr += trueRanges[i];
    }
    atr /= period;

    // Apply Wilder's smoothing for remaining periods
    for (let i = period; i < trueRanges.length; i++) {
      atr = (atr * (period - 1) + trueRanges[i]) / period;
    }

    return {
      value: Math.round(atr * 100000) / 100000,
      signalClass: 'neutral' // ATR is volatility, not directional
    };
  }

  /**
   * Bollinger Bands (TradingView Standard)
   */
  static calculateBollingerBands(closes, period = 20, stdDev = 2) {
    if (!Array.isArray(closes) || closes.length < period) {
      return { value: null, error: 'Insufficient data' };
    }

    const recentCloses = closes.slice(-period);
    
    // Calculate SMA
    const sma = recentCloses.reduce((sum, price) => sum + price, 0) / period;
    
    // Calculate standard deviation
    const variance = recentCloses.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);
    
    const upperBand = sma + (standardDeviation * stdDev);
    const lowerBand = sma - (standardDeviation * stdDev);
    const currentPrice = closes[closes.length - 1];
    
    // Calculate position within bands
    const bandPosition = ((currentPrice - lowerBand) / (upperBand - lowerBand)) * 100;
    
    return {
      upper: Math.round(upperBand * 100) / 100,
      middle: Math.round(sma * 100) / 100,
      lower: Math.round(lowerBand * 100) / 100,
      position: Math.round(bandPosition * 100) / 100,
      signalClass: bandPosition > 80 ? 'strong-sell' : bandPosition > 60 ? 'mild-sell' :
                   bandPosition < 20 ? 'strong-buy' : bandPosition < 40 ? 'mild-buy' : 'neutral'
    };
  }
}

// Export for use in server.js (Node.js)
module.exports = TradingViewAccurateIndicators;

// Also make available globally for browser
if (typeof window !== 'undefined') {
  window.TradingViewAccurateIndicators = TradingViewAccurateIndicators;
}
