View System Status: Press Ctrl+Shift+D to toggle the status dashboard
Manual Health Check: Press Ctrl+Shift+H
Force Recovery: Press Ctrl+Shift+R
Console Commands:
window.systemStatusDashboard.show()
window.systemHealthMonitor.performHealthCheck()
window.errorRecoverySystem.getErrorHistory()

// Verify all data integrity
window.dataFlowVerifier.forceVerification()

// Check data quality metrics
window.dataFlowVerifier.getDataQuality()

// View system status
window.systemStatusDashboard.show()

// Check current price source
window.advancedMLFeatures.getSafeCurrentPrice()

ENHANCED KEYBOARD SHORTCUTS:
Ctrl+Shift+W = Trigger Warp Jump
Ctrl+Shift+H = System Health Check
Ctrl+Shift+S = Synchronize All Systems
🖥️ MENU TESTING COMMANDS:
// Test all menu visibility
starCryptControls.testMenus()
testAllMenuVisibility()

// Show specific menu with animation
starCryptControls.showMenu('indicatorMenu')
starCryptControls.showMenu('thresholdsMenu')
starCryptControls.showMenu('signalLogicMenu')

// Close all menus
starCryptControls.closeMenus()

// Manual menu positioning
ensureMenuInViewport(document.getElementById('menuId'))


// Test enhanced tooltips
window.enhancedTooltipFixes?.diagnoseTooltipStatus();

// Test mini charts
window.enhancedMiniCharts?.diagnoseChartStatus();

// Test ML system
window.advancedMLFeatures?.generateAdvancedPredictions();