/**
 * 🚀 UNIFIED SIGNAL COMMANDER - DEGEN ARMY COMMAND CENTER
 * Consolidates all signal handling systems into one master controller
 * Eliminates conflicts between multiple overlapping handlers
 * Preserves all strategies, indicators, and functionality
 */

class UnifiedSignalCommander {
  constructor() {
    this.admiralMode = false;
    this.selectedLights = new Set();
    this.currentTimeframe = '1h';
    this.currentIndicator = null;
    this.eventListenersAttached = false;
    this.debugMode = true;
    this.triggerMLAnalysis = null; // Will be set by ML bridge

    // State management
    this.state = {
      admiralMode: false,
      selectedSignals: new Set(),
      currentTimeframe: '1h',
      lastClickTime: 0,
      clickDebounceMs: 100
    };

    this.init();
  }

  init() {
    console.log('🚀 UNIFIED SIGNAL COMMANDER: Initializing master control system...');
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupMasterEventHandler());
    } else {
      this.setupMasterEventHandler();
    }
    
    // Initialize Admiral mode state synchronization
    this.syncAdmiralModeState();
    
    // Setup ML system integration
    this.initializeMLIntegration();
    
    console.log('✅ UNIFIED SIGNAL COMMANDER: Master control system online');
  }

  setupMasterEventHandler() {
    if (this.eventListenersAttached) {
      console.log('⚠️ UNIFIED SIGNAL COMMANDER: Event listeners already attached, skipping');
      return;
    }

    // MASTER CLICK HANDLER - Replaces all fragmented handlers
    document.addEventListener('click', (event) => {
      this.handleMasterClick(event);
    }, { capture: true }); // Use capture to intercept before other handlers

    this.eventListenersAttached = true;
    console.log('✅ UNIFIED SIGNAL COMMANDER: Master event handler deployed');
  }

  handleMasterClick(event) {
    // Debounce rapid clicks
    const now = Date.now();
    if (now - this.state.lastClickTime < this.state.clickDebounceMs) {
      return;
    }
    this.state.lastClickTime = now;

    // Find signal circle in event path
    const signalCircle = event.target.closest('.signal-circle');
    if (!signalCircle) return;

    // Extract signal data with comprehensive attribute checking
    const signalData = this.extractSignalData(signalCircle);
    if (!signalData.indicator || !signalData.timeframe) {
      console.warn('🚀 UNIFIED SIGNAL COMMANDER: Invalid signal data', signalData);
      return;
    }

    // Prevent event propagation to stop other handlers
    event.stopPropagation();
    event.preventDefault();

    // Route to appropriate handler based on Admiral mode
    if (this.isAdmiralModeActive()) {
      this.handleAdmiralModeSelection(signalData, signalCircle);
    } else {
      this.handleNormalModeSelection(signalData, signalCircle);
    }
  }

  extractSignalData(element) {
    // Comprehensive data extraction with fallbacks
    const indicator = element.getAttribute('data-ind') || 
                     element.getAttribute('data-indicator') || 
                     element.getAttribute('data-connected-indicator') ||
                     element.dataset.ind ||
                     element.dataset.indicator;

    const timeframe = element.getAttribute('data-tf') || 
                     element.getAttribute('data-timeframe') || 
                     element.getAttribute('data-connected-timeframe') ||
                     element.dataset.tf ||
                     element.dataset.timeframe;

    return {
      indicator: indicator ? indicator.toLowerCase() : null,
      timeframe: timeframe ? timeframe.toLowerCase() : null,
      element: element
    };
  }

  isAdmiralModeActive() {
    // Unified Admiral mode state checking
    return this.state.admiralMode || 
           window.admiralMode || 
           (window.advancedMLFeatures && window.advancedMLFeatures.admiralMode) ||
           document.body.classList.contains('admiral-mode-active');
  }

  handleAdmiralModeSelection(signalData, element) {
    const { indicator, timeframe } = signalData;
    const signalKey = `${indicator}-${timeframe}`;
    
    console.log(`🎯 ADMIRAL MODE: Processing signal ${signalKey}`);

    // Toggle selection state
    const isCurrentlySelected = element.classList.contains('selected') || 
                               element.classList.contains('golden-selected');
    
    if (isCurrentlySelected) {
      // Deselect
      element.classList.remove('selected', 'golden-selected');
      element.style.boxShadow = '';
      this.selectedLights.delete(signalKey);
      this.state.selectedSignals.delete(signalKey);
      
      console.log(`❌ ADMIRAL: Deselected ${signalKey} (${this.selectedLights.size} total)`);
    } else {
      // Select
      element.classList.add('selected', 'golden-selected');
      element.style.boxShadow = '0 0 15px #ffd700, 0 0 30px #ffd700';
      this.selectedLights.add(signalKey);
      this.state.selectedSignals.add(signalKey);
      
      console.log(`✅ ADMIRAL: Selected ${signalKey} (${this.selectedLights.size} total)`);
    }

    // Update ML systems
    this.updateMLSystems();
    
    // Update UI displays
    this.updateSelectedLightsDisplay();
    
    // Arm/disarm analyze button
    this.updateAnalyzeButton();
    
    // Trigger live analysis
    this.triggerLiveAnalysis(signalData);
  }

  handleNormalModeSelection(signalData, _element) {
    const { indicator, timeframe } = signalData;

    console.log(`📊 NORMAL MODE: Switching to ${indicator}(${timeframe})`);

    // Update global state
    this.currentTimeframe = timeframe;
    this.currentIndicator = indicator;
    window.currentTf = timeframe;
    window.currentTimeframe = timeframe;
    window.currentIndicator = indicator;

    // Highlight timeframe column
    this.highlightTimeframeColumn(timeframe);

    // Update TradingView chart
    this.updateTradingViewChart(indicator, timeframe);

    // Update UI elements
    this.updateNormalModeUI(indicator, timeframe);

    // Update mini charts
    if (typeof window.updateMiniCharts === 'function') {
      window.updateMiniCharts();
    }
  }

  syncAdmiralModeState() {
    // Create unified Admiral mode state management
    Object.defineProperty(window, 'admiralMode', {
      get: () => this.state.admiralMode,
      set: (value) => {
        this.state.admiralMode = value;
        if (window.advancedMLFeatures) {
          window.advancedMLFeatures.admiralMode = value;
        }
        if (value) {
          document.body.classList.add('admiral-mode-active');
        } else {
          document.body.classList.remove('admiral-mode-active');
        }
        console.log(`🎯 ADMIRAL MODE: ${value ? 'ACTIVATED' : 'DEACTIVATED'}`);
      }
    });
  }

  initializeMLIntegration() {
    // Bridge to existing ML systems
    if (window.MLHistoricalAnalysis) {
      window.MLHistoricalAnalysis.selectedLights = this.selectedLights;
    }
    
    if (window.mlHistoricalAnalysis) {
      window.mlHistoricalAnalysis.selectedLights = this.selectedLights;
    }
    
    console.log('✅ UNIFIED SIGNAL COMMANDER: ML integration established');
  }

  updateMLSystems() {
    // Sync with all ML systems
    if (window.MLHistoricalAnalysis) {
      window.MLHistoricalAnalysis.selectedLights = this.selectedLights;
    }
    
    if (window.mlHistoricalAnalysis) {
      window.mlHistoricalAnalysis.selectedLights = this.selectedLights;
    }
    
    if (window.advancedMLFeatures) {
      window.advancedMLFeatures.selectedSignals = Array.from(this.selectedLights);
    }
  }

  updateSelectedLightsDisplay() {
    const display = document.getElementById('selectedLightsDisplay');
    if (!display) return;

    if (this.selectedLights.size === 0) {
      display.innerHTML = '<span class="no-selection">No signals selected</span>';
      return;
    }

    const lightsList = Array.from(this.selectedLights).map(signalKey => {
      const [indicator, timeframe] = signalKey.split('-');
      const element = document.querySelector(`.signal-circle[data-ind="${indicator}"][data-tf="${timeframe}"]`);
      const color = element ? element.style.backgroundColor || '#00ccff' : '#00ccff';
      
      return `<span style="color: ${color}; margin-right: 1rem; padding: 0.2rem 0.5rem; border: 1px solid ${color}; border-radius: 3px; font-size: 0.9rem;">
        ${indicator.toUpperCase()}(${timeframe})
      </span>`;
    }).join('');

    display.innerHTML = `
      <div style="color: var(--text-color); margin-bottom: 0.5rem; font-weight: bold;">
        Selected Signals for Convergence Analysis (${this.selectedLights.size}):
      </div>
      <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">${lightsList}</div>
      <div style="color: #00ccff; font-size: 0.8rem; margin-top: 0.5rem;">
        Click "🔍 Analyze Convergence" to find historical patterns
      </div>
    `;
  }

  updateAnalyzeButton() {
    const analyzeBtn = document.getElementById('analyzeConvergence');
    if (!analyzeBtn) return;

    if (this.selectedLights.size > 0) {
      analyzeBtn.disabled = false;
      analyzeBtn.style.opacity = '1';
      analyzeBtn.style.cursor = 'pointer';
      analyzeBtn.style.background = 'linear-gradient(45deg, #00ff00, #ffff00)';
      analyzeBtn.style.boxShadow = '0 0 20px #00ff00';
      analyzeBtn.style.transform = 'scale(1.05)';
      analyzeBtn.textContent = `🎯 Analyze ${this.selectedLights.size} Signals`;
    } else {
      analyzeBtn.disabled = true;
      analyzeBtn.style.opacity = '0.5';
      analyzeBtn.style.cursor = 'not-allowed';
      analyzeBtn.style.background = '';
      analyzeBtn.style.boxShadow = '';
      analyzeBtn.style.transform = '';
      analyzeBtn.textContent = '🔍 Analyze Convergence';
    }
  }

  triggerLiveAnalysis(signalData) {
    // Trigger live analysis without spam
    if (typeof window.updateLiveModeAnalysis === 'function') {
      window.updateLiveModeAnalysis(signalData, this.selectedLights.size);
    }

    // Trigger ML convergence analysis if we have enough signals
    if (this.selectedLights.size >= 2 && this.triggerMLAnalysis) {
      console.log('🚀 UNIFIED COMMANDER: Triggering ML convergence analysis');
      this.triggerMLAnalysis();
    }
  }

  highlightTimeframeColumn(timeframe) {
    // Remove existing highlights
    document.querySelectorAll('.signal-circle').forEach(circle => {
      circle.classList.remove('timeframe-highlighted', 'golden-glow');
    });

    // Add highlight to all signals in the timeframe
    document.querySelectorAll(`[data-tf="${timeframe}"]`).forEach(circle => {
      circle.classList.add('timeframe-highlighted', 'golden-glow');
    });

    console.log(`📊 NORMAL MODE: Highlighted timeframe column ${timeframe}`);
  }

  updateTradingViewChart(indicator, timeframe) {
    // Update TradingView chart if function exists
    if (typeof window.initializeTradingViewChart === 'function') {
      let chartIndicator = indicator;

      // Handle special indicators
      if (['volume', 'sentiment', 'entropy', 'correlation', 'time_anomaly', 'ml'].includes(indicator)) {
        chartIndicator = 'macd'; // Default to MACD for non-standard indicators
      }

      window.initializeTradingViewChart(window.currentPair || 'xbtusdt', timeframe, chartIndicator);
      console.log(`📊 NORMAL MODE: Updated TradingView chart to ${chartIndicator}(${timeframe})`);
    }
  }

  updateNormalModeUI(indicator, timeframe) {
    // Update current timeframe display
    const currentTfElement = document.getElementById('currentTf');
    if (currentTfElement) {
      currentTfElement.innerText = timeframe;
    }

    // Update timeframe buttons
    document.querySelectorAll('.timeframe-button').forEach(btn => {
      if (btn.dataset.tf === timeframe) {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    });

    // Update signal circle states
    document.querySelectorAll('.signal-circle').forEach(circle => {
      circle.classList.remove('selected-indicator', 'active');
    });

    // Highlight selected indicator and timeframe
    document.querySelectorAll(`[data-tf="${timeframe}"]`).forEach(circle => {
      circle.classList.add('active');
    });

    const selectedCircle = document.querySelector(`.signal-circle[data-ind="${indicator}"][data-tf="${timeframe}"]`);
    if (selectedCircle) {
      selectedCircle.classList.add('selected-indicator');
    }

    console.log(`📊 NORMAL MODE: Updated UI for ${indicator}(${timeframe})`);
  }

  // Public API methods
  setAdmiralMode(enabled) {
    window.admiralMode = enabled;
  }

  getSelectedSignals() {
    return Array.from(this.selectedLights);
  }

  clearSelectedSignals() {
    // Clear all selections
    document.querySelectorAll('.signal-circle.selected, .signal-circle.golden-selected').forEach(circle => {
      circle.classList.remove('selected', 'golden-selected');
      circle.style.boxShadow = '';
    });

    this.selectedLights.clear();
    this.state.selectedSignals.clear();
    this.updateSelectedLightsDisplay();
    this.updateAnalyzeButton();

    console.log('🧹 UNIFIED SIGNAL COMMANDER: Cleared all signal selections');
  }

  // COMPLETE SYSTEM TAKEOVER - Disable ALL competing handlers
  disableOldHandlers() {
    console.log('🚀 UNIFIED SIGNAL COMMANDER: 🎯 COMPLETE SYSTEM TAKEOVER - Disabling ALL competing systems');

    // Override ALL signal handling functions
    window.handleSignalClick = (ind, tf) => {
      console.log(`🔄 LEGACY REDIRECT: handleSignalClick(${ind}, ${tf}) → Unified Signal Commander`);
      const element = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`);
      if (element) element.click();
    };

    // Disable ALL strategy management systems
    if (window.StrategyManager) {
      window.StrategyManager.prototype.handleStrategyChange = () => {
        console.log('🚫 STRATEGY MANAGER BLOCKED: Redirected to Unified Signal Commander');
      };
    }

    if (window.StrategyCore) {
      window.StrategyCore.prototype.handleStrategyChange = () => {
        console.log('🚫 STRATEGY CORE BLOCKED: Redirected to Unified Signal Commander');
      };
    }

    // Override strategy notification functions
    if (window.notifyStrategyChange) {
      window.notifyStrategyChange = (strategyId) => {
        console.log(`🔄 STRATEGY REDIRECT: notifyStrategyChange(${strategyId}) → Unified Signal Commander`);

        // 🌟 TRIGGER WARP JUMP ANIMATION
        if (window.starfieldAnimation) {
          console.log('[UnifiedSignalCommander] 🚀 Triggering warp jump animation for strategy change');
          window.starfieldAnimation.triggerWarpJump();
        } else {
          // Dispatch event for starfield animation
          document.dispatchEvent(new CustomEvent('triggerWarpJump', {
            detail: { reason: 'strategy_change', strategy: strategyId }
          }));
        }

        // Dispatch strategy change event for other systems
        document.dispatchEvent(new CustomEvent('strategyChanged', {
          detail: { strategy: strategyId }
        }));
      };
    }

    // Disable signal system handlers
    if (window.signalSystem) {
      window.signalSystem.handleSignalClick = () => {
        console.log('🚫 SIGNAL SYSTEM BLOCKED: Redirected to Unified Signal Commander');
      };
    }

    // Override update signal lights to prevent conflicts
    if (window.updateAllSignalLights) {
      window.originalUpdateAllSignalLights = window.updateAllSignalLights;
      window.updateAllSignalLights = () => {
        console.log('🔄 SIGNAL LIGHTS REDIRECT: updateAllSignalLights() → Unified Signal Commander');
        // Actually update the signal lights using the original function
        if (window.originalUpdateAllSignalLights) {
          window.originalUpdateAllSignalLights();
        }
      };
    }

    console.log('✅ UNIFIED SIGNAL COMMANDER: COMPLETE SYSTEM TAKEOVER SUCCESSFUL - All competing systems disabled');
  }
}

// Initialize the unified system
window.unifiedSignalCommander = new UnifiedSignalCommander();

// Disable old handlers after initialization
setTimeout(() => {
  window.unifiedSignalCommander.disableOldHandlers();
}, 1000);

// Export for global access
window.UnifiedSignalCommander = UnifiedSignalCommander;

// Provide backward compatibility
window.handleSignalClick = (ind, tf) => {
  console.log('🔄 LEGACY REDIRECT: Redirecting to Unified Signal Commander');
  const element = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`);
  if (element) {
    element.click();
  }
};

// 🧪 COMPREHENSIVE SYSTEM TEST FUNCTION
window.testUnifiedSignalCommander = function() {
  console.log('🧪 TESTING: Unified Signal Commander System');
  console.log('═'.repeat(60));

  const commander = window.unifiedSignalCommander;
  if (!commander) {
    console.error('❌ FAILED: Unified Signal Commander not found');
    return false;
  }

  // Test 1: Admiral Mode Toggle
  console.log('🧪 TEST 1: Admiral Mode Toggle');
  const originalMode = commander.isAdmiralModeActive();
  commander.setAdmiralMode(true);
  const admiralActive = commander.isAdmiralModeActive();
  commander.setAdmiralMode(originalMode);
  console.log(`✅ Admiral Mode Toggle: ${admiralActive ? 'PASS' : 'FAIL'}`);

  // Test 2: Signal Selection
  console.log('🧪 TEST 2: Signal Selection');
  const testSignals = document.querySelectorAll('.signal-circle');
  const testCount = Math.min(3, testSignals.length);
  console.log(`📊 Found ${testSignals.length} signal circles, testing ${testCount}`);

  // Test 3: Clear Selections
  console.log('🧪 TEST 3: Clear Selections');
  commander.clearSelectedSignals();
  const clearedCount = commander.getSelectedSignals().length;
  console.log(`✅ Clear Selections: ${clearedCount === 0 ? 'PASS' : 'FAIL'}`);

  // Test 4: ML Integration
  console.log('🧪 TEST 4: ML Integration');
  const mlSystem = window.MLHistoricalAnalysis || window.mlHistoricalAnalysis;
  const mlIntegrated = mlSystem && mlSystem.selectedLights === commander.selectedLights;
  console.log(`✅ ML Integration: ${mlIntegrated ? 'PASS' : 'FAIL'}`);

  console.log('═'.repeat(60));
  console.log('🎉 UNIFIED SIGNAL COMMANDER: System test complete');

  return true;
};

console.log('🚀 UNIFIED SIGNAL COMMANDER: Module loaded and ready for deployment');
console.log('💡 TIP: Run window.testUnifiedSignalCommander() to test the system');
