/**
 * Dual Tooltip System for StarCrypt
 * Separates indicator description tooltips from mini chart data tooltips
 */

class DualTooltipSystem {
  constructor() {
    this.indicatorTooltip = null;
    this.chartTooltip = null;
    this.currentTooltipType = null;
    
    this.init();
  }

  init() {
    console.log('🎯 DUAL TOOLTIP: Initializing separate tooltip systems...');
    
    this.createTooltipElements();
    this.setupIndicatorDescriptions();
    this.setupEventListeners();
    
    console.log('✅ DUAL TOOLTIP: Separate tooltip systems active');
  }

  createTooltipElements() {
    // Create indicator description tooltip
    this.indicatorTooltip = document.createElement('div');
    this.indicatorTooltip.id = 'indicator-description-tooltip';
    this.indicatorTooltip.style.cssText = `
      position: absolute;
      z-index: 10001;
      background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.9));
      color: #ffffff;
      padding: 12px 16px;
      border-radius: 8px;
      font-size: 13px;
      line-height: 1.4;
      max-width: 300px;
      border: 2px solid rgba(0, 255, 255, 0.6);
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
      display: none;
      pointer-events: none;
      backdrop-filter: blur(10px);
    `;
    document.body.appendChild(this.indicatorTooltip);

    // Create mini chart data tooltip
    this.chartTooltip = document.createElement('div');
    this.chartTooltip.id = 'chart-data-tooltip';
    this.chartTooltip.style.cssText = `
      position: absolute;
      z-index: 10002;
      background: rgba(0, 0, 0, 0.9);
      color: #00FFFF;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-family: 'Courier New', monospace;
      border: 1px solid #00FFFF;
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
      display: none;
      pointer-events: none;
      white-space: nowrap;
    `;
    document.body.appendChild(this.chartTooltip);
  }

  setupIndicatorDescriptions() {
    this.indicatorDescriptions = {
      rsi: {
        name: "RSI (Relative Strength Index)",
        description: "Measures momentum on a scale of 0-100. Values above 70 suggest overbought conditions (potential sell), below 30 suggest oversold (potential buy). RSI helps identify trend reversals and entry/exit points.",
        usage: "🔴 >70: Overbought (Consider selling)\n🟡 30-70: Neutral zone\n🟢 <30: Oversold (Consider buying)"
      },
      stochRsi: {
        name: "Stochastic RSI",
        description: "A more sensitive version of RSI that oscillates between 0-100. It identifies overbought/oversold conditions more quickly than regular RSI, making it useful for short-term trading.",
        usage: "🔴 >80: Overbought (Sell signal)\n🟡 20-80: Normal range\n🟢 <20: Oversold (Buy signal)"
      },
      macd: {
        name: "MACD (Moving Average Convergence Divergence)",
        description: "Shows the relationship between two moving averages. The MACD line crossing above the signal line suggests bullish momentum, while crossing below suggests bearish momentum.",
        usage: "🟢 MACD > Signal: Bullish trend\n🔴 MACD < Signal: Bearish trend\n📊 Histogram shows momentum strength"
      },
      mfi: {
        name: "MFI (Money Flow Index)",
        description: "Incorporates volume with price to measure buying/selling pressure. Similar to RSI but considers volume, making it more reliable for volume-based analysis.",
        usage: "🔴 >80: Overbought with volume\n🟡 20-80: Balanced flow\n🟢 <20: Oversold with volume"
      },
      volume: {
        name: "Volume",
        description: "Shows the number of shares/contracts traded. High volume confirms price movements, while low volume suggests weak conviction. Volume precedes price movements.",
        usage: "🔥 High Volume: Strong conviction\n📊 Average Volume: Normal activity\n💤 Low Volume: Weak interest"
      },
      bollingerBands: {
        name: "Bollinger Bands",
        description: "Shows volatility and potential support/resistance levels. Price touching upper band suggests overbought, touching lower band suggests oversold conditions.",
        usage: "🔴 Price at Upper Band: Overbought\n🟡 Price in Middle: Neutral\n🟢 Price at Lower Band: Oversold"
      },
      atr: {
        name: "ATR (Average True Range)",
        description: "Measures market volatility. High ATR indicates high volatility (larger price movements), low ATR indicates low volatility (smaller movements).",
        usage: "🔥 High ATR: High volatility\n📊 Medium ATR: Normal volatility\n💤 Low ATR: Low volatility"
      },
      adx: {
        name: "ADX (Average Directional Index)",
        description: "Measures trend strength regardless of direction. Values above 25 indicate strong trends, below 20 indicate weak trends or sideways movement.",
        usage: "🚀 >25: Strong trend\n📊 20-25: Developing trend\n💤 <20: Weak/no trend"
      },
      williamsR: {
        name: "Williams %R",
        description: "Momentum oscillator that moves between 0 and -100. It's the inverse of the Stochastic Oscillator and helps identify overbought/oversold conditions.",
        usage: "🔴 -20 to 0: Overbought\n🟡 -50 to -20: Normal\n🟢 -100 to -80: Oversold"
      },
      vwap: {
        name: "VWAP (Volume Weighted Average Price)",
        description: "Shows the average price weighted by volume. Price above VWAP suggests bullish sentiment, below suggests bearish sentiment.",
        usage: "🟢 Price > VWAP: Bullish\n🔴 Price < VWAP: Bearish\n📊 Price = VWAP: Neutral"
      }
    };
  }

  setupEventListeners() {
    document.addEventListener('mouseover', (e) => {
      this.handleMouseOver(e);
    });

    document.addEventListener('mouseout', (e) => {
      this.handleMouseOut(e);
    });

    document.addEventListener('mousemove', (e) => {
      this.handleMouseMove(e);
    });
  }

  handleMouseOver(e) {
    const target = e.target;

    // Check for indicator name/label hover (for descriptions)
    const indicatorRow = target.closest('tr[data-indicator]');
    const indicatorLabel = target.closest('.indicator-label, .indicator-name');
    
    if (indicatorLabel && indicatorRow) {
      const indicator = indicatorRow.getAttribute('data-indicator') || indicatorRow.getAttribute('data-ind');
      if (indicator && this.indicatorDescriptions[indicator]) {
        this.showIndicatorTooltip(e, indicator);
        return;
      }
    }

    // Check for mini chart hover (for data)
    const miniChart = target.closest('.mini-chart-container, .enhanced-chart-container');
    const signalCircle = target.closest('.signal-circle[data-tf]');
    
    if (miniChart || signalCircle) {
      const indicator = miniChart?.getAttribute('data-indicator') || 
                      signalCircle?.getAttribute('data-ind') || 
                      signalCircle?.getAttribute('data-indicator');
      const timeframe = signalCircle?.getAttribute('data-tf') || 
                       signalCircle?.getAttribute('data-timeframe');
      
      if (indicator && timeframe) {
        this.showChartTooltip(e, indicator, timeframe);
        return;
      }
    }
  }

  handleMouseOut(e) {
    // Hide tooltips when mouse leaves relevant areas
    if (!e.relatedTarget || 
        (!e.relatedTarget.closest('.indicator-label, .indicator-name') && 
         !e.relatedTarget.closest('.mini-chart-container, .enhanced-chart-container, .signal-circle'))) {
      this.hideAllTooltips();
    }
  }

  handleMouseMove(e) {
    // Update tooltip positions
    if (this.indicatorTooltip.style.display === 'block') {
      this.positionTooltip(this.indicatorTooltip, e);
    }
    if (this.chartTooltip.style.display === 'block') {
      this.positionTooltip(this.chartTooltip, e);
    }
  }

  showIndicatorTooltip(e, indicator) {
    const description = this.indicatorDescriptions[indicator];
    if (!description) return;

    this.hideAllTooltips();
    this.currentTooltipType = 'indicator';

    this.indicatorTooltip.innerHTML = `
      <div style="font-weight: bold; color: #00FFFF; margin-bottom: 8px;">
        ${description.name}
      </div>
      <div style="margin-bottom: 8px;">
        ${description.description}
      </div>
      <div style="font-size: 11px; color: #CCCCCC; white-space: pre-line;">
        ${description.usage}
      </div>
    `;

    this.indicatorTooltip.style.display = 'block';
    this.positionTooltip(this.indicatorTooltip, e);
  }

  showChartTooltip(e, indicator, timeframe) {
    this.hideAllTooltips();
    this.currentTooltipType = 'chart';

    // Get real data for this indicator/timeframe
    const data = this.getIndicatorData(indicator, timeframe);
    const timestamp = this.getDataTimestamp(indicator, timeframe);

    let content = `<strong>${indicator.toUpperCase()} (${timeframe})</strong><br>`;
    
    if (data !== null && data !== undefined) {
      if (typeof data === 'object' && data.value !== undefined) {
        content += `Value: ${data.value.toFixed(4)}<br>`;
      } else if (typeof data === 'number') {
        content += `Value: ${data.toFixed(4)}<br>`;
      }
    } else {
      content += `Value: No data<br>`;
    }

    if (timestamp) {
      const date = new Date(timestamp);
      content += `Updated: ${date.toLocaleTimeString()}`;
    } else {
      content += `Updated: Unknown`;
    }

    this.chartTooltip.innerHTML = content;
    this.chartTooltip.style.display = 'block';
    this.positionTooltip(this.chartTooltip, e);
  }

  getIndicatorData(indicator, timeframe) {
    if (window.indicatorsData && window.indicatorsData[timeframe] && window.indicatorsData[timeframe][indicator]) {
      return window.indicatorsData[timeframe][indicator];
    }
    return null;
  }

  getDataTimestamp(indicator, timeframe) {
    if (window.wsDataTimestamps && window.wsDataTimestamps[timeframe] && window.wsDataTimestamps[timeframe][indicator]) {
      return window.wsDataTimestamps[timeframe][indicator];
    }
    return null;
  }

  positionTooltip(tooltip, e) {
    const rect = tooltip.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left = e.clientX + 15;
    let top = e.clientY - 10;

    // Adjust if tooltip would go off screen
    if (left + rect.width > viewportWidth) {
      left = e.clientX - rect.width - 15;
    }
    if (top + rect.height > viewportHeight) {
      top = e.clientY - rect.height - 10;
    }
    if (top < 0) {
      top = 10;
    }
    if (left < 0) {
      left = 10;
    }

    tooltip.style.left = `${left}px`;
    tooltip.style.top = `${top}px`;
  }

  hideAllTooltips() {
    this.indicatorTooltip.style.display = 'none';
    this.chartTooltip.style.display = 'none';
    this.currentTooltipType = null;
  }

  // Public API
  addIndicatorDescription(indicator, description) {
    this.indicatorDescriptions[indicator] = description;
  }

  updateIndicatorDescription(indicator, updates) {
    if (this.indicatorDescriptions[indicator]) {
      Object.assign(this.indicatorDescriptions[indicator], updates);
    }
  }
}

// Initialize dual tooltip system
window.dualTooltipSystem = new DualTooltipSystem();

// Export for global access
window.DualTooltipSystem = DualTooltipSystem;

console.log('🎯 DUAL TOOLTIP: Separate tooltip systems loaded and active');
