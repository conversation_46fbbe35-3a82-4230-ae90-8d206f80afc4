/**
 * StarCrypt Data Integrity Validator
 * Ensures all data sources are legitimate and trading-grade
 * Zero tolerance for dummy, mock, or fallback data
 */

class DataIntegrityValidator {
  constructor() {
    this.validationRules = new Map();
    this.dataSourceRegistry = new Map();
    this.validationHistory = [];
    this.criticalViolations = [];
    
    this.init();
  }

  init() {
    console.log('🔒 DATA INTEGRITY: Initializing professional data validation system...');
    
    this.setupValidationRules();
    this.registerDataSources();
    this.startContinuousValidation();
    
    console.log('✅ DATA INTEGRITY: Validation system armed and protecting trading data');
  }

  setupValidationRules() {
    // Price data validation
    this.validationRules.set('price', {
      validate: (data) => {
        if (typeof data !== 'number' || isNaN(data) || !isFinite(data)) {
          return { valid: false, reason: 'Price must be a valid number' };
        }
        
        if (data <= 0) {
          return { valid: false, reason: 'Price must be positive' };
        }
        
        if (data > 10000000) {
          return { valid: false, reason: 'Price exceeds reasonable maximum' };
        }
        
        // Check for suspicious round numbers that might indicate dummy data
        if (data === 120000 || data === 100000 || data === 50000) {
          return { valid: false, reason: 'Suspicious round number - possible dummy data' };
        }
        
        return { valid: true };
      },
      critical: true
    });

    // Indicator data validation
    this.validationRules.set('indicator', {
      validate: (data, indicatorType) => {
        if (!data || typeof data !== 'object') {
          return { valid: false, reason: 'Indicator data must be an object' };
        }
        
        if (!data.value && data.value !== 0) {
          return { valid: false, reason: 'Indicator must have a value property' };
        }
        
        // Validate indicator-specific ranges
        switch (indicatorType) {
          case 'rsi':
          case 'stochRsi':
            if (data.value < 0 || data.value > 100) {
              return { valid: false, reason: `${indicatorType} value must be between 0-100` };
            }
            break;
          case 'williamsR':
            if (data.value < -100 || data.value > 0) {
              return { valid: false, reason: 'Williams %R value must be between -100 and 0' };
            }
            break;
        }
        
        return { valid: true };
      },
      critical: true
    });

    // WebSocket data validation
    this.validationRules.set('websocket', {
      validate: (data) => {
        if (!data || typeof data !== 'object') {
          return { valid: false, reason: 'WebSocket data must be an object' };
        }
        
        if (!data.type) {
          return { valid: false, reason: 'WebSocket data must have a type property' };
        }
        
        if (!data.timestamp || typeof data.timestamp !== 'number') {
          return { valid: false, reason: 'WebSocket data must have a valid timestamp' };
        }
        
        // Check timestamp is recent (within last 5 minutes)
        const now = Date.now();
        if (Math.abs(now - data.timestamp) > 300000) {
          return { valid: false, reason: 'WebSocket data timestamp is too old' };
        }
        
        return { valid: true };
      },
      critical: true
    });

    // Historical data validation
    this.validationRules.set('historical', {
      validate: (data) => {
        if (!Array.isArray(data)) {
          return { valid: false, reason: 'Historical data must be an array' };
        }
        
        if (data.length === 0) {
          return { valid: false, reason: 'Historical data cannot be empty' };
        }
        
        // Check for minimum required data points
        if (data.length < 50) {
          return { valid: false, reason: 'Insufficient historical data for reliable analysis' };
        }
        
        // Validate data structure
        for (let i = 0; i < Math.min(data.length, 10); i++) {
          const candle = data[i];
          if (!candle || typeof candle !== 'object') {
            return { valid: false, reason: 'Invalid candle data structure' };
          }
          
          const requiredFields = ['timestamp', 'open', 'high', 'low', 'close', 'volume'];
          for (const field of requiredFields) {
            if (candle[field] === undefined || candle[field] === null) {
              return { valid: false, reason: `Missing required field: ${field}` };
            }
          }
        }
        
        return { valid: true };
      },
      critical: true
    });
  }

  registerDataSources() {
    // Register legitimate data sources
    this.dataSourceRegistry.set('kraken_websocket', {
      type: 'real_time',
      trusted: true,
      description: 'Kraken WebSocket API'
    });
    
    this.dataSourceRegistry.set('kraken_rest', {
      type: 'historical',
      trusted: true,
      description: 'Kraken REST API'
    });
    
    this.dataSourceRegistry.set('csv_files', {
      type: 'historical',
      trusted: true,
      description: 'Local CSV historical data files'
    });
  }

  validateData(data, type, source = 'unknown') {
    const validation = {
      timestamp: Date.now(),
      type,
      source,
      valid: false,
      reason: '',
      critical: false
    };

    // Check if source is registered and trusted
    if (!this.dataSourceRegistry.has(source)) {
      validation.reason = `Unregistered data source: ${source}`;
      validation.critical = true;
      this.logViolation(validation);
      return validation;
    }

    // Apply validation rules
    const rule = this.validationRules.get(type);
    if (!rule) {
      validation.reason = `No validation rule for data type: ${type}`;
      validation.critical = true;
      this.logViolation(validation);
      return validation;
    }

    const result = rule.validate(data, type);
    validation.valid = result.valid;
    validation.reason = result.reason || '';
    validation.critical = rule.critical && !result.valid;

    // Log validation result
    this.validationHistory.push(validation);
    
    // Trim history if too large
    if (this.validationHistory.length > 1000) {
      this.validationHistory.shift();
    }

    // Handle critical violations
    if (validation.critical) {
      this.logViolation(validation);
    }

    return validation;
  }

  logViolation(validation) {
    this.criticalViolations.push(validation);
    
    console.error('🚨 DATA INTEGRITY VIOLATION:', {
      type: validation.type,
      source: validation.source,
      reason: validation.reason,
      timestamp: new Date(validation.timestamp).toISOString()
    });

    // Trigger professional error handler if available
    if (window.professionalErrorHandler) {
      window.professionalErrorHandler.handleError({
        type: 'DATA_INTEGRITY_VIOLATION',
        message: `Data integrity violation: ${validation.reason}`,
        category: 'TRADING_INTEGRITY',
        source: validation.source,
        dataType: validation.type,
        timestamp: validation.timestamp
      });
    }

    // If too many violations, take protective action
    if (this.criticalViolations.length >= 5) {
      this.triggerProtectiveMode();
    }
  }

  triggerProtectiveMode() {
    console.error('🛡️ DATA INTEGRITY: PROTECTIVE MODE ACTIVATED - Multiple violations detected');
    
    // Disable trading operations
    window.tradingDisabled = true;
    
    // Show critical warning to user
    const warning = document.createElement('div');
    warning.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 0, 0, 0.9);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 99999;
        font-family: 'Segoe UI', sans-serif;
        text-align: center;
      ">
        <h1>⚠️ DATA INTEGRITY PROTECTION ACTIVATED</h1>
        <p style="font-size: 18px; max-width: 600px; margin: 20px;">
          Multiple data integrity violations have been detected. 
          Trading operations have been disabled to protect against potentially corrupted data.
        </p>
        <p style="font-size: 16px; margin: 20px;">
          Please refresh the page and verify your data connections.
        </p>
        <button onclick="window.location.reload()" style="
          background: white;
          color: red;
          border: none;
          padding: 15px 30px;
          font-size: 16px;
          border-radius: 8px;
          cursor: pointer;
          margin-top: 20px;
        ">Reload Application</button>
      </div>
    `;
    
    document.body.appendChild(warning);
  }

  startContinuousValidation() {
    // Validate current price every 5 seconds
    setInterval(() => {
      if (window.currentPrice !== undefined) {
        this.validateData(window.currentPrice, 'price', 'websocket');
      }
    }, 5000);

    // Validate indicator data when it updates
    if (window.indicatorsData) {
      const originalIndicatorsData = window.indicatorsData;
      Object.defineProperty(window, 'indicatorsData', {
        get: () => originalIndicatorsData,
        set: (newData) => {
          if (newData && typeof newData === 'object') {
            Object.keys(newData).forEach(timeframe => {
              if (newData[timeframe] && typeof newData[timeframe] === 'object') {
                Object.keys(newData[timeframe]).forEach(indicator => {
                  this.validateData(newData[timeframe][indicator], 'indicator', 'websocket');
                });
              }
            });
          }
          originalIndicatorsData = newData;
        }
      });
    }
  }

  // Public API
  getValidationHistory() {
    return this.validationHistory;
  }

  getCriticalViolations() {
    return this.criticalViolations;
  }

  getValidationStats() {
    const total = this.validationHistory.length;
    const valid = this.validationHistory.filter(v => v.valid).length;
    const invalid = total - valid;
    const critical = this.criticalViolations.length;

    return {
      total,
      valid,
      invalid,
      critical,
      successRate: total > 0 ? (valid / total * 100).toFixed(2) : 0
    };
  }

  clearViolations() {
    this.criticalViolations = [];
    console.log('🔒 DATA INTEGRITY: Violations cleared');
  }

  addCustomValidationRule(name, rule) {
    this.validationRules.set(name, rule);
    console.log(`🔒 DATA INTEGRITY: Added custom validation rule: ${name}`);
  }

  registerDataSource(name, config) {
    this.dataSourceRegistry.set(name, config);
    console.log(`🔒 DATA INTEGRITY: Registered data source: ${name}`);
  }
}

// Initialize data integrity validator
window.dataIntegrityValidator = new DataIntegrityValidator();

// Export for global access
window.DataIntegrityValidator = DataIntegrityValidator;

console.log('🔒 DATA INTEGRITY: Professional data validation system active');
