// emergency-fix.js
// This file is designed to break any recursive calls or stack overflow conditions
// by intercepting and completely replacing problematic functions

(function () {
  'use strict'

  console.log('🚨 EMERGENCY FIX LOADED 🚨')

  // Store original console methods to allow selective error suppression
  const originalConsoleError = console.error
  const originalConsoleWarn = console.warn

  // Replace console.error to suppress stack overflow errors
  console.error = function (...args) {
    // Check if this is a maximum call stack error
    if (args[0] && typeof args[0] === 'string' &&
        (args[0].includes('Maximum call stack size exceeded') ||
         args[0].includes('stack overflow'))) {
      // Log it once but don't repeat
      if (!window._stackErrorLogged) {
        window._stackErrorLogged = true
        originalConsoleError.call(console, '⚠️ Stack errors being suppressed to prevent browser crash')
      }
      return
    }

    // Pass through all other errors
    originalConsoleError.apply(console, args)
  }

  // Replace all recursive-prone functions with safe versions

  // 1. Signal Light Updates - Complete replacement
  window.updateAllSignalLights = function () {
    // Don't call if already updating
    if (window._isUpdatingSignalLights) {
      console.log('Already updating signal lights, skipping')
      return
    }

    try {
      // Use flags to prevent recursion
      window._isUpdatingSignalLights = true

      // Only log first update
      if (!window._signalLightsInitialized) {
        console.log('Initializing signal lights (safe mode)')
        window._signalLightsInitialized = true
      }

      // Use requestAnimationFrame to break call stack
      requestAnimationFrame(() => {
        try {
          // Initialize lights with neutral state
          if (!window.indicatorsData) {
            console.log('No indicator data available')
            const signals = document.querySelectorAll('.signal-circle')
            signals.forEach(signal => {
              signal.className = 'signal-circle grey-light neutral'
            })
            return
          }

          // Update light colors based on indicator data
          const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
          const INDICATORS = ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'adx', 'bollingerBands', 'atr', 'vwap']

          // Process each indicator-timeframe pair
          TIMEFRAMES.forEach(tf => {
            if (!window.indicatorsData[tf]) return

            INDICATORS.forEach(ind => {
              if (!window.indicatorsData[tf][ind]) return

              // Find signal light element
              const circle = document.querySelector(`.signal-circle[data-ind="${ind}"][data-tf="${tf}"]`)
              if (!circle) return

              // Get signal state
              const state = window.indicatorsData[tf][ind]
              if (!state) return

              // Map state to CSS classes
              let cssClass = 'grey-light neutral'

              if (state.signalClass === 'degen-buy') cssClass = 'green-light degen-buy'
              else if (state.signalClass === 'mild-buy') cssClass = 'blue-light mild-buy'
              else if (state.signalClass === 'mild-sell') cssClass = 'orange-light mild-sell'
              else if (state.signalClass === 'degen-sell') cssClass = 'red-light degen-sell'

              // Update element
              circle.className = `signal-circle ${cssClass}`

              // Set tooltip if available
              if (state.tooltip) {
                circle.setAttribute('data-tooltip', state.tooltip)
              }
            })
          })

          console.log('Signal lights updated safely')
        } catch (e) {
          console.log('Error updating signal lights:', e.message)
        } finally {
          // Always reset flag
          window._isUpdatingSignalLights = false
        }
      })
    } catch (e) {
      console.log('Fatal error in signal light update:', e.message)
      window._isUpdatingSignalLights = false
    }
  }

  // 2. WebSocket Message Processing - Safety wrapper
  const originalWSMessageHandler = window.processWebSocketMessage || function () {}

  window.processWebSocketMessage = function (data) {
    try {
      // Create safe copy to break references
      let safeCopy = null

      try {
        safeCopy = JSON.parse(JSON.stringify(data))
      } catch (e) {
        console.log('Could not create safe copy of WebSocket message')
        return // Abandon processing
      }

      // Don't use the original handler - directly process the message
      if (safeCopy && safeCopy.type) {
        switch (safeCopy.type) {
          case 'indicators':
            // Safely update indicators
            if (safeCopy.indicators) {
              window.indicatorsData = safeCopy.indicators

              // Use setTimeout to break call stack
              setTimeout(() => {
                if (typeof window.updateAllSignalLights === 'function') {
                  window.updateAllSignalLights()
                }
              }, 100)
            }
            break

          case 'price':
            // Safely update price
            if (safeCopy.price && !isNaN(parseFloat(safeCopy.price))) {
              window.currentPrice = parseFloat(safeCopy.price)

              if (typeof window.updatePriceDisplay === 'function') {
                setTimeout(() => window.updatePriceDisplay(window.currentPrice), 10)
              }
            }
            break

          case 'strategy_update':
            // Safely update strategy
            if (safeCopy.strategy) {
              window.currentStrategy = safeCopy.strategy

              if (typeof window.updateStrategyDisplay === 'function') {
                setTimeout(() => window.updateStrategyDisplay(safeCopy.strategy), 10)
              }
            }
            break

          case 'pair_update':
            // Safely update pair
            if (safeCopy.pair) {
              window.currentPair = safeCopy.pair

              if (typeof window.updatePairDisplay === 'function') {
                setTimeout(() => window.updatePairDisplay(safeCopy.pair), 10)
              }
            }
            break
        }
      }
    } catch (e) {
      console.log('Emergency handler caught error:', e.message)
    }
  }

  // Initialize with safe values
  window.indicatorsData = window.indicatorsData || {}
  window.currentStrategy = window.currentStrategy || 'admiral_toa'
  window.currentPair = window.currentPair || 'BTC/USD'

  // Alert that emergency fix is active
  const alertDiv = document.createElement('div')
  alertDiv.style.position = 'fixed'
  alertDiv.style.bottom = '10px'
  alertDiv.style.right = '10px'
  alertDiv.style.backgroundColor = 'rgba(255, 0, 0, 0.8)'
  alertDiv.style.color = 'white'
  alertDiv.style.padding = '10px'
  alertDiv.style.borderRadius = '5px'
  alertDiv.style.zIndex = '9999'
  alertDiv.style.fontSize = '12px'
  alertDiv.innerHTML = 'Emergency fix active - Stack errors suppressed'

  // Add to DOM when it's ready
  if (document.body) {
    document.body.appendChild(alertDiv)
  } else {
    window.addEventListener('DOMContentLoaded', () => {
      document.body.appendChild(alertDiv)
    })
  }

  // Auto-close alert after 10 seconds
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv)
    }
  }, 10000)

  console.log('🚨 EMERGENCY FIX COMPLETED 🚨')
})()
