/* Strategy-specific styles for StarCrypt */

/* Strategy Selector Container */
.strategy-selector-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  max-width: 100%;
}

/* Strategy Select Dropdown */
#mainStrategySelector {
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #444;
  background-color: #2a2a2a;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23e0e0e0%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat, repeat;
  background-position: right 0.7em top 50%, 0 0;
  background-size: 0.65em auto, 100%;
  padding-right: 2.5em;
}

#mainStrategySelector:hover {
  border-color: #666;
  background-color: #333;
}

#mainStrategySelector:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

/* Apply Button */
#applyStrategyBtn {
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

#applyStrategyBtn:hover {
  background-color: #3a7bc8;
  transform: translateY(-1px);
}

#applyStrategyBtn:active {
  transform: translateY(0);
  background-color: #2d6cb0;
}

#applyStrategyBtn:disabled {
  background-color: #555;
  cursor: not-allowed;
  transform: none;
  opacity: 0.7;
}

/* Status Message */
.status {
  padding: 10px 15px;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 13px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  height: 0;
  padding: 0 15px;
  overflow: hidden;
}

.status.visible {
  opacity: 1;
  transform: translateY(0);
  height: auto;
  padding: 10px 15px;
  margin-top: 10px;
}

.status.success {
  background-color: rgba(46, 204, 113, 0.15);
  border-left: 3px solid #2ecc71;
  color: #2ecc71;
}

.status.error {
  background-color: rgba(231, 76, 60, 0.15);
  border-left: 3px solid #e74c3c;
  color: #e74c3c;
}

.status.info {
  background-color: rgba(52, 152, 219, 0.15);
  border-left: 3px solid #3498db;
  color: #3498db;
}

/* Strategy Info Panel */
#strategyInfo {
  padding: 15px;
  margin: 15px 0;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
  border-left: 3px solid #4a90e2;
  transition: all 0.3s ease;
}

#strategyInfo h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #e0e0e0;
  font-size: 16px;
}

#strategyInfo p {
  margin: 5px 0;
  color: #b0b0b0;
  font-size: 13px;
  line-height: 1.5;
}

/* Signal Matrix Styles */
#signalMatrixContainer {
  min-height: 300px;
  transition: all 0.3s ease;
  overflow: hidden;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.1);
}

.signal-row {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.signal-row.header {
  background-color: rgba(0, 0, 0, 0.2);
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  font-size: 11px;
  letter-spacing: 0.5px;
}

.signal-label {
  padding: 12px 15px;
  min-width: 120px;
  font-weight: 500;
  color: #e0e0e0;
  display: flex;
  align-items: center;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.signal-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 5px;
  min-width: 60px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.signal-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #444;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

/* Signal States */
.signal-circle.strong-buy {
  background-color: #00c853;
  box-shadow: 0 0 10px rgba(0, 200, 83, 0.5);
}

.signal-circle.mild-buy {
  background-color: #64dd17;
  box-shadow: 0 0 8px rgba(100, 221, 23, 0.4);
}

.signal-circle.neutral {
  background-color: #9e9e9e;
}

.signal-circle.mild-sell {
  background-color: #ff9100;
  box-shadow: 0 0 8px rgba(255, 145, 0, 0.4);
}

.signal-circle.strong-sell {
  background-color: #ff3d00;
  box-shadow: 0 0 10px rgba(255, 61, 0, 0.5);
}

/* Tooltip styles */
.signal-circle::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-5px);
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  pointer-events: none;
}

.signal-circle:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-10px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .strategy-selector-container {
    flex-direction: column;
  }
  
  #mainStrategySelector, #applyStrategyBtn {
    width: 100%;
  }
  
  .signal-label {
    min-width: 100px;
    font-size: 12px;
    padding: 10px 8px;
  }
  
  .signal-cell {
    min-width: 40px;
    padding: 5px 3px;
  }
  
  .signal-circle {
    width: 16px;
    height: 16px;
  }
}

/* Animation for signal updates */
@keyframes pulse-update {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.signal-updated {
  animation: pulse-update 0.5s ease-out;
}
