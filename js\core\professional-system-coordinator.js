/**
 * StarCrypt Professional System Coordinator
 * Orchestrates all professional systems for enterprise-grade operation
 * Ensures system integrity, data quality, and trading reliability
 */

class ProfessionalSystemCoordinator {
  constructor() {
    this.systems = new Map();
    this.systemStatus = new Map();
    this.initializationOrder = [
      'professionalErrorHandler',
      'dataIntegrityValidator', 
      'professionalWebSocketManager',
      'professionalCodeStandards'
    ];
    
    this.healthCheckInterval = null;
    this.systemMetrics = {
      uptime: Date.now(),
      totalErrors: 0,
      criticalErrors: 0,
      dataViolations: 0,
      wsReconnections: 0,
      codeViolations: 0
    };
    
    this.init();
  }

  init() {
    console.log('🎯 SYSTEM COORDINATOR: Initializing professional system orchestration...');
    
    this.registerSystems();
    this.startSystemMonitoring();
    this.setupSystemIntegration();
    
    console.log('✅ SYSTEM COORDINATOR: Professional system orchestration active');
  }

  registerSystems() {
    // Register all professional systems
    this.initializationOrder.forEach(systemName => {
      if (window[systemName]) {
        this.systems.set(systemName, window[systemName]);
        this.systemStatus.set(systemName, 'ACTIVE');
        console.log(`🎯 SYSTEM COORDINATOR: Registered ${systemName}`);
      } else {
        this.systemStatus.set(systemName, 'MISSING');
        console.warn(`⚠️ SYSTEM COORDINATOR: System ${systemName} not found`);
      }
    });
  }

  startSystemMonitoring() {
    // DISABLED - Was causing excessive system load
    // Continuous monitoring disabled to prevent performance issues
    console.log('🎯 SYSTEM COORDINATOR: Continuous monitoring disabled to prevent system overload');

    // Only perform initial health check
    setTimeout(() => {
      console.log('🎯 SYSTEM COORDINATOR: Performing one-time health check...');
    }, 5000);
  }

  performSystemHealthCheck() {
    console.log('🎯 SYSTEM COORDINATOR: Performing comprehensive system health check...');
    
    const healthReport = {
      timestamp: Date.now(),
      overallHealth: 'HEALTHY',
      systems: {},
      metrics: this.collectSystemMetrics(),
      recommendations: []
    };

    // Check each system
    this.systems.forEach((system, name) => {
      const systemHealth = this.checkSystemHealth(name, system);
      healthReport.systems[name] = systemHealth;
      
      if (systemHealth.status !== 'HEALTHY') {
        healthReport.overallHealth = 'DEGRADED';
      }
    });

    // Generate recommendations
    healthReport.recommendations = this.generateHealthRecommendations(healthReport);

    // Log health status
    this.logHealthReport(healthReport);

    // Take corrective action if needed
    if (healthReport.overallHealth === 'DEGRADED') {
      this.handleSystemDegradation(healthReport);
    }

    return healthReport;
  }

  checkSystemHealth(name, system) {
    const health = {
      name,
      status: 'HEALTHY',
      issues: [],
      metrics: {}
    };

    try {
      switch (name) {
        case 'professionalErrorHandler':
          health.metrics = system.getErrorStats();
          if (health.metrics.critical > 5) {
            health.status = 'DEGRADED';
            health.issues.push('High critical error count');
          }
          break;

        case 'dataIntegrityValidator':
          health.metrics = system.getValidationStats();
          if (health.metrics.critical > 3) {
            health.status = 'CRITICAL';
            health.issues.push('Critical data integrity violations');
          }
          break;

        case 'professionalWebSocketManager':
          health.metrics = system.getConnectionMetrics();
          if (!system.isConnected()) {
            health.status = 'CRITICAL';
            health.issues.push('WebSocket disconnected');
          } else if (health.metrics.reconnectAttempts > 3) {
            health.status = 'DEGRADED';
            health.issues.push('Frequent reconnections');
          }
          break;

        case 'professionalCodeStandards':
          health.metrics = system.getQualityMetrics();
          if (health.metrics.maintainabilityScore < 70) {
            health.status = 'DEGRADED';
            health.issues.push('Low code maintainability score');
          }
          break;
      }
    } catch (error) {
      health.status = 'ERROR';
      health.issues.push(`Health check failed: ${error.message}`);
    }

    return health;
  }

  collectSystemMetrics() {
    const metrics = { ...this.systemMetrics };
    
    // Update uptime
    metrics.uptime = Date.now() - metrics.uptime;

    // Collect metrics from each system
    this.systems.forEach((system, name) => {
      try {
        switch (name) {
          case 'professionalErrorHandler':
            const errorStats = system.getErrorStats();
            metrics.totalErrors = errorStats.total;
            metrics.criticalErrors = errorStats.critical;
            break;

          case 'dataIntegrityValidator':
            const validationStats = system.getValidationStats();
            metrics.dataViolations = validationStats.critical;
            break;

          case 'professionalWebSocketManager':
            const wsMetrics = system.getConnectionMetrics();
            metrics.wsReconnections = wsMetrics.reconnectCount;
            break;

          case 'professionalCodeStandards':
            const codeMetrics = system.getQualityMetrics();
            metrics.codeViolations = codeMetrics.codeSmellsDetected;
            break;
        }
      } catch (error) {
        console.warn(`Failed to collect metrics from ${name}:`, error);
      }
    });

    return metrics;
  }

  generateHealthRecommendations(healthReport) {
    const recommendations = [];

    // Check overall system health
    if (healthReport.metrics.criticalErrors > 10) {
      recommendations.push('Consider system restart due to high error count');
    }

    if (healthReport.metrics.dataViolations > 5) {
      recommendations.push('Review data sources for integrity issues');
    }

    if (healthReport.metrics.wsReconnections > 5) {
      recommendations.push('Check network connectivity and WebSocket stability');
    }

    // Check individual systems
    Object.values(healthReport.systems).forEach(system => {
      if (system.status === 'CRITICAL') {
        recommendations.push(`Immediate attention required for ${system.name}`);
      }
    });

    return recommendations;
  }

  logHealthReport(healthReport) {
    const healthEmoji = healthReport.overallHealth === 'HEALTHY' ? '✅' : 
                       healthReport.overallHealth === 'DEGRADED' ? '⚠️' : '🚨';
    
    console.log(`${healthEmoji} SYSTEM HEALTH: ${healthReport.overallHealth}`);
    
    if (healthReport.overallHealth !== 'HEALTHY') {
      console.table(healthReport.systems);
      
      if (healthReport.recommendations.length > 0) {
        console.warn('🎯 RECOMMENDATIONS:', healthReport.recommendations);
      }
    }
  }

  handleSystemDegradation(healthReport) {
    console.warn('🎯 SYSTEM COORDINATOR: Handling system degradation...');

    // Attempt automatic recovery
    Object.entries(healthReport.systems).forEach(([name, health]) => {
      if (health.status === 'CRITICAL') {
        this.attemptSystemRecovery(name, health);
      }
    });

    // Notify user if critical issues persist
    const criticalSystems = Object.values(healthReport.systems)
      .filter(s => s.status === 'CRITICAL');
    
    if (criticalSystems.length > 0) {
      this.notifyUserOfCriticalIssues(criticalSystems);
    }
  }

  attemptSystemRecovery(systemName, health) {
    console.log(`🛠️ SYSTEM COORDINATOR: Attempting recovery for ${systemName}...`);

    try {
      switch (systemName) {
        case 'professionalWebSocketManager':
          if (this.systems.has(systemName)) {
            this.systems.get(systemName).reconnect();
          }
          break;

        case 'dataIntegrityValidator':
          if (this.systems.has(systemName)) {
            this.systems.get(systemName).clearViolations();
          }
          break;

        case 'professionalErrorHandler':
          if (this.systems.has(systemName)) {
            this.systems.get(systemName).clearErrorLog();
          }
          break;
      }
    } catch (error) {
      console.error(`Failed to recover ${systemName}:`, error);
    }
  }

  notifyUserOfCriticalIssues(criticalSystems) {
    const systemNames = criticalSystems.map(s => s.name).join(', ');
    
    const notification = document.createElement('div');
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(255, 68, 68, 0.6);
        z-index: 99999;
        max-width: 500px;
        font-family: 'Segoe UI', sans-serif;
        text-align: center;
      ">
        <h2 style="margin: 0 0 15px 0;">🚨 Critical System Issues</h2>
        <p style="margin: 0 0 20px 0; font-size: 16px;">
          Critical issues detected in: ${systemNames}
        </p>
        <p style="margin: 0 0 20px 0; font-size: 14px;">
          System reliability may be compromised. Consider refreshing the application.
        </p>
        <div style="display: flex; gap: 10px; justify-content: center;">
          <button onclick="window.location.reload()" style="
            background: white;
            color: #cc0000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
          ">Refresh Application</button>
          <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
          ">Continue Anyway</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);
  }

  setupSystemIntegration() {
    // Integrate systems for optimal cooperation
    this.setupErrorHandlerIntegration();
    this.setupDataValidatorIntegration();
    this.setupWebSocketIntegration();
  }

  setupErrorHandlerIntegration() {
    // DISABLED - Was causing recursive error loops
    // Error handler integration disabled to prevent infinite recursion
    console.log('🎯 SYSTEM COORDINATOR: Error handler integration disabled to prevent recursion');
  }

  setupDataValidatorIntegration() {
    // Enhance data validator with system-wide context
    if (this.systems.has('dataIntegrityValidator')) {
      const validator = this.systems.get('dataIntegrityValidator');
      
      // Add system coordinator to violation handling
      const originalLogViolation = validator.logViolation.bind(validator);
      validator.logViolation = (validation) => {
        // Add system context to violations
        validation.systemContext = this.getSystemHealthSummary();
        return originalLogViolation(validation);
      };
    }
  }

  setupWebSocketIntegration() {
    // Enhance WebSocket manager with system coordination
    if (this.systems.has('professionalWebSocketManager')) {
      const wsManager = this.systems.get('professionalWebSocketManager');
      
      // Monitor connection events
      wsManager.on('connected', () => {
        console.log('🎯 SYSTEM COORDINATOR: WebSocket connected - system health improved');
      });
      
      wsManager.on('disconnected', () => {
        console.warn('🎯 SYSTEM COORDINATOR: WebSocket disconnected - system health degraded');
      });
    }
  }

  getSystemHealthSummary() {
    const summary = {
      overallHealth: 'HEALTHY',
      activeSystemsCount: 0,
      criticalIssuesCount: 0
    };

    this.systemStatus.forEach((status, name) => {
      if (status === 'ACTIVE') {
        summary.activeSystemsCount++;
      } else if (status === 'CRITICAL') {
        summary.criticalIssuesCount++;
        summary.overallHealth = 'DEGRADED';
      }
    });

    return summary;
  }

  // Public API
  getSystemStatus() {
    return Object.fromEntries(this.systemStatus);
  }

  getSystemMetrics() {
    return this.collectSystemMetrics();
  }

  getHealthReport() {
    return this.performSystemHealthCheck();
  }

  shutdown() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    console.log('🎯 SYSTEM COORDINATOR: Professional system coordination shutdown');
  }
}

// Initialize professional system coordinator
window.professionalSystemCoordinator = new ProfessionalSystemCoordinator();

// Export for global access
window.ProfessionalSystemCoordinator = ProfessionalSystemCoordinator;

console.log('🎯 SYSTEM COORDINATOR: Professional system orchestration active');
