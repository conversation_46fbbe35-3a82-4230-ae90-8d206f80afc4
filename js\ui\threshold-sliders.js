/**
 * Threshold Sliders for StarCrypt
 * Provides threshold slider controls for indicator values
 */

(function () {
  // Flags to prevent recursive updates
  window.isUpdatingSlidersProgrammatically = false;
  window.isUpdatingSignalLightsFromThresholds = false;

  // Initialize default thresholds if not already set
  if (!window.defaultThresholds) {
    window.defaultThresholds = {
      rsi: { green: 20, blue: 40, orange: 60, red: 80 },
      stochRsi: { green: 20, blue: 40, orange: 60, red: 80 },
      macd: { green: 15, blue: 35, orange: 65, red: 85 },
      bollingerBands: { green: 5, blue: 25, orange: 75, red: 95 },
      atr: { green: 10, blue: 30, orange: 70, red: 90 },
      volume: { green: 20, blue: 40, orange: 60, red: 80 },
      mfi: { green: 20, blue: 40, orange: 60, red: 80 },
      williamsR: { green: 80, blue: 60, orange: 40, red: 20 },
      cci: { green: 15, blue: 35, orange: 65, red: 85 },
      ultimateOscillator: { green: 20, blue: 40, orange: 60, red: 80 },
      adx: { green: 15, blue: 35, orange: 65, red: 85 },
      sentiment: { green: 20, blue: 40, orange: 60, red: 80 },
      entropy: { green: 20, blue: 40, orange: 60, red: 80 },
      correlation: { green: 20, blue: 40, orange: 60, red: 80 },
      time_anomaly: { green: 20, blue: 40, orange: 60, red: 80 }
    };
  }

  // Initialize thresholds from localStorage or defaults
  if (!window.thresholds) {
    const saved = localStorage.getItem('userThresholds');
    if (saved) {
      try {
        window.thresholds = JSON.parse(saved);
      } catch (e) {
        window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      }
    } else {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
    }
  }

  // Update the visual representation of threshold segments for a single slider
  function updateThresholdDisplay(sliderContainer) {
    try {
      const indicatorName = sliderContainer.dataset.indicator;
      const thresholds = window.thresholds[indicatorName];
      
      if (!thresholds) return;

      // Get the segment elements
      const greenSegment = sliderContainer.querySelector('.green-segment');
      const blueSegment = sliderContainer.querySelector('.blue-segment');
      const graySegment = sliderContainer.querySelector('.gray-segment');
      const orangeSegment = sliderContainer.querySelector('.orange-segment');
      const redSegment = sliderContainer.querySelector('.red-segment');

      // Calculate the widths of each segment
      greenSegment.style.width = `${thresholds.green}%`;
      blueSegment.style.width = `${thresholds.blue - thresholds.green}%`;
      graySegment.style.width = `${thresholds.orange - thresholds.blue}%`;
      orangeSegment.style.width = `${thresholds.red - thresholds.orange}%`;
      redSegment.style.width = `${100 - thresholds.red}%`;
      
      // Set left positions
      blueSegment.style.left = `${thresholds.green}%`;
      graySegment.style.left = `${thresholds.blue}%`;
      orangeSegment.style.left = `${thresholds.orange}%`;
      redSegment.style.left = `${thresholds.red}%`;
    } catch (error) {
      console.error('Error updating threshold display:', error);
    }
  }

  // Update all threshold displays
  function updateAllThresholdDisplays() {
    const sliderContainers = document.querySelectorAll('.slider-container');
    sliderContainers.forEach(updateThresholdDisplay);
  }

  // Placeholder for renderThresholdSliders function (will be added by enhanced-threshold-sliders.js)
  function renderThresholdSliders(strategy) {
    console.log('[ThresholdSliders] renderThresholdSliders called with strategy:', strategy);
    // This will be overridden by enhanced-threshold-sliders.js
  }

  // Export to global scope
  window.renderThresholdSliders = renderThresholdSliders;
  window.updateAllThresholdDisplays = updateAllThresholdDisplays;

  // Initialize and export functions to global scope
  window.thresholdSliders = {
    render: renderThresholdSliders,
    update: updateAllThresholdDisplays,
    reset: function() {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      renderThresholdSliders(window.currentStrategy);
    }
  };

  console.log('[ThresholdSliders] Threshold sliders system initialized');
})();
