/* Base Theme Styles for StarCrypt */
:root {
  /* Dark theme as default */
  --bg-color: #0a0a1a;
  --text-color: #00FFFF;
  --border-color: #333;
  --box-size: 6.5rem;
  --label-width: 8rem;
  --mini-chart-width: 24rem;
  --logger-width: 100%;
  --logger-height: 12rem;
  --secondary-bg: #1a1a2a;
  --highlight-color: #00FF00;
  --animation-intensity: 3;
  
  /* Extended theme variables */
  --bg-primary: #0a0e17;
  --bg-secondary: #121d33;
  --bg-tertiary: #1a2942;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --accent-primary: #00b4d8;
  --accent-secondary: #0077b6;
  --positive: #2ecc71;
  --negative: #e74c3c;
  --neutral: #f39c12;
  --grid-color: rgba(255, 255, 255, 0.05);
  --tooltip-bg: rgba(10, 14, 23, 0.95);
  --tooltip-text: #ffffff;
  --chart-bg: #0a0e17;
  --chart-grid: rgba(255, 255, 255, 0.05);
  --chart-candle-up: #26a69a;
  --chart-candle-down: #ef5350;
  --chart-volume: rgba(0, 180, 216, 0.3);
  --chart-crosshair: rgba(255, 255, 255, 0.5);
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1em;
  color: var(--text-secondary);
}

a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Buttons */
button, .btn {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

button:hover, .btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

button:active, .btn:active {
  transform: translateY(0);
  box-shadow: none;
}

button:disabled, .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Forms */
input, select, textarea {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  width: 100%;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 180, 216, 0.2);
}

/* Cards */
.card {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Status indicators */
.status {
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status.positive { background-color: var(--positive); }
.status.negative { background-color: var(--negative); }
.status.neutral { background-color: var(--neutral); }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-secondary); }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .toolbar {
    padding: 0.5rem;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}
/* Extracted from index.html <style> block. This file contains all StarCrypt core styles, including theme variables, global tooltip, Chart.js tooltips, price display, indicator highlighting, volume status, pulse animations, section headers, logger, signal matrix, and all UI component styles. */

html, body {
    font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
    font-size: 14px;
}

/* ... (all other CSS from the main <style> block in index.html, as seen above, will be extracted here in full) ... */

/* Please ensure this file is referenced in index.html as <link rel="stylesheet" href="css/app-styles.css"> */
/* Loading Overlay Styles */
#loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.3s ease;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--border-color);
  border-top: 5px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

.loading-text {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin-top: 1rem;
  text-align: center;
  max-width: 80%;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: 2px;
  margin-top: 1.5rem;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  width: 0%;
  background-color: var(--accent-primary);
  transition: width 0.3s ease;
  border-radius: 2px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Logo animation */
.logo-container {
  position: relative;
  margin-bottom: 2rem;
}

.logo {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.logo::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  filter: blur(15px);
  z-index: -1;
  opacity: 0.3;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.5; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .loading-text {
    font-size: 1rem;
  }
  
  .logo {
    font-size: 2rem;
  }
}
/* Loading Overlay Styles */
#loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 14, 23, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 5px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1.5rem;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 5px solid transparent;
  border-radius: 50%;
  border-top-color: #00B4D8;
  animation: spin 1.5s ease-in-out infinite reverse;
  filter: blur(1px);
}

.loading-text {
  color: #00FFFF;
  font-size: 1.5rem;
  font-weight: 500;
  margin-top: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-align: center;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation: pulse 2s infinite;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: rgba(0, 180, 216, 0.2);
  border-radius: 2px;
  margin-top: 2rem;
  overflow: hidden;
  position: relative;
}

.loading-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, #00B4D8, #00FFFF);
  transition: width 0.3s ease;
  animation: progressPulse 2s infinite;
}

.loading-details {
  color: #A0A0A0;
  font-size: 0.9rem;
  margin-top: 1rem;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

.loading-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  color: #00FFFF;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.stat-label {
  color: #A0A0A0;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes progressPulse {
  0% { box-shadow: 0 0 5px rgba(0, 180, 216, 0.5); }
  50% { box-shadow: 0 0 20px rgba(0, 180, 216, 0.8); }
  100% { box-shadow: 0 0 5px rgba(0, 180, 216, 0.5); }
}

/* Loading Complete State */
#loading-overlay.fade-out {
  opacity: 0;
  visibility: hidden;
}

/* Loading Error State */
.loading-error {
  color: #FF6B6B;
  text-align: center;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 4px;
  max-width: 80%;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .loading-text {
    font-size: 1.2rem;
  }
  
  .loading-details {
    font-size: 0.8rem;
  }
  
  .loading-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
}
/* AI Dashboard Styles */

/* Loading State */
.ai-dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.ai-dashboard-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 162, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00a2ff;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error State */
.ai-dashboard-error {
  padding: 1.5rem;
  background: rgba(255, 50, 50, 0.1);
  border: 1px solid #ff3232;
  border-radius: 8px;
  color: #ff6b6b;
  margin: 1rem 0;
}

.ai-dashboard-error h3 {
  color: #ff3232;
  margin-top: 0;
}

.ai-dashboard-error button {
  background: #ff3232;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.ai-dashboard-error button:hover {
  background: #ff0000;
}

/* Base styles */
.ai-dashboard {
  --primary-color: #00f2fe;
  --secondary-color: #4facfe;
  --success-color: #00e676;
  --danger-color: #ff5252;
  --warning-color: #ffab00;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --bg-primary: rgba(10, 14, 23, 0.95);
  --bg-secondary: rgba(20, 25, 40, 0.8);
  --border-color: rgba(255, 255, 255, 0.1);
  --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  --transition-speed: 0.3s;
}

/* Dashboard container */
#ai-dashboard-container {
  font-family: 'Segoe UI', Roboto, -apple-system, sans-serif;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 20px;
  max-width: 1200px;
  margin: 20px auto;
  overflow: hidden;
}

/* Dashboard header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

/* Dashboard grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Cards */
.dashboard-card {
  background: var(--bg-secondary);
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--card-shadow);
  transition: transform var(--transition-speed) ease, 
              box-shadow var(--transition-speed) ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Card header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  margin: 10px 0;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Confidence meter */
.confidence-meter {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin: 15px 0;
  overflow: hidden;
}

.confidence-level {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.confidence-high { background: var(--success-color); }
.confidence-medium { background: var(--warning-color); }
.confidence-low { background: var(--danger-color); }

/* Tags */
.tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag-buy { 
  background: rgba(0, 230, 118, 0.2); 
  color: var(--success-color);
  border: 1px solid rgba(0, 230, 118, 0.3);
}

.tag-sell { 
  background: rgba(255, 82, 82, 0.2); 
  color: var(--danger-color);
  border: 1px solid rgba(255, 82, 82, 0.3);
}

.tag-hold { 
  background: rgba(255, 171, 0, 0.2); 
  color: var(--warning-color);
  border: 1px solid rgba(255, 171, 0, 0.3);
}

/* Key levels */
.key-levels {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  background: rgba(0, 0, 0, 0.2);
  padding: 15px;
  border-radius: 8px;
}

.level {
  text-align: center;
  flex: 1;
  padding: 0 10px;
  position: relative;
}

.level:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 60%;
  width: 1px;
  background: var(--border-color);
}

.level-value {
  font-weight: 700;
  font-size: 18px;
  margin: 5px 0;
  color: var(--primary-color);
}

.level-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading state */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .key-levels {
    flex-direction: column;
    gap: 15px;
  }
  
  .level {
    padding: 10px 0;
  }
  
  .level:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
  }
  
  .level:not(:last-child)::after {
    display: none;
  }
}

/* AI Status Indicator */
.ai-status {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}

.ai-status.strong_buy { background-color: var(--success-color); box-shadow: 0 0 10px var(--success-color); }
.ai-status.buy { background-color: var(--success-color); }
.ai-status.hold { background-color: var(--warning-color); }
.ai-status.sell { background-color: var(--danger-color); }
.ai-status.strong_sell { background-color: var(--danger-color); box-shadow: 0 0 10px var(--danger-color); }
/* Animations and overlay styles extracted from index.html */
#loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(10, 10, 26, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  transition: opacity 0.5s ease-in-out;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 5px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #00FFFF;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
  animation: pulse 1.5s infinite alternate;
}

.loading-progress {
  width: 300px;
  height: 10px;
  background-color: rgba(0, 255, 255, 0.2);
  border-radius: 5px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #0088FF, #00FFFF);
  border-radius: 5px;
  transition: width 0.3s ease-in-out;
}

.strategy-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(10, 10, 26, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.strategy-animation-content {
  background: rgba(0, 10, 30, 0.8);
  border: 2px solid #00FFFF;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  max-width: 80%;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.strategy-animation-content h2 {
  color: #00FFFF;
  font-size: 2rem;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
}

.strategy-animation-icon {
  font-size: 3rem;
  margin: 20px 0;
}

.strategy-animation-icon i {
  animation: spin 2s linear infinite;
}

/* Golden Pulse Glow for Active Timeframe */
.golden-pulse-glow {
  position: relative;
  z-index: 2;
  animation: goldenPulse 1.5s infinite ease-in-out;
  box-shadow: 0 0 10px 3px rgba(255, 215, 0, 0.7) !important;
  border: 2px solid #FFD700 !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.strategy-indicators-preview {
  margin: 20px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.indicator-preview {
  padding: 5px 10px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.3);
  font-weight: bold;
}

@keyframes indicatorPulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.strategy-animation-progress {
  width: 100%;
  height: 10px;
  background-color: rgba(0, 255, 255, 0.2);
  border-radius: 5px;
  overflow: hidden;
  margin-top: 20px;
}

.strategy-animation-progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #0088FF, #00FFFF);
  border-radius: 5px;
  transition: width 1.5s ease-in-out;
}

@keyframes goldenPulse {
  0% {
    box-shadow: 0 0 5px 2px rgba(255, 215, 0, 0.7), 0 0 7px 3px rgba(255, 223, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 10px 4px rgba(255, 215, 0, 1), 0 0 15px 6px rgba(255, 223, 0, 0.7);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 15px 5px rgba(255, 215, 0, 1), 0 0 25px 10px rgba(255, 223, 0, 0.7);
    transform: scale(1.05);
  }
}
/* Layout Styles */
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
  font-size: 14px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Chart Container Styles */
.chart-container {
  position: relative;
  flex: 1;
  min-height: 500px;
  width: 100%;
  height: 100%;
  background-color: var(--chart-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin: 1rem 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

#mainChart {
  width: 100%;
  height: 100%;
}

/* Chart Controls */
.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.timeframe-selector {
  display: flex;
  gap: 0.5rem;
}

.timeframe-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.timeframe-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.timeframe-btn.active {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
  border-color: var(--accent-primary);
}

.chart-toolbar {
  display: flex;
  gap: 0.5rem;
}

.chart-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.chart-btn.active {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
  border-color: var(--accent-primary);
}

/* Chart Canvas */
.chart-canvas-container {
  position: relative;
  width: 100%;
  height: calc(100% - 42px); /* Account for controls height */
}

#mainChart {
  width: 100%;
  height: 100%;
}

/* Chart Legend */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 0.85rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .chart-container {
    flex: 1;
    min-height: 500px;
    width: 100%;
    height: 60vh;
    position: relative;
    background: #0a0e17;
    border: 1px solid #1a1f2e;
    border-radius: 4px;
    overflow: hidden;
  }
  
  #mainChart {
    width: 100% !important;
    height: 100% !important;
    display: block;
  }
  
  .timeframe-selector {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
  }
  
  .timeframe-btn {
    flex-shrink: 0;
  }
  
  .chart-legend {
    font-size: 0.8rem;
    gap: 0.5rem;
  }
}
/**
 * Enhanced Light Logic Overlays and Z-Index Fixes for StarCrypt
 * Comprehensive overlay management with proper z-index hierarchy
 */

/* Z-Index Hierarchy Definition */
:root {
  --z-base: 1;
  --z-content: 10;
  --z-signal-lights: 50;
  --z-interactive: 100;
  --z-menus: 1000;
  --z-enhanced-menus: 1100;
  --z-overlays: 2000;
  --z-tooltips: 9999;
  --z-loading: 10000;
}

/* Enhanced Light Logic Menu Positioning */
#lightLogicMenu {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  width: 350px !important;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
  border: 2px solid #00d4ff !important;
  border-radius: 15px !important;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  z-index: var(--z-enhanced-menus) !important;
  padding: 20px !important;
  color: #ffffff !important;
  display: none !important;
  pointer-events: auto !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  /* Prevent covering interactive elements */
  margin-top: 10px !important;
}

#lightLogicMenu.active {
  display: block !important;
  animation: enhancedMenuSlideIn 0.3s ease-out !important;
}

@keyframes enhancedMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Signal Logic Menu Positioning */
#logicMenu {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  width: 500px !important;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
  border: 2px solid #00d4ff !important;
  border-radius: 15px !important;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  z-index: var(--z-enhanced-menus) !important;
  padding: 20px !important;
  color: #ffffff !important;
  display: none !important;
  pointer-events: auto !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  margin-top: 10px !important;
}

#logicMenu.active {
  display: block !important;
  animation: enhancedMenuSlideIn 0.3s ease-out !important;
}

/* Fix Other Menu Z-Index Issues */
#indicatorMenu,
#strategyMenu,
#thresholdsMenu,
#timeframesMenu {
  z-index: var(--z-menus) !important;
  pointer-events: auto !important;
}

/* Enhanced Convergence Overlays */
.convergence-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: var(--z-overlays);
  border-radius: inherit;
  overflow: hidden;
}

.convergence-overlay.active {
  animation: convergenceOverlayPulse 2s infinite;
}

@keyframes convergenceOverlayPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* Enhanced Light Logic Layers */
.light-logic-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: var(--z-signal-lights);
  border-radius: inherit;
  transition: all 0.3s ease;
}

.light-logic-layer.conservative {
  background: radial-gradient(circle, rgba(100, 149, 237, 0.1) 0%, transparent 70%);
  filter: brightness(0.9);
}

.light-logic-layer.wallstreet {
  background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.2);
}

.light-logic-layer.vibeflow {
  background: radial-gradient(circle, rgba(138, 43, 226, 0.1) 0%, transparent 70%);
  animation: vibeflowPulse 3s infinite;
}

@keyframes vibeflowPulse {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  33% { filter: hue-rotate(120deg) brightness(1.2); }
  66% { filter: hue-rotate(240deg) brightness(1.1); }
}

.light-logic-layer.cosmic {
  background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 50%, transparent 70%);
  animation: cosmicRotate 5s linear infinite;
}

@keyframes cosmicRotate {
  from { filter: hue-rotate(0deg); }
  to { filter: hue-rotate(360deg); }
}

.light-logic-layer.chaos {
  background: radial-gradient(circle, rgba(255, 69, 0, 0.1) 0%, transparent 70%);
  animation: chaosFlicker 0.5s infinite;
}

@keyframes chaosFlicker {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.3; }
}

/* Enhanced Helper Step Overlays */
.helper-step-overlay {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  pointer-events: none;
  z-index: var(--z-content);
  border-radius: 10px;
  opacity: 0;
  transition: all 0.3s ease;
}

.helper-step.highlight-active .helper-step-overlay {
  opacity: 1;
  background: linear-gradient(45deg, 
    rgba(0, 212, 255, 0.1) 0%, 
    rgba(0, 255, 136, 0.1) 50%, 
    rgba(255, 165, 2, 0.1) 100%);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  animation: helperStepGlow 2s infinite;
}

@keyframes helperStepGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

/* Enhanced Convergence Indicators */
.enhanced-convergence-indicator {
  position: relative;
  z-index: var(--z-signal-lights);
}

.enhanced-convergence-indicator::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: conic-gradient(
    from 0deg,
    #00d4ff 0deg,
    #00ff88 90deg,
    #ffa502 180deg,
    #ff4757 270deg,
    #00d4ff 360deg
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  animation: convergenceRing 3s linear infinite;
  transition: opacity 0.3s ease;
}

.enhanced-convergence-indicator.active::before {
  opacity: 0.7;
}

@keyframes convergenceRing {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Degenism Settings Overlays */
.degenism-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: var(--z-overlays);
  border-radius: inherit;
  opacity: 0;
  transition: all 0.3s ease;
}

.degenism-overlay.degen-mode {
  opacity: 1;
  background: radial-gradient(circle, 
    rgba(255, 20, 147, 0.1) 0%, 
    rgba(0, 255, 255, 0.1) 50%, 
    transparent 70%);
  animation: degenPulse 1s infinite;
}

@keyframes degenPulse {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
}

/* Structured Light Layers */
.structured-light-container {
  position: relative;
  z-index: var(--z-signal-lights);
}

.light-layer-1 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
  border-radius: inherit;
}

.light-layer-2 {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  pointer-events: none;
  z-index: 2;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.05) 0%, transparent 70%);
  border-radius: inherit;
  animation: lightLayer2Rotate 4s linear infinite;
}

@keyframes lightLayer2Rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.light-layer-3 {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  pointer-events: none;
  z-index: 3;
  background: radial-gradient(circle, rgba(255, 165, 2, 0.05) 0%, transparent 70%);
  border-radius: inherit;
  animation: lightLayer3Pulse 2s infinite;
}

@keyframes lightLayer3Pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Interactive Element Protection */
.interactive-element {
  position: relative;
  z-index: var(--z-interactive) !important;
  pointer-events: auto !important;
}

/* Ensure buttons, sliders, and inputs are always interactive */
button,
input,
select,
.slider,
.threshold-slider,
.enhanced-slider,
.signal-circle,
.menu-button {
  position: relative;
  z-index: var(--z-interactive) !important;
  pointer-events: auto !important;
}

/* Enhanced Tooltip Z-Index */
.custom-tooltip,
.tooltip,
[data-tooltip]::after {
  z-index: var(--z-tooltips) !important;
  pointer-events: none !important;
}

/* Loading Overlay Fix */
#loading-overlay {
  z-index: var(--z-loading) !important;
}

/* Menu Backdrop Enhancement */
.menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: calc(var(--z-menus) - 1);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.menu-backdrop.active {
  opacity: 1;
  pointer-events: auto;
}

/* Enhanced Signal Light Overlays */
.signal-light-overlay {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  pointer-events: none;
  z-index: calc(var(--z-signal-lights) + 1);
  border-radius: inherit;
  opacity: 0;
  transition: all 0.3s ease;
}

.signal-circle:hover .signal-light-overlay {
  opacity: 1;
  background: radial-gradient(circle, currentColor 0%, transparent 70%);
  filter: blur(2px);
}

/* Responsive Overlay Adjustments */
@media (max-width: 768px) {
  #lightLogicMenu,
  #logicMenu {
    width: 90vw !important;
    max-width: 400px !important;
    left: 5vw !important;
    right: auto !important;
  }
  
  .convergence-overlay,
  .light-logic-layer,
  .helper-step-overlay {
    border-radius: 8px;
  }
}

/* Debug Mode for Z-Index Issues */
.debug-z-index * {
  outline: 1px solid rgba(255, 0, 0, 0.3) !important;
  position: relative !important;
}

.debug-z-index *::before {
  content: attr(class) " z:" attr(style);
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  padding: 2px;
  z-index: 99999;
  pointer-events: none;
}

/* Enhanced Timeframe Selector Styles */
.enhanced-timeframes-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.timeframe-presets {
  background: rgba(0, 20, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.timeframe-presets h4 {
  margin: 0 0 10px 0;
  color: #00d4ff;
  font-size: 1rem;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.timeframe-preset-btn {
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.timeframe-preset-btn:hover {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000000;
  transform: translateY(-1px);
}

.active-timeframes-section {
  background: rgba(0, 40, 20, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 255, 136, 0.2);
}

.active-timeframes-section h4 {
  margin: 0 0 10px 0;
  color: #00ff88;
  font-size: 1rem;
}

.active-timeframes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.active-timeframe-item {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid #00ff88;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.85rem;
}

.timeframe-label {
  color: #00ff88;
  font-weight: bold;
}

.timeframe-duration {
  color: #cccccc;
  font-size: 0.75rem;
}

.remove-timeframe-btn {
  background: #ff4757;
  border: none;
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-timeframe-btn:hover {
  background: #ff3742;
}

.available-timeframes-section {
  background: rgba(40, 20, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 165, 2, 0.2);
}

.available-timeframes-section h4 {
  margin: 0 0 15px 0;
  color: #ffa502;
  font-size: 1rem;
}

.timeframe-group {
  margin-bottom: 15px;
}

.timeframe-group h5 {
  margin: 0 0 8px 0;
  color: #ffa502;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(255, 165, 2, 0.2);
  padding-bottom: 4px;
}

.timeframe-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.timeframe-checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.timeframe-checkbox-label:hover {
  background: rgba(255, 165, 2, 0.1);
}

.timeframe-checkbox-label input[type="checkbox"] {
  margin: 0;
}

.timeframe-checkbox-label input[type="checkbox"]:disabled {
  opacity: 0.5;
}

.timeframe-text {
  color: #ffffff;
  font-weight: 500;
}

.timeframe-desc {
  color: #cccccc;
  font-size: 0.75rem;
}

.custom-timeframe-section {
  background: rgba(40, 0, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 0, 255, 0.2);
}

.custom-timeframe-section h4 {
  margin: 0 0 10px 0;
  color: #ff00ff;
  font-size: 1rem;
}

.add-custom-btn {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  border: 1px solid #ff00ff;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.add-custom-btn:hover {
  background: linear-gradient(135deg, #ff00ff, #cc00cc);
  transform: translateY(-1px);
}

.custom-timeframe-dialog {
  margin-top: 15px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 0, 255, 0.3);
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  color: #ff00ff;
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 0, 255, 0.3);
  color: #ffffff;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #ff00ff;
  box-shadow: 0 0 5px rgba(255, 0, 255, 0.3);
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.form-actions button {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  border: 1px solid #ff00ff;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.form-actions button[type="button"] {
  background: linear-gradient(135deg, #555, #666);
  border-color: #999;
}

.live-update-section {
  background: rgba(20, 40, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.live-update-section h4 {
  margin: 0 0 10px 0;
  color: #00ffff;
  font-size: 1rem;
}

.live-update-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 0.85rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

/* Toast Notifications */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast {
  font-family: 'Orbitron', monospace;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-timeframes-container {
    gap: 15px;
  }

  .preset-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .timeframe-checkboxes {
    grid-template-columns: 1fr;
  }

  .active-timeframes-list {
    flex-direction: column;
  }
}
/**
 * Enhanced Signal Logic Styles for StarCrypt
 * Comprehensive styling for enhanced signal logic system
 */

/* Enhanced Signal Logic Menu */
.enhanced-signal-logic-menu {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border: 2px solid #00d4ff;
  border-radius: 15px;
  padding: 20px;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(10px);
  max-width: 500px;
  min-height: 600px;
}

.menu-header {
  border-bottom: 2px solid #00d4ff;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.menu-header h3 {
  margin: 0 0 15px 0;
  color: #00d4ff;
  text-align: center;
  font-size: 1.4em;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Menu Tabs */
.menu-tabs {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.tab-btn {
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Orbitron', monospace;
  font-size: 0.9em;
}

.tab-btn:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: #000000;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
}

/* Tab Content */
.tab-content {
  display: none;
  animation: fadeIn 0.3s ease;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Setting Groups */
.setting-group {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 10px;
}

.setting-group label {
  display: block;
  color: #00d4ff;
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 0.95em;
}

/* Enhanced Sliders */
.slider-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.enhanced-slider {
  flex: 1;
  height: 6px;
  background: linear-gradient(90deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  position: relative;
}

.enhanced-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  transition: all 0.3s ease;
}

.enhanced-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
}

.enhanced-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.slider-value {
  min-width: 60px;
  text-align: center;
  color: #00d4ff;
  font-weight: bold;
  background: rgba(0, 212, 255, 0.1);
  padding: 5px 10px;
  border-radius: 5px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

/* Enhanced Select */
.enhanced-select {
  width: 100%;
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 10px;
  border-radius: 8px;
  font-family: 'Orbitron', monospace;
  cursor: pointer;
  transition: all 0.3s ease;
}

.enhanced-select:hover,
.enhanced-select:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  outline: none;
}

/* Weight Controls */
.weight-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.weight-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 5px;
}

.weight-item input[type="number"] {
  width: 80px;
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 5px;
  border-radius: 5px;
  font-family: 'Orbitron', monospace;
  text-align: center;
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.checkbox-item:hover {
  background: rgba(0, 212, 255, 0.1);
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #00d4ff;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  border-color: #00d4ff;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000000;
  font-weight: bold;
  font-size: 14px;
}

/* History Controls */
.history-controls {
  display: flex;
  gap: 10px;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: #000000;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 25px rgba(0, 212, 255, 0.5);
}

.btn-secondary {
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  color: #ffffff;
  border: 1px solid #00d4ff;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: #000000;
  transform: translateY(-2px);
}

.btn-danger {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: #ffffff;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(255, 71, 87, 0.4);
}

/* Menu Footer */
.menu-footer {
  border-top: 2px solid #00d4ff;
  padding-top: 15px;
  margin-top: 20px;
}

.status-indicators {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  flex: 1;
  margin: 0 5px;
}

.status-label {
  font-size: 0.8em;
  color: #00d4ff;
  margin-bottom: 5px;
}

.convergence-strength,
.confidence-level {
  font-size: 1.2em;
  font-weight: bold;
  color: #ffffff;
}

.convergence-strength.buy,
.confidence-level.buy {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.convergence-strength.sell,
.confidence-level.sell {
  color: #ff4757;
  text-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.convergence-strength.neutral,
.confidence-level.neutral {
  color: #ffa502;
  text-shadow: 0 0 10px rgba(255, 165, 2, 0.5);
}

.menu-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* Signal Logic Visual Effects */
.light-logic-conservative .signal-circle {
  transition: all 0.2s ease;
  filter: brightness(0.9);
}

.light-logic-wallstreet .signal-circle {
  transition: all 0.3s ease;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.light-logic-vibeflow .signal-circle {
  transition: all 0.4s ease;
  animation: vibeflow-pulse 2s infinite;
}

@keyframes vibeflow-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.light-logic-cosmic .signal-circle {
  transition: all 0.5s ease;
  animation: cosmic-glow 3s infinite;
  filter: hue-rotate(0deg);
}

@keyframes cosmic-glow {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  33% { filter: hue-rotate(120deg) brightness(1.2); }
  66% { filter: hue-rotate(240deg) brightness(1.1); }
}

.light-logic-chaos .signal-circle {
  transition: all 0.1s ease;
  animation: chaos-flicker 0.5s infinite;
}

@keyframes chaos-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Enhanced Effects */
.enable-pulse {
  animation: enhanced-pulse 1.5s infinite !important;
}

@keyframes enhanced-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.enable-glow {
  box-shadow: 0 0 15px currentColor !important;
}

.enable-chevrons::before {
  content: '▶';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: currentColor;
  font-size: 0.8em;
  opacity: 0.7;
}

/* Convergence Visual Effects */
.convergence-buy {
  background: radial-gradient(circle, rgba(0, 255, 136, 0.1) 0%, transparent 70%);
}

.convergence-sell {
  background: radial-gradient(circle, rgba(255, 71, 87, 0.1) 0%, transparent 70%);
}

.convergence-neutral {
  background: radial-gradient(circle, rgba(255, 165, 2, 0.1) 0%, transparent 70%);
}

/* Background Effects */
.bg-convergence-buy {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 50%, 
    rgba(0, 0, 0, 0.8) 100%);
}

.bg-convergence-sell {
  background: linear-gradient(135deg, 
    rgba(255, 71, 87, 0.05) 0%, 
    rgba(255, 165, 2, 0.03) 50%, 
    rgba(0, 0, 0, 0.8) 100%);
}

.bg-convergence-neutral {
  background: linear-gradient(135deg, 
    rgba(255, 165, 2, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 50%, 
    rgba(0, 0, 0, 0.8) 100%);
}

/* Convergence Animations */
.convergence-pulse {
  animation: convergence-pulse-bg 2s infinite;
}

@keyframes convergence-pulse-bg {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.1); }
}

.divergence-warning {
  animation: divergence-warning-bg 1s infinite;
}

@keyframes divergence-warning-bg {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(30deg); }
}

/* Helper Step Enhancements */
.helper-step {
  transition: all 0.3s ease;
}

.helper-step.highlight-active {
  background: rgba(0, 212, 255, 0.1);
  border-left: 3px solid #00d4ff;
  padding-left: 15px;
  animation: step-highlight 2s infinite;
}

@keyframes step-highlight {
  0%, 100% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.3); }
  50% { box-shadow: 0 0 15px rgba(0, 212, 255, 0.6); }
}

.step-buy {
  border-left-color: #00ff88;
  background: rgba(0, 255, 136, 0.05);
}

.step-sell {
  border-left-color: #ff4757;
  background: rgba(255, 71, 87, 0.05);
}

.step-neutral {
  border-left-color: #ffa502;
  background: rgba(255, 165, 2, 0.05);
}

.step-highlight {
  animation: step-highlight-enhanced 1.5s infinite;
}

@keyframes step-highlight-enhanced {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(5px); }
}

/* Priority Signals */
.priority-signal {
  z-index: 15 !important;
  transform: scale(1.1);
  box-shadow: 0 0 20px currentColor;
  animation: priority-pulse 1s infinite;
}

@keyframes priority-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Weak Signals */
.weak-signal {
  opacity: 0.5 !important;
  filter: grayscale(0.3);
}

/* Dot Matrix Effects */
.matrix-dot {
  transition: all 0.3s ease;
}

.dot-buy {
  background: radial-gradient(circle, #00ff88 0%, transparent 70%);
}

.dot-sell {
  background: radial-gradient(circle, #ff4757 0%, transparent 70%);
}

.dot-neutral {
  background: radial-gradient(circle, #ffa502 0%, transparent 70%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-signal-logic-menu {
    max-width: 100%;
    padding: 15px;
  }
  
  .menu-tabs {
    flex-direction: column;
    gap: 5px;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 10px;
  }
  
  .menu-actions {
    flex-direction: column;
  }
}
/* Light Logic and Logic Menu Styles */
.light-logic-description {
  margin: 10px 0;
  font-size: 0.9rem;
  color: #CCCCCC;
  line-height: 1.4;
}

.light-logic-option {
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.5);
  border-radius: 5px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.option-description {
  margin-top: 5px;
  font-size: 0.8rem;
  color: #AAAAAA;
  font-style: italic;
}

/* Strategy button styles */
.strategy-button {
  background: linear-gradient(to right, #00BFFF, #00FFFF) !important;
  color: #000 !important;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.strategy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 191, 255, 0.4);
}

/* Light logic controls */
.light-logic-controls {
  padding: 15px;
}

.light-logic-option h4 {
  margin: 0 0 10px 0;
  color: #00FFFF;
  font-size: 1rem;
}

/* 🚀 SIGNAL LOGIC MENU - UNIFIED POSITIONING SYSTEM */
#logicMenu {
  position: fixed !important; /* Force fixed positioning like other menus */
  width: 500px !important; /* Match other enhanced menus */
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
  border: 2px solid #00d4ff !important;
  border-radius: 15px !important;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  z-index: 1000 !important;
  padding: 20px !important;
  color: #ffffff !important;
  display: none !important;
  pointer-events: auto !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  /* 💥 POSITIONED BY JAVASCRIPT TO ENSURE MOMENTUM-INDICATORS IS VISIBLE */
}

#logicMenu.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

#logicMenu h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #00ccff;
  font-size: 1.1rem;
  border-bottom: 1px solid #303045;
  padding-bottom: 8px;
}

.logic-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.logic-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.logic-group label {
  font-size: 0.9rem;
  color: #aaa;
}

.logic-selector {
  padding: 6px 8px;
  background: #0f0f1a;
  border: 1px solid #303045;
  color: #e0e0e0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.logic-description {
  font-size: 0.8rem;
  color: #888;
  margin: 5px 0 0;
  line-height: 1.4;
}

.apply-logic-button {
  background: #2a5a8a;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 10px;
  transition: background 0.2s;
}

.apply-logic-button:hover {
  background: #3a6a9a;
}

/* Animation for menu appearance */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ENHANCED LIGHT LOGIC VISUAL TRIGGERS */

/* Convergence pulse animation for helper steps */
@keyframes convergencePulse {
  0% {
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
    transform: scale(1);
  }
}

/* Ordered pulse for convergence representation */
@keyframes orderedPulse {
  0% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

/* Strong convergence pulse */
@keyframes strongConvergence {
  0% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    border: 2px solid rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 215, 0, 1);
    border: 2px solid rgba(255, 215, 0, 0.8);
  }
  100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    border: 2px solid rgba(255, 215, 0, 0.3);
  }
}

/* Light logic activation classes */
.signal-circle.convergence-active {
  animation: convergencePulse 2s ease-in-out infinite;
}

.signal-circle.ordered-pulse {
  animation: orderedPulse 1.5s ease-in-out infinite;
}

.signal-circle.ordered-pulse-2 {
  animation: strongConvergence 1s ease-in-out infinite;
}

/* Helper step visual triggers */
.helper-step-active {
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0.3));
  border-left: 4px solid #00FFFF;
  padding-left: 12px;
  transition: all 0.3s ease;
}

.helper-step-completed {
  background: linear-gradient(90deg, rgba(0, 255, 0, 0.1), rgba(0, 255, 0, 0.2));
  border-left: 4px solid #00FF00;
  opacity: 0.7;
}

/* Convergence representation enhancements */
.convergence-indicator {
  position: relative;
  overflow: visible;
}

.convergence-indicator::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: inherit;
  background: linear-gradient(45deg, #00FFFF, #FF00FF, #FFFF00, #00FFFF);
  background-size: 400% 400%;
  animation: convergenceGlow 3s ease infinite;
  z-index: -1;
}

@keyframes convergenceGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Light intensity variations */
.light-intensity-low {
  opacity: 0.4;
  filter: brightness(0.6);
}

.light-intensity-medium {
  opacity: 0.7;
  filter: brightness(0.8);
}

.light-intensity-high {
  opacity: 1;
  filter: brightness(1.2);
}

.light-intensity-maximum {
  opacity: 1;
  filter: brightness(1.5) saturate(1.3);
  box-shadow: 0 0 15px currentColor;
}

/* System notification */
.system-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 2000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Preview light styles */
.preview-light {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

/* Light logic presets */
.light-logic-presets {
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.3);
  border-radius: 5px;
  border: 1px solid rgba(0, 150, 255, 0.2);
}

.light-logic-presets h5 {
  margin: 0 0 10px 0;
  color: #00BFFF;
  font-size: 0.9rem;
}

.preset-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preset-button {
  background: rgba(0, 150, 255, 0.2);
  border: 1px solid rgba(0, 150, 255, 0.5);
  color: #00BFFF;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.preset-button:hover {
  background: rgba(0, 150, 255, 0.4);
  transform: translateY(-1px);
}

/* Slider styles */
.slider-container {
  margin: 10px 0;
  position: relative;
  padding: 15px 0;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
  color: #00BFFF;
  font-size: 0.9rem;
}

.slider-container input[type="range"] {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  border-radius: 2px;
  background: rgba(0, 150, 255, 0.2);
  outline: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00BFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-container input[type="range"]:hover::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.8);
}

/* Color picker styles */
.color-picker-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.color-picker-container label {
  margin-right: 10px;
  color: #00BFFF;
  font-size: 0.9rem;
  min-width: 100px;
}

.color-picker {
  width: 30px;
  height: 30px;
  border: 2px solid #333;
  border-radius: 4px;
  cursor: pointer;
  background: #000;
}

/* Animation preview */
.animation-preview {
  width: 100%;
  height: 60px;
  margin: 15px 0;
  background: rgba(0, 20, 40, 0.3);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.animation-preview-light {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #00BFFF;
  position: relative;
}
/* Market Trend Visualization Styles */

/* Base Container */
.market-trend-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background: #1e1e2d;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #e0e0e0;
}

/* Trend Strength Indicator */
.trend-strength {
  margin-bottom: 25px;
}

.trend-strength h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #a0a0c0;
}

.trend-meter {
  position: relative;
  height: 30px;
  background: #2a2a3c;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.trend-bar {
  position: absolute;
  top: 0;
  height: 100%;
  width: 0;
  transition: width 0.5s ease, left 0.5s ease, background-color 0.5s ease;
  border-radius: 15px;
}

.trend-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #888;
}

.trend-value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 10;
}

/* Market Phase Indicator */
.market-phase {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 1px;
  margin: 10px 0;
  transition: all 0.3s ease;
}

.market-phase.trending-up {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.5);
}

.market-phase.trending-down {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.5);
}

.market-phase.ranging {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.5);
}

/* Key Levels */n.key-levels {
  margin-top: 25px;
}

.key-levels h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #a0a0c0;
}

.level {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 6px;
  border-radius: 6px;
  background: #2a2a3c;
  font-size: 14px;
  transition: all 0.3s ease;
}

.level.support {
  border-left: 4px solid #4caf50;
}

.level.resistance {
  border-left: 4px solid #f44336;
}

.level-value {
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .market-trend-container {
    padding: 15px;
    margin: 10px;
  }
  
  .trend-meter {
    height: 25px;
  }
  
  .trend-value {
    font-size: 12px;
  }
  
  .market-phase {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* Animation for trend changes */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.trend-update {
  animation: pulse 0.5s ease-in-out;
}

/* Tooltip Styles */n.tooltip {
  position: relative;
  display: inline-block;
  cursor: help;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 13px;
  line-height: 1.4;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}
/* Mini-chart styles */
.mini-chart-container {
    position: relative;
    width: 100%;
    height: 80px;
    margin: 5px 0;
    background: rgba(0, 10, 20, 0.5); /* Lighter background */
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 5px;
    overflow: hidden;
    box-shadow: none; /* Removed box shadow */
}

.mini-chart-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 15; /* Further increased to ensure it's above all overlays */
    box-shadow: none; /* Remove any shadow that might be affecting the chart */
    background: transparent !important; /* Force transparent background */
}

/* Apply to all canvas elements in mini charts */
.mini-chart canvas {
    z-index: 15;
    box-shadow: none !important;
    background: transparent !important;
}

.live-readout {
    position: absolute;
    top: 2px;
    left: 3px;
    width: auto; /* Auto width to fit content */
    font-family: 'Orbitron', sans-serif; /* Back to Orbitron for consistency */
    font-size: 8px; /* Larger font size for better readability */
    font-weight: bold;
    letter-spacing: normal; /* Normal letter spacing */
    color: #00FFFF;
    text-shadow: 0 0 2px rgba(0, 255, 255, 0.8); /* Sharper text shadow */
    z-index: 10; /* Increased z-index to ensure it's above other elements */
    background: rgba(0, 0, 0, 0.7); /* Darker background for better contrast */
    padding: 1px 2px; /* Slightly more padding */
    border-radius: 2px;
    white-space: nowrap;
    overflow: visible; /* Make overflow visible */
    max-width: none; /* Remove max width restriction */
    border: 1px solid rgba(0, 255, 255, 0.3); /* More visible border */
    transform: none; /* No scaling - causes blurriness */
    line-height: 1.2; /* Better line height */
}

.timestamp {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-family: 'Orbitron', sans-serif;
    font-size: 8px;
    color: rgba(0, 255, 255, 0.7);
    z-index: 2;
    background: rgba(0, 10, 20, 0.7);
    padding: 1px 3px;
    border-radius: 2px;
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
    pointer-events: none; /* Ensures the grid doesn't interfere with chart interactions */
}

.indicator-label {
    position: absolute;
    top: 5px;
    left: 10px;
    font-family: 'Orbitron', sans-serif;
    font-size: 10px;
    font-weight: bold;
    color: rgba(0, 255, 255, 0.7);
    text-transform: uppercase;
    z-index: 2;
    background: rgba(0, 10, 20, 0.7);
    padding: 2px 5px;
    border-radius: 3px;
}

/* Remove any potential overlay from mini-chart elements */
.mini-chart::before,
.mini-chart::after {
    display: none !important;
    content: none !important;
    box-shadow: none !important;
    background: none !important;
}

/* Ensure chart data is visible */
.mini-chart .chart-line,
.mini-chart path,
.mini-chart line {
    z-index: 20 !important;
    stroke-width: 1.5px !important; /* Make lines more visible */
    opacity: 1 !important;
}

/* Signal circle tooltip styles */
.signal-circle {
    position: relative;
    cursor: pointer;
    /* Enhanced styling for better visibility */
    width: 33px;
    height: 33px;
    border-radius: 50%;
    box-shadow: 0 0 12px rgba(0, 255, 255, 0.5);
    margin: 1px;
    transition: all 0.2s ease;
}

.signal-circle::after {
    content: attr(title);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 10, 20, 0.95);
    color: #00FFFF;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid rgba(0, 255, 255, 0.5);
    white-space: nowrap;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
}

.signal-circle:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Status icons for live readouts */
.status-icon {
    margin-left: 5px;
    font-size: 10px;
}

.status-up {
    color: #00FF00;
}

.status-down {
    color: #FF0000;
}

/* Animation for value updates */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Chart tooltip styles */
.chartjs-tooltip {
    background: rgba(0, 10, 20, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.5) !important;
    border-radius: 5px !important;
    color: #00FFFF !important;
    font-family: 'Orbitron', sans-serif !important;
    padding: 10px !important;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
    z-index: 1000 !important;
}

/* Ensure signal matrix has proper spacing */
.signal-matrix {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.signal-row {
    display: flex;
    align-items: center;
    gap: 5px;
}

.indicator-name {
    width: 120px;
    font-family: 'Orbitron', sans-serif;
    font-size: 12px;
    color: #00FFFF;
    text-transform: uppercase;
}

.signal-cell {
    width: 33px;
    height: 33px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure all indicators are visible */
[data-indicator] {
    display: flex !important;
}

/* Enhanced signal circle color classes */
.green-light {
    background-color: rgba(0, 255, 0, 0.3) !important;
    border: 2px solid #00FF00 !important;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.6) !important;
}

.blue-light {
    background-color: rgba(0, 100, 255, 0.3) !important;
    border: 2px solid #0064FF !important;
    box-shadow: 0 0 15px rgba(0, 100, 255, 0.6) !important;
}

.orange-light {
    background-color: rgba(255, 165, 0, 0.3) !important;
    border: 2px solid #FFA500 !important;
    box-shadow: 0 0 15px rgba(255, 165, 0, 0.6) !important;
}

.red-light {
    background-color: rgba(255, 0, 0, 0.3) !important;
    border: 2px solid #FF0000 !important;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.6) !important;
}

.grey-light {
    background-color: rgba(128, 128, 128, 0.3) !important;
    border: 2px solid #808080 !important;
    box-shadow: 0 0 15px rgba(128, 128, 128, 0.6) !important;
}

/* Signal classes for trade direction */
.degen-buy {
    background-color: rgba(0, 255, 0, 0.3) !important;
    border: 3px solid #00FF00 !important;
    box-shadow: 0 0 18px rgba(0, 255, 0, 0.7) !important;
}

.mild-buy {
    background-color: rgba(0, 100, 255, 0.3) !important;
    border: 3px solid #0064FF !important;
    box-shadow: 0 0 18px rgba(0, 100, 255, 0.7) !important;
}

.neutral {
    background-color: rgba(128, 128, 128, 0.3) !important;
    border: 3px solid #808080 !important;
    box-shadow: 0 0 18px rgba(128, 128, 128, 0.7) !important;
}

.mild-sell {
    background-color: rgba(255, 165, 0, 0.3) !important;
    border: 3px solid #FFA500 !important;
    box-shadow: 0 0 18px rgba(255, 165, 0, 0.7) !important;
}

.degen-sell {
    background-color: rgba(255, 0, 0, 0.3) !important;
    border: 3px solid #FF0000 !important;
    box-shadow: 0 0 18px rgba(255, 0, 0, 0.7) !important;
}
/* Price Display Styles */
.price-display {
  font-family: 'Orbitron', 'Roboto', sans-serif;
  font-weight: bold;
  font-size: 1.5rem;
  text-align: center;
  margin: 0.5rem 0;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.price-up {
  color: #00FF00;
  animation: pricePulseGreen 2s infinite;
}

.price-down {
  color: #FF3B30;
  animation: pricePulseRed 2s infinite;
}

.price-unchanged {
  color: #CCCCCC;
}

.price-change {
  font-size: 1rem;
  margin-left: 0.5rem;
  vertical-align: middle;
}

/* Price Change Indicators */
.price-change.positive {
  color: #4CD964;
}

.price-change.negative {
  color: #FF3B30;
}

/* Price Container */
.price-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: rgba(10, 14, 23, 0.7);
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.price-container:hover {
  background: rgba(16, 22, 36, 0.8);
  box-shadow: 0 0 15px rgba(0, 180, 216, 0.3);
}

/* Price Label */
.price-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Price Chart Container */
.price-chart-container {
  width: 100%;
  height: 100px;
  margin-top: 1rem;
  position: relative;
}

/* Price Stats */
.price-stats {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 0.5rem;
  font-size: 0.8rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.7rem;
  margin-bottom: 0.2rem;
}

.stat-value {
  font-weight: bold;
}

/* Price History */
.price-history {
  width: 100%;
  margin-top: 1rem;
  font-size: 0.8rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 1px solid var(--border-color);
}

.history-label {
  color: var(--text-secondary);
}

.history-value {
  font-weight: 500;
}

/* Animations */
@keyframes pricePulseGreen {
  0%, 100% { text-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
  50% { text-shadow: 0 0 20px rgba(0, 255, 0, 0.8); }
}

@keyframes pricePulseRed {
  0%, 100% { text-shadow: 0 0 10px rgba(255, 59, 48, 0.5); }
  50% { text-shadow: 0 0 20px rgba(255, 59, 48, 0.8); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .price-display {
    font-size: 1.2rem;
  }
  
  .price-change {
    font-size: 0.9rem;
  }
  
  .price-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}
/* Indicator Rows */
#momentum-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px 0;
  width: 100%;
}

/* Indicators container */
#momentum-indicators {
  width: 100%;
  margin-bottom: 15px;
}

/* Indicators table */
.indicators-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 4px;
}

/* Indicator row */
.indicator-row {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  transition: all 0.3s ease;
  height: 60px;
}

.indicator-row:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.1);
}

/* Table cells */
.indicators-table td {
  padding: 8px 12px;
  vertical-align: middle;
  height: 60px;
  box-sizing: border-box;
}

/* Label cell */
.label-cell {
  width: 120px;
  min-width: 120px;
  border-right: 1px solid rgba(0, 255, 255, 0.1);
  padding-right: 15px;
}

/* Indicator name */
.indicator-name {
  font-family: 'Orbitron', sans-serif;
  font-size: 12px;
  color: #00FFFF;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

/* Mini chart cell */
.mini-chart-cell {
  padding: 0 15px !important;
  border-right: 1px solid rgba(0, 255, 255, 0.1);
  width: auto;
}

.mini-chart {
  position: relative;
  width: 200px;
  height: 40px;
  background: rgba(0, 10, 20, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.mini-chart canvas {
  display: block;
  width: 100% !important;
  height: 40px !important;
}

.live-readout {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  font-size: 11px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid;
  white-space: nowrap;
}

.indicator-label {
  display: none; /* Hidden in production */
}

/* Signal lights cell */
/* Signals cell */
.signals-cell {
  text-align: right;
  white-space: nowrap;
  padding-left: 15px !important;
  min-width: 300px;
}

/* Signal circle */
.signal-circle {
  display: inline-block;
  width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: 50%;
  text-align: center;
  font-size: 10px;
  font-weight: bold;
  margin: 0 2px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  background: rgba(64, 64, 96, 0.8);
}

.signal-circle:hover {
  transform: scale(1.1);
  z-index: 10;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.signal-circle.active {
  border: 2px solid #00FFFF;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
}

.signal-circle[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 20, 40, 0.9);
  color: #00FFFF;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 100;
  border: 1px solid rgba(0, 255, 255, 0.5);
  pointer-events: none;
}

.signal-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #333;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.signal-circle::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.signal-circle:hover::after {
  opacity: 0.3;
}

.signal-circle.neutral {
  background: #666;
  box-shadow: 0 0 5px rgba(102, 102, 102, 0.5);
}

.signal-circle.buy {
  background: #00FF00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
}

.signal-circle.sell {
  background: #FF0000;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
}

.signal-circle.strong-buy {
  background: #00AAFF;
  box-shadow: 0 0 15px rgba(0, 170, 255, 0.8);
  animation: pulse 2s infinite;
}

.signal-circle.strong-sell {
  background: #FF5500;
  box-shadow: 0 0 15px rgba(255, 85, 0, 0.8);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
}

/* Tooltip styles */
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  pointer-events: none;
}

[data-tooltip]:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 5px);
}

/* Strategy-specific styles for StarCrypt */

/* Strategy Selector Container */
.strategy-selector-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  max-width: 100%;
}

/* Strategy Select Dropdown */
#mainStrategySelector {
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #444;
  background-color: #2a2a2a;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23e0e0e0%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat, repeat;
  background-position: right 0.7em top 50%, 0 0;
  background-size: 0.65em auto, 100%;
  padding-right: 2.5em;
}

#mainStrategySelector:hover {
  border-color: #666;
  background-color: #333;
}

#mainStrategySelector:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

/* Apply Button */
#applyStrategyBtn {
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

#applyStrategyBtn:hover {
  background-color: #3a7bc8;
  transform: translateY(-1px);
}

#applyStrategyBtn:active {
  transform: translateY(0);
  background-color: #2d6cb0;
}

#applyStrategyBtn:disabled {
  background-color: #555;
  cursor: not-allowed;
  transform: none;
  opacity: 0.7;
}

/* Status Message */
.status {
  padding: 10px 15px;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 13px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  height: 0;
  padding: 0 15px;
  overflow: hidden;
}

.status.visible {
  opacity: 1;
  transform: translateY(0);
  height: auto;
  padding: 10px 15px;
  margin-top: 10px;
}

.status.success {
  background-color: rgba(46, 204, 113, 0.15);
  border-left: 3px solid #2ecc71;
  color: #2ecc71;
}

.status.error {
  background-color: rgba(231, 76, 60, 0.15);
  border-left: 3px solid #e74c3c;
  color: #e74c3c;
}

.status.info {
  background-color: rgba(52, 152, 219, 0.15);
  border-left: 3px solid #3498db;
  color: #3498db;
}

/* Strategy Info Panel */
#strategyInfo {
  padding: 15px;
  margin: 15px 0;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
  border-left: 3px solid #4a90e2;
  transition: all 0.3s ease;
}

#strategyInfo h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #e0e0e0;
  font-size: 16px;
}

#strategyInfo p {
  margin: 5px 0;
  color: #b0b0b0;
  font-size: 13px;
  line-height: 1.5;
}

/* Signal Matrix Styles */
#signalMatrixContainer {
  min-height: 300px;
  transition: all 0.3s ease;
  overflow: hidden;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.1);
}

.signal-row {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.signal-row.header {
  background-color: rgba(0, 0, 0, 0.2);
  font-weight: 600;
  color: #888;
  text-transform: uppercase;
  font-size: 11px;
  letter-spacing: 0.5px;
}

.signal-label {
  padding: 12px 15px;
  min-width: 120px;
  font-weight: 500;
  color: #e0e0e0;
  display: flex;
  align-items: center;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.signal-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 5px;
  min-width: 60px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.signal-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #444;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

/* Signal States */
.signal-circle.strong-buy {
  background-color: #00c853;
  box-shadow: 0 0 10px rgba(0, 200, 83, 0.5);
}

.signal-circle.mild-buy {
  background-color: #64dd17;
  box-shadow: 0 0 8px rgba(100, 221, 23, 0.4);
}

.signal-circle.neutral {
  background-color: #9e9e9e;
}

.signal-circle.mild-sell {
  background-color: #ff9100;
  box-shadow: 0 0 8px rgba(255, 145, 0, 0.4);
}

.signal-circle.strong-sell {

/* Tooltip styles */

/* Responsive adjustments */
@media (max-width: 768px) {
  .strategy-selector-container {
    flex-direction: column;
  }
  
  #mainStrategySelector, #applyStrategyBtn {
    width: 100%;
  }
  
  .signal-label {
    min-width: 100px;
    font-size: 12px;
    padding: 10px 8px;
  }
  
  .signal-cell {
    min-width: 40px;
    padding: 5px 3px;
  }
}

/* Animation for signal updates */
@keyframes pulse-update {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.signal-updated {
  animation: pulse-update 0.5s ease-out;
}
/* thresholds-menu.css */
.thresholds-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(10, 20, 40, 0.98);
  border: 1px solid #00a8ff;
  border-radius: 8px;
  padding: 15px;
  width: 400px;
  max-width: 95vw;
  max-height: 80vh;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  color: #e0e0e0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: none;
  overflow-y: auto;
  margin-top: 10px;
  border-top-right-radius: 0;
  border-top-left-radius: 8px;
  border-bottom-right-radius: 8px;
  transition: all 0.3s ease;
}

.thresholds-menu.active {
  display: block;
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure the menu stays within viewport on small screens */
@media (max-width: 768px) {
  .thresholds-menu {
    width: 95%;
    right: 2.5%;
    left: auto;
  }
}

.threshold-sliders-container {
  max-height: 65vh;
  overflow-y: auto;
  padding: 10px 5px;
  margin-right: -5px;
  padding-right: 5px;
  scrollbar-width: thin;
  scrollbar-color: #00a8ff rgba(0, 0, 0, 0.2);
}

/* Menu header styles */
.thresholds-menu .menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 200, 255, 0.3);
}

.thresholds-menu .menu-header h3 {
  margin: 0;
  color: #00ccff;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.2rem;
}

/* Close button */
.thresholds-menu .close-menu {
  background: none;
  border: none;
  color: #ff4d4d;
  font-size: 1.5rem;
  cursor: pointer;
  line-height: 1;
  padding: 0 5px;
  transition: color 0.2s;
}

.thresholds-menu .close-menu:hover {
  color: #ff0000;
}

/* Custom scrollbar for WebKit browsers */
.threshold-sliders-container::-webkit-scrollbar {
  width: 6px;
}

.threshold-sliders-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.threshold-sliders-container::-webkit-scrollbar-thumb {
  background-color: var(--accent-color, #00b4d8);
  border-radius: 3px;
}

.slider-group {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.slider-group:hover {
  background: rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 10px rgba(0, 180, 216, 0.1);
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

.slider-header h4 {
  margin: 0;
  color: var(--accent-color, #00b4d8);
  font-size: 0.95rem;
  font-weight: 600;
  text-transform: capitalize;
}

.threshold-values {
  font-size: 0.8rem;
  color: var(--text-secondary, #8e9aaf);
  background: rgba(0, 0, 0, 0.3);
  padding: 5px 10px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  box-sizing: border-box;
}

.threshold-container h4 {
  margin: 0 0 10px 0;
  color: var(--accent-color, #00b4d8);
  font-size: 0.9rem;
}

.slider-container {
  position: relative;
  height: 40px;
  margin: 15px 0 30px;
  padding: 10px 0;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
}

.slider-track {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-50%);
  border-radius: 4px;
  overflow: visible;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  width: 100%;
  box-sizing: border-box;
}

.slider-segment {
  position: absolute;
  height: 100%;
  transition: all 0.2s ease;
}

.slider-segment.green {
  left: 0;
  background: linear-gradient(90deg, rgba(0, 200, 83, 0.7), rgba(0, 200, 83, 0.9));
  border-radius: 4px 0 0 4px;
}

.slider-segment.blue {
  background: linear-gradient(90deg, rgba(33, 150, 243, 0.7), rgba(33, 150, 243, 0.9));
}

.slider-segment.neutral {
  background: rgba(255, 255, 255, 0.05);
}

.slider-segment.orange {
  background: linear-gradient(90deg, rgba(255, 152, 0, 0.7), rgba(255, 152, 0, 0.9));
}

.slider-segment.red {
  background: linear-gradient(90deg, rgba(244, 67, 54, 0.7), rgba(244, 67, 54, 0.9));
  border-radius: 0 4px 4px 0;
}

.slider-thumb {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(0, 180, 216, 0.5);
  transition: all 0.2s ease;
  z-index: 2;
}

.slider-thumb:hover, .slider-thumb:active {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 3px rgba(0, 180, 216, 0.7);
}

.slider-thumb:active {
  cursor: grabbing;
}

/* Tooltip for thumbs */
.slider-thumb::after {
  content: attr(data-threshold);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-5px);
  background-color: #333;
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.slider-thumb:hover::after {
  opacity: 1;
}

/* Reset button */
.reset-thresholds-button {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 15px;
  background: rgba(244, 67, 54, 0.2);
  color: #ff5252;
  border: 1px solid #ff5252;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.reset-thresholds-button:hover {
  background: rgba(244, 67, 54, 0.3);
  transform: translateY(-1px);
}

.reset-thresholds-button:active {
  transform: translateY(0);
}

/* Ensure menu doesn't overlap signal lights */
@media (min-width: 1200px) {
  .ticker-container {
    position: relative;
  }
  
  .thresholds-menu {
    position: absolute;
    right: 0;
    left: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .thresholds-menu {
    width: 90%;
    right: 5%;
    left: 5%;
  }
  
  .slider-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .threshold-values {
    margin-top: 5px;
    width: 100%;
    text-align: left;
  }
}

.segment {
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
}

.segment.green { background: #27ae60; }
.segment.blue { background: #3498db; }
.segment.grey { background: #7f8c8d; }
.segment.orange { background: #f39c12; }
.segment.red { background: #e74c3c; }

.slider-thumb {
  position: absolute;
  width: 12px;
  height: 20px;
  background: white;
  border-radius: 3px;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0,0,0,0.5);
  z-index: 2;
}

.green-thumb { background: #27ae60; }
.blue-thumb { background: #3498db; }
.orange-thumb { background: #f39c12; }
.red-thumb { background: #e74c3c; }

.threshold-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
  font-size: 0.7rem;
  color: var(--text-secondary, #a0a0a0);
}

.threshold-label {
  padding: 2px 5px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.reset-thresholds-button {
  display: block;
  width: 100%;
  padding: 8px;
  margin-top: 10px;
  background: var(--accent-color, #00b4d8);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background 0.2s;
}

.reset-thresholds-button:hover {
  background: var(--accent-secondary, #0077b6);
}

/* Menu button styles */
#toggleThresholdsButton {
  background: linear-gradient(90deg, #00FFFF 0%, #0055FF 100%);
  color: #0a0a1a;
  border: 1px solid #00FFFF;
  border-radius: 4px;
  padding: 0.5em 1em;
  font-family: 'Orbitron', sans-serif;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 255, 255, 0.2);
  margin-right: 0.5rem;
}

#toggleThresholdsButton:hover {
  background: linear-gradient(90deg, #00CCCC 0%, #0044CC 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

#toggleThresholdsButton:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(0, 255, 255, 0.2);
}

/* Dark theme by default */
:root {
    --signal-border: rgba(255, 255, 255, 0.1);
    --signal-shadow: rgba(0, 0, 0, 0.3);
    --signal-hover-shadow: rgba(255, 255, 255, 0.3);
    --tooltip-bg: rgba(0, 0, 0, 0.8);
    --tooltip-text: #fff;
}

/* Light theme support */
[data-theme="light"] {
    --signal-border: rgba(0, 0, 0, 0.1);
    --signal-shadow: rgba(0, 0, 0, 0.1);
    --signal-hover-shadow: rgba(0, 0, 0, 0.2);
    --tooltip-bg: rgba(255, 255, 255, 0.95);
    --tooltip-text: #333;
}

/* Update signal circle styles to use theme variables */

/* Responsive adjustments */
@media (max-width: 1200px) {
}

@media (max-width: 768px) {
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white;
        color: black;
    }
}

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Prevent horizontal scrolling and ensure proper layout */
html, body {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

/* Ensure main content containers don't cause overflow */
.main-content,
.indicators-and-chart,
.chart-column,
.indicators-column,
.bordered-chart-container,
.chart-container,
#tradingview_candle,
#tradingview_main_chart {
    max-width: 100%;
    overflow-x: hidden;
}

/* Fix for TradingView iframe */
#tradingview_candle iframe {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
}

/* Ensure Oracle Matrix container stays within bounds */
.indicators-section.cosmic-indicators {
    max-width: 100%;
    overflow: hidden;
}

    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.2s ease;
}

/* Ensure proper stacking context */
body {
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Fix for WebSocket connection status */
.connection-status {
    position: fixed;
    bottom: 10px;
    right: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background-color: #48bb78;
    color: white;
}

.connection-status.disconnected {
    background-color: #f56565;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

/* Fix for scrollbars */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white;
        color: black;
    }
}
