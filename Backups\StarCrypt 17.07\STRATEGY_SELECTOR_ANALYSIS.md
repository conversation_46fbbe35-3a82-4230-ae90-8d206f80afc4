# Strategy Selector Analysis

## analyze-strategy-selector.js

### initializeStrategySelector (2 occurrences)

- eferences         const patterns = [             'initializeStrategySelector',             'strategySelector',             'st
- # Recommendations\n\n';     output += '1. Ensure `initializeStrategySelector` is only called once\n';     output += '2. Check 

### strategySelector (1 occurrences)

-        'initializeStrategySelector',             'strategySelector',             'strategy-selector',             'm

### strategy-selector (2 occurrences)

- or',             'strategySelector',             'strategy-selector',             'mainStrategySelector',            
- izes the application\n';     output += '3. `js/ui/strategy-selector.js` is loaded and initializes the strategy select

### mainStrategySelector (1 occurrences)

- r',             'strategy-selector',             'mainStrategySelector',             'strategyMenu',             'strate

### strategyMenu (1 occurrences)

-              'mainStrategySelector',             'strategyMenu',             'strategy-menu',             'handl

### strategy-menu (1 occurrences)

- lector',             'strategyMenu',             'strategy-menu',             'handleStrategyChange',            

### handleStrategyChange (1 occurrences)

- yMenu',             'strategy-menu',             'handleStrategyChange',             'updateStrategyDescription'        

### updateStrategyDescription (1 occurrences)

-              'handleStrategyChange',             'updateStrategyDescription'         ];                  const matches = []; 


## Backups\05.06 Gemini temporary broken backup\index.html

### strategySelector (44 occurrences)

- nd: var(--text-color);     }     #tokenSelector, #strategySelector {       background: var(--secondary-bg);       co
- (0, 0, 0, 0.3);     }     #tokenSelector option, #strategySelector option {       background: var(--secondary-bg);  
- adding: 0.75rem;     }     #tokenSelector:hover, #strategySelector:hover {       background: rgba(0, 255, 255, 0.1);
-  255, 255, 0.5);     }     #tokenSelector:focus, #strategySelector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.8);     }      #strategySelector {       background-color: rgba(0, 100, 100, 0.3);
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-  {       // Get the strategy selector       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
-      // Update all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- lectorContainer.innerHTML = `         <label for="strategySelector">Select Signal Logic:</label>         <select id=
- >Select Signal Logic:</label>         <select id="strategySelector" class="strategy-selector">           ${Object.ke
- d event listener to strategy selector       const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');       if (strategySelector) {         strategy
- ent.getElementById('strategySelector');       if (strategySelector) {         strategySelector.addEventListener('cha
- Selector');       if (strategySelector) {         strategySelector.addEventListener('change', function() {          
- ', function() {           try {             const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');             if (!strategySelector) {          
- ElementById('strategySelector');             if (!strategySelector) {               logMessages.push(`[${new Date().
-           }              const selectedStrategy = strategySelector.value;             if (!selectedStrategy || !TRAD
- = `     <label>       Strategy:       <select id="strategySelector" class="strategy-selector">         <option value
- pdateLogger();       }       if (e.target.id === 'strategySelector') {         const strategy = e.target.value;     
-    // Initialize all strategy selectors     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector) {     
- const selectedStrategy = document.getElementById('strategySelector').value;          // Show strategy animation     
- ate all strategy selectors to match         const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.
- '#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => {           if (selector) { 
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-    }    // Initialize strategy selector     const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');     if (strategySelector) {       // Set initi
- ument.getElementById('strategySelector');     if (strategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       strategySelector.value = currentStrategy;        strategySelector.
-  strategySelector.value = currentStrategy;        strategySelector.addEventListener('change', function() {         c

### strategy-selector (27 occurrences)

- i/timeframe-ui.js"></script>   <script src="js/ui/strategy-selector.js"></script>   <script src="js/ui/strategy-logic
- }     }      /* Strategy selector styling */     .strategy-selector {       background: var(--secondary-bg);       co
- no-repeat;       padding-right: 30px;     }      .strategy-selector:hover {       box-shadow: 0 0 15px rgba(0, 255, 2
-      border-color: var(--text-color);     }      .strategy-selector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.7);     }      .strategy-selector option {       background: var(--secondary-bg);  
- color: #FFF;   box-shadow: 0 0 18px #00FFFF99; } .strategy-selector {   font-family: 'Orbitron', sans-serif;   font-s
- w: 0 2px 12px #00FFFF22;   margin-right: 1rem; } .strategy-selector option {   background-color: #0a0a1a;   color: #0
- slateY(-2px); }  /* Strategy selector styling */ .strategy-selector {   background: linear-gradient(135deg, rgba(0, 2
- on: right 10px center;   padding-right: 30px; }  .strategy-selector:hover {   background-color: rgba(0, 255, 255, 0.1
- 55, 255, 0.5);   transform: translateY(-2px); }  .strategy-selector:focus {   border-color: #FF00FF;   box-shadow: 0 
-  box-shadow: 0 0 20px rgba(255, 0, 255, 0.5); }  .strategy-selector option {   background-color: #0a0a1a;   color: #0
- ading styles.         </div>          <div class="strategy-selector-container">           <label for="mainStrategySel
-          <select id="mainStrategySelector" class="strategy-selector">             <optgroup label="Core Strategies"> 
-   }    /* Enhanced strategy selector styles */   .strategy-selector-container {     margin: 15px 0;     position: rel
-   margin: 15px 0;     position: relative;   }    .strategy-selector {     width: 100%;     padding: 10px;     backgro
- x; /* Limit height to prevent overflow */   }    .strategy-selector:hover {     border-color: rgba(0, 255, 255, 0.8);
- -shadow: 0 0 10px rgba(0, 255, 255, 0.3);   }    .strategy-selector option {     background-color: #0a1525;     color
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
- ment('div');       selectorContainer.className = 'strategy-selector-container';       selectorContainer.innerHTML = `
- bel>         <select id="strategySelector" class="strategy-selector">           ${Object.keys(TRADING_STRATEGIES).map
- ategy:       <select id="strategySelector" class="strategy-selector">         <option value="admiral_toa">Admiral TOA
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
- js/ui/animations.js"></script> <script src="js/ui/strategy-selector.js"></script> <script src="js/ui/update-signal-li

### mainStrategySelector (16 occurrences)

- rategy-selector-container">           <label for="mainStrategySelector">Choose Strategy:</label>           <select id="m
- r">Choose Strategy:</label>           <select id="mainStrategySelector" class="strategy-selector">             <optgroup
- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- e strategy selector with event listener     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('c
- Selector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('change', function() {         /
-    // Initialize main strategy selector     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set i
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       mainStrategySelector.value = currentStrategy;     }      // Initialize
-      const newStrategy = document.getElementById('mainStrategySelector').value;         if (newStrategy !== currentStrat

### strategyMenu (4 occurrences)

- ss="menu-button" id="strategyButton" data-target="strategyMenu">Strategy Selector</button>       <button class="
- ct>     </div>      <div class="menu-content" id="strategyMenu">       <div class="strategy-controls" id="strate
- ) {         const menu = document.getElementById('strategyMenu');         if (menu) {           menu.classList.t
-  strategy menu           document.getElementById('strategyMenu').classList.remove('active');            // Log t


## Backups\05.06 Gemini temporary broken backup\js\global-variables.js

### strategySelector (3 occurrences)

- t mainStrategySelector = document.getElementById('strategySelector') ||                               document.getEl
- / Update all strategy selectors in the UI   const strategySelectors = document.querySelectorAll('#mainStrategySelect
- ll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     if (selector && select

### strategy-selector (1 occurrences)

- ocument.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     i

### mainStrategySelector (6 occurrences)

- l strategy selector value from our global   const mainStrategySelector = document.getElementById('strategySelector') || 
-                          document.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategyS
- ment.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.curren
- ategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.currentStrategy     // Trigger str
- n') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  // Indicators window.INDICATORS 
- t strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach

### handleStrategyChange (2 occurrences)

- change to ensure UI updates     if (typeof window.handleStrategyChange === 'function') {       window.handleStrategyChan
- ndleStrategyChange === 'function') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  /


## Backups\05.06 Gemini temporary broken backup\js\main.js

### initializeStrategySelector (2 occurrences)

-  Initialize strategy selector   if (typeof window.initializeStrategySelector === 'function') {     window.initializeStrategySe
- lizeStrategySelector === 'function') {     window.initializeStrategySelector()     console.log('Strategy selector initialized'

### mainStrategySelector (1 occurrences)

-   const strategySelect = document.getElementById('mainStrategySelector')   const applyStrategyBtn = document.getElementB


## Backups\05.06 Gemini temporary broken backup\js\menu-system.js

### strategy-selector (1 occurrences)

-  strategy selector     if (event.target.closest('.strategy-selector')) {       this.closeAllMenus()     }   }    clos

### strategyMenu (2 occurrences)

- veMenu = null     this.menus = {       strategy: 'strategyMenu',       indicator: 'indicatorMenu',       thresho
-  menu     this.setupMenuButton('strategyButton', 'strategyMenu')      // Indicator menu     this.setupMenuButton

### handleStrategyChange (3 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId)     })   }    initialize() {
- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId)     })      // Menu button f
-  => {       this.handleMenuClick(e)     })   }    handleStrategyChange(strategyId) {     if (!this.strategies[strategyId


## Backups\05.06 Gemini temporary broken backup\js\strategy-manager.js

### strategyMenu (8 occurrences)

- = null     this.strategyContainer = null     this.strategyMenu = null      // Timeframe state     this.timeframe
- ument.getElementById('strategyControls')     this.strategyMenu = document.getElementById('strategyMenu')     thi
-      this.strategyMenu = document.getElementById('strategyMenu')     this.timeframeContainer = document.getEleme
- trols')      if (!this.strategyContainer || !this.strategyMenu || !this.timeframeContainer) {       console.erro
-     }   }    renderStrategyMenu() {     if (!this.strategyMenu) return      const strategyOptions = Object.entri
-           </option>         `).join('')      this.strategyMenu.innerHTML = `             <div class="strategy-co
-      `   }    toggleStrategyMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open
- yMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open')     }   }    handleStrat

### handleStrategyChange (3 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId)     })      // WebSocket con
- "strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">                     ${strategyOptio
- trategyMenu.classList.toggle('open')     }   }    handleStrategyChange(strategyId) {     if (!this.strategies[strategyId

### updateStrategyDescription (2 occurrences)

-  Update UI     this.renderStrategyMenu()     this.updateStrategyDescription()      // Update indicators     this.updateIndica
-      this.notifyStrategyChange(strategyId)   }    updateStrategyDescription() {     const description = this.getStrategyDescr


## Backups\05.06 Gemini temporary broken backup\js\ui\event-handlers.js

### strategySelector (1 occurrences)

- teLogger()         }         if (e.target.id === 'strategySelector') {           const strategy = e.target.value    


## Backups\05.06 Gemini temporary broken backup\js\ui\menu-handler.js

### strategyMenu (2 occurrences)

- ) {     this.activeMenu = null     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
- kHandler('strategyButton', () => this.toggleMenu('strategyMenu'))     this.setupClickHandler('toggleMenuButton',


## Backups\05.06 Gemini temporary broken backup\js\ui\menu-loader.js

### strategyMenu (1 occurrences)

- v>                  <div class="menu-content" id="strategyMenu">           <h3>Strategy Selector</h3>           


## Backups\05.06 Gemini temporary broken backup\js\ui\menu-strategy-fix.js

### strategySelector (6 occurrences)

-  Fix the strategy selector change event     const strategySelector = document.getElementById('mainStrategySelector')
- nt.getElementById('mainStrategySelector')     if (strategySelector) {       // Clone to remove old listeners       c
- to remove old listeners       const newSelector = strategySelector.cloneNode(true)       if (strategySelector.parent
- ctor = strategySelector.cloneNode(true)       if (strategySelector.parentNode) {         strategySelector.parentNode
-        if (strategySelector.parentNode) {         strategySelector.parentNode.replaceChild(newSelector, strategySele
- tegySelector.parentNode.replaceChild(newSelector, strategySelector)       }        // Add preview-only behavior on c

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')     if (strategySelector) {       // Clone to r
-         const selector = document.getElementById('mainStrategySelector')         if (!selector) return          const st

### strategyMenu (4 occurrences)

- gy)          // Close strategy menu         const strategyMenu = document.getElementById('strategyMenu')        
-     const strategyMenu = document.getElementById('strategyMenu')         if (strategyMenu) {           strategyM
- cument.getElementById('strategyMenu')         if (strategyMenu) {           strategyMenu.style.display = 'none' 
- ategyMenu')         if (strategyMenu) {           strategyMenu.style.display = 'none'         }       })     }  


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-comprehensive-fix.js

### strategySelector (6 occurrences)

- ) => {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- getElementById('mainStrategySelector')       if (!strategySelector) {         console.error('[StrategyFix] Strategy 
-    return       }        const selectedStrategy = strategySelector.value       console.log('[StrategyFix] Applying s
- ntStrategy = strategy      // Update UI     const strategySelector = document.getElementById('mainStrategySelector')
- nt.getElementById('mainStrategySelector')     if (strategySelector) {       strategySelector.value = strategy     } 
- ategySelector')     if (strategySelector) {       strategySelector.value = strategy     }      // Update helper and 

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')       if (!strategySelector) {         console.
- const strategySelector = document.getElementById('mainStrategySelector')     if (strategySelector) {       strategySelec


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-consolidated.js

### initializeStrategySelector (1 occurrences)

- y {     // Initialize strategy selector     await initializeStrategySelector()      // Initialize indicator displays     await


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-fix.js

### strategySelector (9 occurrences)

- ent) {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- getElementById('mainStrategySelector')       if (!strategySelector) {         console.error('Strategy selector not f
-    return       }        const selectedStrategy = strategySelector.value       console.log('Applying strategy:', sel
-    // Get current DOM selected strategy     const strategySelector = document.getElementById('mainStrategySelector')
- elector')     let selectorStrategy = null     if (strategySelector) {       selectorStrategy = strategySelector.valu
-  if (strategySelector) {       selectorStrategy = strategySelector.value     }      // Determine correct strategy   
- trategy      // Update selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) { 
- te selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) {       strategySelect
- rategySelector.value !== currentStrategy) {       strategySelector.value = currentStrategy     }      console.log('G

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')       if (!strategySelector) {         console.
- const strategySelector = document.getElementById('mainStrategySelector')     let selectorStrategy = null     if (strateg


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-logic.js

### mainStrategySelector (4 occurrences)

- ateStrategyMenu = function () {   try {     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrat
- nt.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStra
- ySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStrategy     }     const mainStrat


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-manager.js

### strategySelector (1 occurrences)

- leanupGlobals() {     const oldGlobals = [       'strategySelector',       'updateStrategyMenu',       'handleStrate

### strategy-selector (1 occurrences)

- tor management.  * It combines functionality from strategy-selector.js and strategy-logic.js  * to provide a single s

### strategyMenu (13 occurrences)

-    constructor() {     // DOM Elements       this.strategyMenu = document.getElementById('strategyMenu')       t
-      this.strategyMenu = document.getElementById('strategyMenu')       this.strategyButton = document.getElement
- ('strategyButton')       this.strategyList = this.strategyMenu?.querySelector('.strategy-list')       this.strat
- '.strategy-title')       this.strategyDesc = this.strategyMenu?.querySelector('.strategy-description')       thi
- gy-description')       this.strategyHelper = this.strategyMenu?.querySelector('.strategy-helper')       this.app
- '.strategy-helper')       this.applyButton = this.strategyMenu?.querySelector('.apply-strategy')        // State
- tialize() {       if (this.isInitialized || !this.strategyMenu || !this.strategyButton) {         console.warn('
- (e) => {         e.stopPropagation()         this.strategyMenu.classList.toggle('show')       })        if (this
- st if available       const indicatorsList = this.strategyMenu.querySelector('.strategy-indicators')       if (i
- ck('Strategy applied successfully!')         this.strategyMenu.classList.remove('show')          // Update the m
- /     handleDocumentClick(event) {       if (this.strategyMenu && !this.strategyMenu.contains(event.target) &&  
- ick(event) {       if (this.strategyMenu && !this.strategyMenu.contains(event.target) &&         this.strategyBu
- tegyButton.contains(event.target)) {         this.strategyMenu.classList.remove('show')       }     }      /**  


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-persistence.js

### strategySelector (3 occurrences)

-  Get current strategy from selector         const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector')         if (strategySelector) {           const selectedStrategy = strategySel
- egySelector) {           const selectedStrategy = strategySelector.value            // Save to localStorage         

### mainStrategySelector (5 occurrences)

- Strategy)          // Update the UI         const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')         if (mainStrategySelector) {           m
- etElementById('mainStrategySelector')         if (mainStrategySelector) {           mainStrategySelector.value = savedSt
- r')         if (mainStrategySelector) {           mainStrategySelector.value = savedStrategy           console.log('[Str
- const strategySelector = document.getElementById('mainStrategySelector')         if (strategySelector) {           const


## Backups\05.06 Gemini temporary broken backup\js\ui\strategy-selector.js

### initializeStrategySelector (2 occurrences)

- obals = () => {     const oldFunctions = [       'initializeStrategySelector',       'toggleStrategyMenu',       'updateStrate
- egyChange',       'applySelectedStrategy',       'initializeStrategySelectorModule',       'showStatusMessage',       'getCurr

### strategySelector (4 occurrences)

- Crypt.StrategySelector = {     init: () => window.strategySelector?.initialize(),     getCurrentStrategy: () => stra
- dEventListener('beforeunload', () => {     window.strategySelector?.cleanup()   })    // Initialize the strategy sel
- Listener('DOMContentLoaded', () => {       window.strategySelector = new StrategySelectorUI()     })   } else {     
- StrategySelectorUI()     })   } else {     window.strategySelector = new StrategySelectorUI()   }    // Make the Str

### strategyMenu (12 occurrences)

-    constructor() {     // DOM Elements       this.strategyMenu = document.getElementById('strategyMenu')       t
-      this.strategyMenu = document.getElementById('strategyMenu')       this.strategyButton = document.getElement
- ('strategyButton')       this.strategyList = this.strategyMenu?.querySelector('.strategy-list')       this.strat
- '.strategy-title')       this.strategyDesc = this.strategyMenu?.querySelector('.strategy-description')       thi
- gy-description')       this.strategyHelper = this.strategyMenu?.querySelector('.strategy-helper')       this.app
- '.strategy-helper')       this.applyButton = this.strategyMenu?.querySelector('.apply-strategy')        // State
-   if (this.isInitialized) return        if (!this.strategyMenu || !this.strategyList) {         console.warn('[S
-  (success) {       // Close the menu         this.strategyMenu?.classList.remove('active')          // Show succ
- ontent = message        // Add to menu       this.strategyMenu?.appendChild(feedback)        // Remove after del
-      handleDocumentClick(event) {       if (!this.strategyMenu || !this.strategyButton) return        const isCl
- yButton) return        const isClickInside = this.strategyMenu.contains(event.target) ||                        
- target)        if (!isClickInside) {         this.strategyMenu.classList.remove('active')       }     }      /**

### handleStrategyChange (1 occurrences)

- yMenu',       'updateStrategyDescription',       'handleStrategyChange',       'updateStrategyUI',       'updateIndicato

### updateStrategyDescription (1 occurrences)

- tegySelector',       'toggleStrategyMenu',       'updateStrategyDescription',       'handleStrategyChange',       'updateStra


## Backups\05.06 Gemini temporary broken backup\js\ui\timeframe-ui.js

### handleStrategyChange (2 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId)     })   }    initializeWebS
- ateSignalManagerTimeframe()       }     })   }    handleStrategyChange(strategyId) {     console.log(`[TimeframeUI] Stra


## Backups\12.06 cant get lights back\index.html

### strategySelector (43 occurrences)

- nd: var(--text-color);     }     #tokenSelector, #strategySelector {       background: var(--secondary-bg);       co
- (0, 0, 0, 0.3);     }     #tokenSelector option, #strategySelector option {       background: var(--secondary-bg);  
- adding: 0.75rem;     }     #tokenSelector:hover, #strategySelector:hover {       background: rgba(0, 255, 255, 0.1);
-  255, 255, 0.5);     }     #tokenSelector:focus, #strategySelector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.8);     }      #strategySelector {       background-color: rgba(0, 100, 100, 0.3);
- rategy-selector-container">           <select id="strategySelector" class="strategy-selector">             <option v
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-  {       // Get the strategy selector       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
- lectorContainer.innerHTML = `         <label for="strategySelector">Select Signal Logic:</label>         <select id=
- >Select Signal Logic:</label>         <select id="strategySelector" class="strategy-selector">           ${Object.ke
- d event listener to strategy selector       const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');       if (strategySelector) {         strategy
- ent.getElementById('strategySelector');       if (strategySelector) {         strategySelector.addEventListener('cha
- Selector');       if (strategySelector) {         strategySelector.addEventListener('change', function() {          
- ', function() {           try {             const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');             if (!strategySelector) {          
- ElementById('strategySelector');             if (!strategySelector) {               logMessages.push(`[${new Date().
-           }              const selectedStrategy = strategySelector.value;             if (!selectedStrategy || !TRAD
- = `     <label>       Strategy:       <select id="strategySelector" class="strategy-selector">         <option value
- pdateLogger();       }       if (e.target.id === 'strategySelector') {         const strategy = e.target.value;     
-    // Initialize all strategy selectors     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector) {     
- const selectedStrategy = document.getElementById('strategySelector').value;          // Show strategy animation     
- ate all strategy selectors to match         const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.
- '#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => {           if (selector) { 
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-    }    // Initialize strategy selector     const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');     if (strategySelector) {       // Set initi
- ument.getElementById('strategySelector');     if (strategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       strategySelector.value = currentStrategy;        strategySelector.
-  strategySelector.value = currentStrategy;        strategySelector.addEventListener('change', function() {         c
- gy object or name not found for ${newStrategy} in strategySelector listener`);           }            // Force updat

### strategy-selector (25 occurrences)

- i/timeframe-ui.js"></script>   <script src="js/ui/strategy-selector.js"></script>   <script src="js/ui/strategy-logic
- }     }      /* Strategy selector styling */     .strategy-selector {       background: var(--secondary-bg);       co
- no-repeat;       padding-right: 30px;     }      .strategy-selector:hover {       box-shadow: 0 0 15px rgba(0, 255, 2
-      border-color: var(--text-color);     }      .strategy-selector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.7);     }      .strategy-selector option {       background: var(--secondary-bg);  
- color: #FFF;   box-shadow: 0 0 18px #00FFFF99; } .strategy-selector {   font-family: 'Orbitron', sans-serif;   font-s
- w: 0 2px 12px #00FFFF22;   margin-right: 1rem; } .strategy-selector option {   background-color: #0a0a1a;   color: #0
- slateY(-2px); }  /* Strategy selector styling */ .strategy-selector {   background: linear-gradient(135deg, rgba(0, 2
- on: right 10px center;   padding-right: 30px; }  .strategy-selector:hover {   background-color: rgba(0, 255, 255, 0.1
- 55, 255, 0.5);   transform: translateY(-2px); }  .strategy-selector:focus {   border-color: #FF00FF;   box-shadow: 0 
-  box-shadow: 0 0 20px rgba(255, 0, 255, 0.5); }  .strategy-selector option {   background-color: #0a0a1a;   color: #0
- rading styles.         </div>         <div class="strategy-selector-container">           <select id="strategySelecto
- ">           <select id="strategySelector" class="strategy-selector">             <option value="">Select a strategy.
-   }    /* Enhanced strategy selector styles */   .strategy-selector-container {     margin: 15px 0;     position: rel
-   margin: 15px 0;     position: relative;   }    .strategy-selector {     width: 100%;     padding: 10px;     backgro
- x; /* Limit height to prevent overflow */   }    .strategy-selector:hover {     border-color: rgba(0, 255, 255, 0.8);
- -shadow: 0 0 10px rgba(0, 255, 255, 0.3);   }    .strategy-selector option {     background-color: #0a1525;     color
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
- ment('div');       selectorContainer.className = 'strategy-selector-container';       selectorContainer.innerHTML = `
- bel>         <select id="strategySelector" class="strategy-selector">           ${Object.keys(TRADING_STRATEGIES).map
- ategy:       <select id="strategySelector" class="strategy-selector">         <option value="admiral_toa">Admiral TOA
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   

### mainStrategySelector (15 occurrences)

- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- e strategy selector with event listener     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('c
- Selector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('change', function() {         /
-    // Initialize main strategy selector     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set i
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       mainStrategySelector.value = currentStrategy;     }      // Initialize
-      const newStrategy = document.getElementById('mainStrategySelector').value;         if (newStrategy !== currentStrat
- gy object or name not found for ${newStrategy} in mainStrategySelector listener`);           }           updateLogger();

### strategyMenu (5 occurrences)

- ss="menu-button" id="strategyButton" data-target="strategyMenu">Strategy Selector</button>     <button class="me
- egy Helper Text</div>       </div>       <div id="strategyMenu" class="menu-content">         <!-- Strategy item
- rategy Menu -->     <div class="menu-content" id="strategyMenu">       <div class="strategy-controls" id="strate
- ) {         const menu = document.getElementById('strategyMenu');         if (menu) {           menu.classList.t
-  strategy menu           document.getElementById('strategyMenu').classList.remove('active');            // Log t


## Backups\12.06 cant get lights back\js\for deletion- menu\strategy-menu.js

### strategyMenu (2 occurrences)

- )     }   } }  // Export singleton instance const strategyMenu = new StrategyMenu() export default strategyMenu 
-  strategyMenu = new StrategyMenu() export default strategyMenu 

### strategy-menu (1 occurrences)

-  configuration     this.config = {       menuId: 'strategy-menu',       buttonId: 'strategy-button',       contai

### handleStrategyChange (2 occurrences)

- er.on('strategy-change', (data) => {         this.handleStrategyChange(data)       })        // Listen for menu events  
-  - Strategy change data      * @private      */   handleStrategyChange(data) {     try {       // Update state       thi


## Backups\12.06 cant get lights back\js\global-variables.js

### strategySelector (3 occurrences)

- t mainStrategySelector = document.getElementById('strategySelector') ||                                document.getE
- / Update all strategy selectors in the UI   const strategySelectors = document.querySelectorAll('#mainStrategySelect
- l('#mainStrategySelector, .strategy-selector');   strategySelectors.forEach(selector => {     if (selector && select

### strategy-selector (1 occurrences)

- ocument.querySelectorAll('#mainStrategySelector, .strategy-selector');   strategySelectors.forEach(selector => {     

### mainStrategySelector (6 occurrences)

- l strategy selector value from our global   const mainStrategySelector = document.getElementById('strategySelector') || 
-                          document.getElementById('mainStrategySelector');   if (mainStrategySelector) {     mainStrategy
- ent.getElementById('mainStrategySelector');   if (mainStrategySelector) {     mainStrategySelector.value = window.curren
- tegySelector');   if (mainStrategySelector) {     mainStrategySelector.value = window.currentStrategy;     // Trigger st
- n') {       window.handleStrategyChange({ target: mainStrategySelector });     }   } });  // Indicators window.INDICATOR
- t strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector');   strategySelectors.forEac

### handleStrategyChange (2 occurrences)

- change to ensure UI updates     if (typeof window.handleStrategyChange === 'function') {       window.handleStrategyChan
- ndleStrategyChange === 'function') {       window.handleStrategyChange({ target: mainStrategySelector });     }   } }); 


## Backups\12.06 cant get lights back\js\main.js

### initializeStrategySelector (2 occurrences)

- nitialize strategy selector     if (typeof window.initializeStrategySelector === 'function') {       window.initializeStrategy
- zeStrategySelector === 'function') {       window.initializeStrategySelector();       console.log('[Main.js] Strategy selector

### mainStrategySelector (1 occurrences)

-   const strategySelect = document.getElementById('mainStrategySelector');   const applyStrategyBtn = document.getElement


## Backups\12.06 cant get lights back\js\strategy-manager.js

### strategyMenu (8 occurrences)

-       this.strategyContainer = null;         this.strategyMenu = null;                  // Timeframe state      
- .getElementById('strategyControls');         this.strategyMenu = document.getElementById('strategyMenu');       
-      this.strategyMenu = document.getElementById('strategyMenu');         // this.timeframeContainer = document.
-              if (!this.strategyContainer || !this.strategyMenu) {             console.error('[StrategyManager] R
-   }      renderStrategyMenu() {         if (!this.strategyMenu) return;                  const strategyOptions =
- ption>         `).join('');                  this.strategyMenu.innerHTML = `             <div class="strategy-co
-    }      toggleStrategyMenu() {         if (this.strategyMenu) {             this.strategyMenu.classList.toggle
-         if (this.strategyMenu) {             this.strategyMenu.classList.toggle('open');         }     }      ha

### handleStrategyChange (3 occurrences)

- ener('strategyChanged', (e) => {             this.handleStrategyChange(e.detail.strategyId);         });                
- "strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">                     ${strategyOptio
- nu.classList.toggle('open');         }     }      handleStrategyChange(strategyId) {         if (!this.strategies[strate

### updateStrategyDescription (2 occurrences)

- I         this.renderStrategyMenu();         this.updateStrategyDescription();                  // Update indicators         
- this.notifyStrategyChange(strategyId);     }      updateStrategyDescription() {         const description = this.getStrategyD


## Backups\12.06 cant get lights back\js\ui\event-handlers.js

### strategySelector (1 occurrences)

- eLogger();         }         if (e.target.id === 'strategySelector') {           const strategy = e.target.value;   


## Backups\12.06 cant get lights back\js\ui\menu-handler.js

### strategyMenu (2 occurrences)

-  {     this.activeMenu = null;     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
- kHandler('strategyButton', () => this.toggleMenu('strategyMenu'));     this.setupClickHandler('toggleMenuButton'


## Backups\12.06 cant get lights back\js\ui\menu-strategy-fix.js

### strategySelector (6 occurrences)

-  Fix the strategy selector change event     const strategySelector = document.getElementById('mainStrategySelector')
- t.getElementById('mainStrategySelector');     if (strategySelector) {       // Clone to remove old listeners       c
- to remove old listeners       const newSelector = strategySelector.cloneNode(true);       if (strategySelector.paren
- tor = strategySelector.cloneNode(true);       if (strategySelector.parentNode) {         strategySelector.parentNode
-        if (strategySelector.parentNode) {         strategySelector.parentNode.replaceChild(newSelector, strategySele
- tegySelector.parentNode.replaceChild(newSelector, strategySelector);       }              // Add preview-only behavi

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector');     if (strategySelector) {       // Clone to 
-         const selector = document.getElementById('mainStrategySelector');         if (!selector) return;                

### strategyMenu (4 occurrences)

-              // Close strategy menu         const strategyMenu = document.getElementById('strategyMenu');       
-     const strategyMenu = document.getElementById('strategyMenu');         if (strategyMenu) {           strategy
- ument.getElementById('strategyMenu');         if (strategyMenu) {           strategyMenu.style.display = 'none';
- tegyMenu');         if (strategyMenu) {           strategyMenu.style.display = 'none';         }       });     }


## Backups\12.06 cant get lights back\js\ui\strategy-comprehensive-fix.js

### strategySelector (6 occurrences)

- ent) {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('[StrategyFix] Strategy 
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('[StrategyFix] Applying 
- ategy = strategy;          // Update UI     const strategySelector = document.getElementById('mainStrategySelector')
- t.getElementById('mainStrategySelector');     if (strategySelector) {       strategySelector.value = strategy;     }
- tegySelector');     if (strategySelector) {       strategySelector.value = strategy;     }          // Update helper

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- const strategySelector = document.getElementById('mainStrategySelector');     if (strategySelector) {       strategySele


## Backups\12.06 cant get lights back\js\ui\strategy-fix.js

### strategySelector (9 occurrences)

- ent) {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
-    // Get current DOM selected strategy     const strategySelector = document.getElementById('mainStrategySelector')
- ector');     let selectorStrategy = null;     if (strategySelector) {       selectorStrategy = strategySelector.valu
-  if (strategySelector) {       selectorStrategy = strategySelector.value;     }          // Determine correct strate
- gy;          // Update selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) { 
- te selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) {       strategySelect
- rategySelector.value !== currentStrategy) {       strategySelector.value = currentStrategy;     }          console.l

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- const strategySelector = document.getElementById('mainStrategySelector');     let selectorStrategy = null;     if (strat


## Backups\12.06 cant get lights back\js\ui\strategy-logic.js

### mainStrategySelector (4 occurrences)

- dateStrategyMenu = function() {   try {     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.value = currentStra
- Selector');     if (mainStrategySelector) {       mainStrategySelector.value = currentStrategy;     }     const mainStra


## Backups\12.06 cant get lights back\js\ui\strategy-persistence.js

### strategySelector (3 occurrences)

-  Get current strategy from selector         const strategySelector = document.getElementById('mainStrategySelector')
- tElementById('mainStrategySelector');         if (strategySelector) {           const selectedStrategy = strategySel
- egySelector) {           const selectedStrategy = strategySelector.value;                      // Save to localStora

### mainStrategySelector (5 occurrences)

- ;                  // Update the UI         const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');         if (mainStrategySelector) {           
- tElementById('mainStrategySelector');         if (mainStrategySelector) {           mainStrategySelector.value = savedSt
- ');         if (mainStrategySelector) {           mainStrategySelector.value = savedStrategy;           console.log('[St
- const strategySelector = document.getElementById('mainStrategySelector');         if (strategySelector) {           cons


## Backups\12.06 cant get lights back\js\ui\strategy-selector.js

### initializeStrategySelector (5 occurrences)

- ize strategy selector (single canonical) function initializeStrategySelector() {   // Close menu when clicking outside   funct
- tListener('DOMContentLoaded', () => {   try {     initializeStrategySelector()     if (typeof updateIndicatorMenu === 'functio
- al_toa', true)   } catch (e) {     console.error('initializeStrategySelector failed:', e)   } }) // Fallback: try to initializ
- lementById('strategySelector')) {     try {       initializeStrategySelector()       if (typeof updateIndicatorMenu === 'funct
- )     } catch (e) {       console.error('Fallback initializeStrategySelector failed:', e)     }   } }, 2000)  // Make function

### strategySelector (14 occurrences)

- = document.getElementById('strategyMenu')   const strategySelector = document.getElementById('strategySelector')   c
- const strategySelector = document.getElementById('strategySelector')   const applyStrategyButton = document.getEleme
- rading styles.         </div>         <select id="strategySelector" class="strategy-selector">           ${Object.en
- st newStrategySelector = document.getElementById('strategySelector')   const newApplyStrategyButton = document.getEl
- t mainStrategySelector = document.getElementById('strategySelector')   if (mainStrategySelector && window.currentStr
- gy function updateStrategyDescription() {   const strategySelector = document.getElementById('strategySelector')   c
- const strategySelector = document.getElementById('strategySelector')   const strategyDescription = document.getEleme
-  document.getElementById('strategyHelper')    if (strategySelector && strategyDescription && strategyHelper) {     c
- && strategyHelper) {     const selectedStrategy = strategySelector.value     const strategy = window.TRADING_STRATEG
- ) {   console.log('Applying strategy...')   const strategySelector = document.getElementById('strategySelector')   i
- const strategySelector = document.getElementById('strategySelector')   if (!strategySelector) {     console.error('S
- ocument.getElementById('strategySelector')   if (!strategySelector) {     console.error('Strategy selector not found
- ound')     return   }    const selectedStrategy = strategySelector.value   console.log('Selected strategy:', selecte
- etTimeout(() => {   if (!document.getElementById('strategySelector')) {     try {       initializeStrategySelector()

### strategy-selector (1 occurrences)

- div>         <select id="strategySelector" class="strategy-selector">           ${Object.entries(window.TRADING_STRAT

### mainStrategySelector (3 occurrences)

- playing current strategy description only   const mainStrategySelector = document.getElementById('strategySelector')   i
- document.getElementById('strategySelector')   if (mainStrategySelector && window.currentStrategy) {     // Set selector 
- ch current strategy without triggering change     mainStrategySelector.value = window.currentStrategy     updateStrategy

### strategyMenu (16 occurrences)

- nus() {     const menu = document.getElementById('strategyMenu')     if (menu && menu.classList.contains('open')
- t) => {     const menu = document.getElementById('strategyMenu')     if (menu && menu.classList.contains('open')
- document.getElementById('strategyButton')   const strategyMenu = document.getElementById('strategyMenu')   const
- )   const strategyMenu = document.getElementById('strategyMenu')   const strategySelector = document.getElementB
- / Create strategy menu if it doesn't exist   if (!strategyMenu) {     const menu = document.createElement('div')
- nu = document.createElement('div')     menu.id = 'strategyMenu'     menu.className = 'menu-content'     document
- isibility function toggleStrategyMenu() {   const strategyMenu = document.getElementById('strategyMenu')   if (s
- {   const strategyMenu = document.getElementById('strategyMenu')   if (strategyMenu) {     strategyMenu.style.di
- u = document.getElementById('strategyMenu')   if (strategyMenu) {     strategyMenu.style.display = strategyMenu.
- entById('strategyMenu')   if (strategyMenu) {     strategyMenu.style.display = strategyMenu.style.display === 'b
- (strategyMenu) {     strategyMenu.style.display = strategyMenu.style.display === 'block' ? 'none' : 'block'     
- s = document.querySelectorAll('.menu-content:not(#strategyMenu)')     otherMenus.forEach(menu => {       menu.st
- when new data arrives    // Hide the menu   const strategyMenu = document.getElementById('strategyMenu')   if (s
- u   const strategyMenu = document.getElementById('strategyMenu')   if (strategyMenu) strategyMenu.style.display 
- u = document.getElementById('strategyMenu')   if (strategyMenu) strategyMenu.style.display = 'none' }  // Update
- etElementById('strategyMenu')   if (strategyMenu) strategyMenu.style.display = 'none' }  // Update the indicator

### updateStrategyDescription (6 occurrences)

-    newStrategySelector.addEventListener('change', updateStrategyDescription)   }    if (newApplyStrategyButton) {     newAppl
- rategySelector.value = window.currentStrategy     updateStrategyDescription() // Only update description, no strategy switch 
- y description based on selected strategy function updateStrategyDescription() {   const strategySelector = document.getElemen
- r menu updated')   }    // Update strategy info   updateStrategyDescription()   if (typeof window.updateStrategyInfoPanel ===
- ow.toggleStrategyMenu = toggleStrategyMenu window.updateStrategyDescription = updateStrategyDescription; window.applySelected
- leStrategyMenu window.updateStrategyDescription = updateStrategyDescription; window.applySelectedStrategy = applySelectedStra


## Backups\12.06 cant get lights back\js\ui\timeframe-ui.js

### handleStrategyChange (2 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId);     });   }      initialize
- ignalManagerTimeframe();       }     });   }      handleStrategyChange(strategyId) {     console.log(`[TimeframeUI] Stra


## Backups\StarCrypt 01.06 slimming the errors down shit working better\index.html

### strategySelector (44 occurrences)

- nd: var(--text-color);     }     #tokenSelector, #strategySelector {       background: var(--secondary-bg);       co
- (0, 0, 0, 0.3);     }     #tokenSelector option, #strategySelector option {       background: var(--secondary-bg);  
- adding: 0.75rem;     }     #tokenSelector:hover, #strategySelector:hover {       background: rgba(0, 255, 255, 0.1);
-  255, 255, 0.5);     }     #tokenSelector:focus, #strategySelector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.8);     }      #strategySelector {       background-color: rgba(0, 100, 100, 0.3);
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-  {       // Get the strategy selector       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
-      // Update all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- lectorContainer.innerHTML = `         <label for="strategySelector">Select Signal Logic:</label>         <select id=
- >Select Signal Logic:</label>         <select id="strategySelector" class="strategy-selector">           ${Object.ke
- d event listener to strategy selector       const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');       if (strategySelector) {         strategy
- ent.getElementById('strategySelector');       if (strategySelector) {         strategySelector.addEventListener('cha
- Selector');       if (strategySelector) {         strategySelector.addEventListener('change', function() {          
- ', function() {           try {             const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');             if (!strategySelector) {          
- ElementById('strategySelector');             if (!strategySelector) {               logMessages.push(`[${new Date().
-           }              const selectedStrategy = strategySelector.value;             if (!selectedStrategy || !TRAD
- = `     <label>       Strategy:       <select id="strategySelector" class="strategy-selector">         <option value
- pdateLogger();       }       if (e.target.id === 'strategySelector') {         const strategy = e.target.value;     
-    // Initialize all strategy selectors     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector) {     
- const selectedStrategy = document.getElementById('strategySelector').value;          // Show strategy animation     
- ate all strategy selectors to match         const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.
- '#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => {           if (selector) { 
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-    }    // Initialize strategy selector     const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');     if (strategySelector) {       // Set initi
- ument.getElementById('strategySelector');     if (strategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       strategySelector.value = currentStrategy;        strategySelector.
-  strategySelector.value = currentStrategy;        strategySelector.addEventListener('change', function() {         c

### strategy-selector (25 occurrences)

- i/timeframe-ui.js"></script>   <script src="js/ui/strategy-selector.js"></script>   <script src="js/ui/strategy-logic
- }     }      /* Strategy selector styling */     .strategy-selector {       background: var(--secondary-bg);       co
- no-repeat;       padding-right: 30px;     }      .strategy-selector:hover {       box-shadow: 0 0 15px rgba(0, 255, 2
-      border-color: var(--text-color);     }      .strategy-selector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.7);     }      .strategy-selector option {       background: var(--secondary-bg);  
- color: #FFF;   box-shadow: 0 0 18px #00FFFF99; } .strategy-selector {   font-family: 'Orbitron', sans-serif;   font-s
- w: 0 2px 12px #00FFFF22;   margin-right: 1rem; } .strategy-selector option {   background-color: #0a0a1a;   color: #0
- slateY(-2px); }  /* Strategy selector styling */ .strategy-selector {   background: linear-gradient(135deg, rgba(0, 2
- on: right 10px center;   padding-right: 30px; }  .strategy-selector:hover {   background-color: rgba(0, 255, 255, 0.1
- 55, 255, 0.5);   transform: translateY(-2px); }  .strategy-selector:focus {   border-color: #FF00FF;   box-shadow: 0 
-  box-shadow: 0 0 20px rgba(255, 0, 255, 0.5); }  .strategy-selector option {   background-color: #0a0a1a;   color: #0
-   }    /* Enhanced strategy selector styles */   .strategy-selector-container {     margin: 15px 0;     position: rel
-   margin: 15px 0;     position: relative;   }    .strategy-selector {     width: 100%;     padding: 10px;     backgro
- x; /* Limit height to prevent overflow */   }    .strategy-selector:hover {     border-color: rgba(0, 255, 255, 0.8);
- -shadow: 0 0 10px rgba(0, 255, 255, 0.3);   }    .strategy-selector option {     background-color: #0a1525;     color
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
- ment('div');       selectorContainer.className = 'strategy-selector-container';       selectorContainer.innerHTML = `
- bel>         <select id="strategySelector" class="strategy-selector">           ${Object.keys(TRADING_STRATEGIES).map
- ategy:       <select id="strategySelector" class="strategy-selector">         <option value="admiral_toa">Admiral TOA
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
- js/ui/animations.js"></script> <script src="js/ui/strategy-selector.js"></script> <script src="js/ui/update-signal-li

### mainStrategySelector (14 occurrences)

- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- e strategy selector with event listener     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('c
- Selector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('change', function() {         /
-    // Initialize main strategy selector     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set i
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       mainStrategySelector.value = currentStrategy;     }      // Initialize
-      const newStrategy = document.getElementById('mainStrategySelector').value;         if (newStrategy !== currentStrat

### strategyMenu (3 occurrences)

-  Strategy and Timeframe Controls -->     <div id="strategyMenu" class="menu-content" style="display: none;">    
- ) {         const menu = document.getElementById('strategyMenu');         if (menu) {           menu.classList.t
-  strategy menu           document.getElementById('strategyMenu').classList.remove('active');            // Log t


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\global-variables.js

### strategySelector (3 occurrences)

- t mainStrategySelector = document.getElementById('strategySelector') ||                               document.getEl
- / Update all strategy selectors in the UI   const strategySelectors = document.querySelectorAll('#mainStrategySelect
- ll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     if (selector && select

### strategy-selector (1 occurrences)

- ocument.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     i

### mainStrategySelector (6 occurrences)

- l strategy selector value from our global   const mainStrategySelector = document.getElementById('strategySelector') || 
-                          document.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategyS
- ment.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.curren
- ategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.currentStrategy     // Trigger str
- n') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  // Indicators window.INDICATORS 
- t strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach

### handleStrategyChange (2 occurrences)

- change to ensure UI updates     if (typeof window.handleStrategyChange === 'function') {       window.handleStrategyChan
- ndleStrategyChange === 'function') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  /


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\main.js

### initializeStrategySelector (2 occurrences)

-  Initialize strategy selector   if (typeof window.initializeStrategySelector === 'function') {     window.initializeStrategySe
- lizeStrategySelector === 'function') {     window.initializeStrategySelector()     console.log('Strategy selector initialized'

### mainStrategySelector (1 occurrences)

-   const strategySelect = document.getElementById('mainStrategySelector')   const applyStrategyBtn = document.getElementB


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\strategy-manager.js

### strategyMenu (8 occurrences)

- = null     this.strategyContainer = null     this.strategyMenu = null      // Timeframe state     this.timeframe
- ument.getElementById('strategyControls')     this.strategyMenu = document.getElementById('strategyMenu')     thi
-      this.strategyMenu = document.getElementById('strategyMenu')     this.timeframeContainer = document.getEleme
- trols')      if (!this.strategyContainer || !this.strategyMenu || !this.timeframeContainer) {       console.erro
-     }   }    renderStrategyMenu() {     if (!this.strategyMenu) return      const strategyOptions = Object.entri
-           </option>         `).join('')      this.strategyMenu.innerHTML = `             <div class="strategy-co
-      `   }    toggleStrategyMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open
- yMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open')     }   }    handleStrat

### handleStrategyChange (3 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId)     })      // WebSocket con
- "strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">                     ${strategyOptio
- trategyMenu.classList.toggle('open')     }   }    handleStrategyChange(strategyId) {     if (!this.strategies[strategyId

### updateStrategyDescription (2 occurrences)

-  Update UI     this.renderStrategyMenu()     this.updateStrategyDescription()      // Update indicators     this.updateIndica
-      this.notifyStrategyChange(strategyId)   }    updateStrategyDescription() {     const description = this.getStrategyDescr


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\event-handlers.js

### strategySelector (1 occurrences)

- teLogger()         }         if (e.target.id === 'strategySelector') {           const strategy = e.target.value    


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\menu-handler.js

### strategyMenu (2 occurrences)

- ) {     this.activeMenu = null     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
-    e.stopPropagation()           this.toggleMenu('strategyMenu')           return         }          // Indicato


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\menu-strategy-fix.js

### strategySelector (6 occurrences)

-  Fix the strategy selector change event     const strategySelector = document.getElementById('mainStrategySelector')
- nt.getElementById('mainStrategySelector')     if (strategySelector) {       // Clone to remove old listeners       c
- to remove old listeners       const newSelector = strategySelector.cloneNode(true)       if (strategySelector.parent
- ctor = strategySelector.cloneNode(true)       if (strategySelector.parentNode) {         strategySelector.parentNode
-        if (strategySelector.parentNode) {         strategySelector.parentNode.replaceChild(newSelector, strategySele
- tegySelector.parentNode.replaceChild(newSelector, strategySelector)       }        // Add preview-only behavior on c

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')     if (strategySelector) {       // Clone to r
-         const selector = document.getElementById('mainStrategySelector')         if (!selector) return          const st

### strategyMenu (4 occurrences)

- gy)          // Close strategy menu         const strategyMenu = document.getElementById('strategyMenu')        
-     const strategyMenu = document.getElementById('strategyMenu')         if (strategyMenu) {           strategyM
- cument.getElementById('strategyMenu')         if (strategyMenu) {           strategyMenu.style.display = 'none' 
- ategyMenu')         if (strategyMenu) {           strategyMenu.style.display = 'none'         }       })     }  


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-comprehensive-fix.js

### strategySelector (6 occurrences)

- ) => {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- getElementById('mainStrategySelector')       if (!strategySelector) {         console.error('[StrategyFix] Strategy 
-    return       }        const selectedStrategy = strategySelector.value       console.log('[StrategyFix] Applying s
- ntStrategy = strategy      // Update UI     const strategySelector = document.getElementById('mainStrategySelector')
- nt.getElementById('mainStrategySelector')     if (strategySelector) {       strategySelector.value = strategy     } 
- ategySelector')     if (strategySelector) {       strategySelector.value = strategy     }      // Update helper and 

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')       if (!strategySelector) {         console.
- const strategySelector = document.getElementById('mainStrategySelector')     if (strategySelector) {       strategySelec


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-fix.js

### strategySelector (9 occurrences)

- ent) {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- getElementById('mainStrategySelector')       if (!strategySelector) {         console.error('Strategy selector not f
-    return       }        const selectedStrategy = strategySelector.value       console.log('Applying strategy:', sel
-    // Get current DOM selected strategy     const strategySelector = document.getElementById('mainStrategySelector')
- elector')     let selectorStrategy = null     if (strategySelector) {       selectorStrategy = strategySelector.valu
-  if (strategySelector) {       selectorStrategy = strategySelector.value     }      // Determine correct strategy   
- trategy      // Update selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) { 
- te selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) {       strategySelect
- rategySelector.value !== currentStrategy) {       strategySelector.value = currentStrategy     }      console.log('G

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')       if (!strategySelector) {         console.
- const strategySelector = document.getElementById('mainStrategySelector')     let selectorStrategy = null     if (strateg


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-logic.js

### mainStrategySelector (4 occurrences)

- ateStrategyMenu = function () {   try {     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrat
- nt.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStra
- ySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStrategy     }     const mainStrat


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-persistence.js

### strategySelector (3 occurrences)

-  Get current strategy from selector         const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector')         if (strategySelector) {           const selectedStrategy = strategySel
- egySelector) {           const selectedStrategy = strategySelector.value            // Save to localStorage         

### mainStrategySelector (5 occurrences)

- Strategy)          // Update the UI         const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')         if (mainStrategySelector) {           m
- etElementById('mainStrategySelector')         if (mainStrategySelector) {           mainStrategySelector.value = savedSt
- r')         if (mainStrategySelector) {           mainStrategySelector.value = savedStrategy           console.log('[Str
- const strategySelector = document.getElementById('mainStrategySelector')         if (strategySelector) {           const


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-selector.js

### initializeStrategySelector (6 occurrences)

- ize strategy selector (single canonical) function initializeStrategySelector() {   // Close menu when clicking outside   funct
-  }, 1000); }  // Initialize on DOM ready function initializeStrategySelectorModule() {   // Wait a bit to ensure all elements 
-               // Initialize the selector UI       initializeStrategySelector();              // Initialize strategy selectors 
-  } }  // Make functions available globally window.initializeStrategySelector = initializeStrategySelector; window.applySelecte
- able globally window.initializeStrategySelector = initializeStrategySelector; window.applySelectedStrategy = applySelectedStra
-  Initialize the main strategy selector module     initializeStrategySelectorModule();          // Attach indicator menu handle

### strategySelector (9 occurrences)

- egy     const selector = document.getElementById('strategySelector') || document.getElementById('mainStrategySelecto
-  document.getElementById('strategyMenu');   const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');   const applyStrategyButton = document.getElem
- rading styles.         </div>         <select id="strategySelector" class="strategy-selector">           ${Object.en
- st newStrategySelector = document.getElementById('strategySelector');   const newApplyStrategyButton = document.getE
-      // Initialize strategy selectors       const strategySelectors = [         document.getElementById('strategySel
- egySelectors = [         document.getElementById('strategySelector'),         document.getElementById('mainStrategyS
- // Set the selected value for all selectors       strategySelectors.forEach(selector => {         if (selector) {   
- y);     const selector = document.getElementById('strategySelector') ||                     document.getElementById(

### strategy-selector (1 occurrences)

- div>         <select id="strategySelector" class="strategy-selector">           ${Object.entries(window.TRADING_STRAT

### mainStrategySelector (3 occurrences)

- d('strategySelector') || document.getElementById('mainStrategySelector');     if (selector) {       selector.value = win
- ategySelector'),         document.getElementById('mainStrategySelector')       ].filter(Boolean);              // Set th
- ) ||                     document.getElementById('mainStrategySelector');          if (selector) {       selector.value 

### strategyMenu (14 occurrences)

- nus() {     const menu = document.getElementById('strategyMenu');     if (menu && menu.classList.contains('open'
- vent) {     const menu = document.getElementById('strategyMenu');     if (menu && menu.classList.contains('open'
- ocument.getElementById('strategyButton');   const strategyMenu = document.getElementById('strategyMenu');   cons
- ;   const strategyMenu = document.getElementById('strategyMenu');   const strategySelector = document.getElement
- / Create strategy menu if it doesn't exist   if (!strategyMenu) {     const menu = document.createElement('div')
- u = document.createElement('div');     menu.id = 'strategyMenu';     menu.className = 'menu-content';     docume
- isibility function toggleStrategyMenu() {   const strategyMenu = document.getElementById('strategyMenu');   if (
- {   const strategyMenu = document.getElementById('strategyMenu');   if (strategyMenu) {     strategyMenu.style.d
-  = document.getElementById('strategyMenu');   if (strategyMenu) {     strategyMenu.style.display = strategyMenu.
- ntById('strategyMenu');   if (strategyMenu) {     strategyMenu.style.display = strategyMenu.style.display === 'b
- (strategyMenu) {     strategyMenu.style.display = strategyMenu.style.display === 'block' ? 'none' : 'block';    
- s = document.querySelectorAll('.menu-content:not(#strategyMenu)');     otherMenus.forEach(menu => {       menu.s
- () => {     const menu = document.getElementById('strategyMenu');     if (menu) menu.style.display = 'none';   }
- () => {     const menu = document.getElementById('strategyMenu');     if (menu) menu.style.display = 'none';   }

### handleStrategyChange (8 occurrences)

- ;   } }  // Handle strategy change event function handleStrategyChange(event) {   const strategyId = event.target.value;
- s   notifyStrategyChange(strategyId); }  function handleStrategyChange(event) {   const strategyId = event.target.value;
-            selector.removeEventListener('change', handleStrategyChange);           selector.addEventListener('change', h
- e);           selector.addEventListener('change', handleStrategyChange);         }       });              // Initialize 
- gyDescription = updateStrategyDescription; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI =
- trategyDescription; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI = updateStrategyUI; wind
- tifyStrategyChange = notifyStrategyChange; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI =
- tifyStrategyChange; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI = updateStrategyUI; wind

### updateStrategyDescription (14 occurrences)

-    selector.value = window.currentStrategy;       updateStrategyDescription();     }   }    document.addEventListener('moused
-    newStrategySelector.addEventListener('change', updateStrategyDescription);   }      if (newApplyStrategyButton) {     newA
- egy   updateStrategyUI(window.currentStrategy);   updateStrategyDescription(); }  // Toggle strategy menu visibility function
- y description based on selected strategy function updateStrategyDescription() {   try {     const strategyId = window.current
- ategyUI(strategyId);      // Update description   updateStrategyDescription();      // Notify other components   notifyStrate
-  strategyId;      // Update strategy info panel   updateStrategyDescription();      // Update indicators for the new strategy
- ();     }   }, 100);      // Update description   updateStrategyDescription();      // Show success message   showStatusMessa
- ategyUI(strategyId);      // Update description   updateStrategyDescription();      // Show success message   showStatusMessa
-   updateStrategyUI(window.currentStrategy);       updateStrategyDescription();              // Update indicators for the curr
- ySelectedStrategy = applySelectedStrategy; window.updateStrategyDescription = updateStrategyDescription; window.handleStrateg
- ectedStrategy; window.updateStrategyDescription = updateStrategyDescription; window.handleStrategyChange = handleStrategyChan
- sole.log('Updating strategy description...');     updateStrategyDescription();          console.log('Strategy module fully in
- w.toggleStrategyMenu = toggleStrategyMenu; window.updateStrategyDescription = updateStrategyDescription; window.applySelected
- eStrategyMenu; window.updateStrategyDescription = updateStrategyDescription; window.applySelectedStrategy = applySelectedStra


## Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\timeframe-ui.js

### handleStrategyChange (2 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail.strategyId)     })   }    initializeWebS
- ateSignalManagerTimeframe()       }     })   }    handleStrategyChange(strategyId) {     console.log(`[TimeframeUI] Stra


## Backups\StarCrypt 21.05\index.html

### strategySelector (47 occurrences)

- nd: var(--text-color);     }     #tokenSelector, #strategySelector {       background: var(--secondary-bg);       co
- (0, 0, 0, 0.3);     }     #tokenSelector option, #strategySelector option {       background: var(--secondary-bg);  
- adding: 0.75rem;     }     #tokenSelector:hover, #strategySelector:hover {       background: rgba(0, 255, 255, 0.1);
-  255, 255, 0.5);     }     #tokenSelector:focus, #strategySelector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.8);     }      #strategySelector {       background-color: rgba(0, 100, 100, 0.3);
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-  {       // Get the strategy selector       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
-      // Update all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- lectorContainer.innerHTML = `         <label for="strategySelector">Select Signal Logic:</label>         <select id=
- >Select Signal Logic:</label>         <select id="strategySelector" class="strategy-selector">           ${Object.ke
- d event listener to strategy selector       const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');       if (strategySelector) {         strategy
- ent.getElementById('strategySelector');       if (strategySelector) {         strategySelector.addEventListener('cha
- Selector');       if (strategySelector) {         strategySelector.addEventListener('change', function() {          
- ', function() {           try {             const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');             if (!strategySelector) {          
- ElementById('strategySelector');             if (!strategySelector) {               logMessages.push(`[${new Date().
-           }              const selectedStrategy = strategySelector.value;             if (!selectedStrategy || !TRAD
- = `     <label>       Strategy:       <select id="strategySelector" class="strategy-selector">         <option value
- pdateLogger();       }       if (e.target.id === 'strategySelector') {         const strategy = e.target.value;     
-    // Initialize all strategy selectors     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector) {     
-    // Initialize all strategy selectors     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector) {     
- const selectedStrategy = document.getElementById('strategySelector').value;          // Show strategy animation     
- ate all strategy selectors to match         const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.
- '#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => {           if (selector) { 
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-    }    // Initialize strategy selector     const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');     if (strategySelector) {       // Set initi
- ument.getElementById('strategySelector');     if (strategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       strategySelector.value = currentStrategy;        strategySelector.
-  strategySelector.value = currentStrategy;        strategySelector.addEventListener('change', function() {         c

### strategy-selector (28 occurrences)

- i/timeframe-ui.js"></script>   <script src="js/ui/strategy-selector.js"></script>   <script src="js/ui/strategy-logic
- }     }      /* Strategy selector styling */     .strategy-selector {       background: var(--secondary-bg);       co
- no-repeat;       padding-right: 30px;     }      .strategy-selector:hover {       box-shadow: 0 0 15px rgba(0, 255, 2
-      border-color: var(--text-color);     }      .strategy-selector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.7);     }      .strategy-selector option {       background: var(--secondary-bg);  
- color: #FFF;   box-shadow: 0 0 18px #00FFFF99; } .strategy-selector {   font-family: 'Orbitron', sans-serif;   font-s
- w: 0 2px 12px #00FFFF22;   margin-right: 1rem; } .strategy-selector option {   background-color: #0a0a1a;   color: #0
- slateY(-2px); }  /* Strategy selector styling */ .strategy-selector {   background: linear-gradient(135deg, rgba(0, 2
- on: right 10px center;   padding-right: 30px; }  .strategy-selector:hover {   background-color: rgba(0, 255, 255, 0.1
- 55, 255, 0.5);   transform: translateY(-2px); }  .strategy-selector:focus {   border-color: #FF00FF;   box-shadow: 0 
-  box-shadow: 0 0 20px rgba(255, 0, 255, 0.5); }  .strategy-selector option {   background-color: #0a0a1a;   color: #0
- ading styles.         </div>          <div class="strategy-selector-container">           <label for="mainStrategySel
-          <select id="mainStrategySelector" class="strategy-selector">             <optgroup label="Core Strategies"> 
-   }    /* Enhanced strategy selector styles */   .strategy-selector-container {     margin: 15px 0;     position: rel
-   margin: 15px 0;     position: relative;   }    .strategy-selector {     width: 100%;     padding: 10px;     backgro
- x; /* Limit height to prevent overflow */   }    .strategy-selector:hover {     border-color: rgba(0, 255, 255, 0.8);
- -shadow: 0 0 10px rgba(0, 255, 255, 0.3);   }    .strategy-selector option {     background-color: #0a1525;     color
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
- ment('div');       selectorContainer.className = 'strategy-selector-container';       selectorContainer.innerHTML = `
- bel>         <select id="strategySelector" class="strategy-selector">           ${Object.keys(TRADING_STRATEGIES).map
- ategy:       <select id="strategySelector" class="strategy-selector">         <option value="admiral_toa">Admiral TOA
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
- js/ui/animations.js"></script> <script src="js/ui/strategy-selector.js"></script> <script src="js/ui/update-signal-li

### mainStrategySelector (16 occurrences)

- rategy-selector-container">           <label for="mainStrategySelector">Choose Strategy:</label>           <select id="m
- r">Choose Strategy:</label>           <select id="mainStrategySelector" class="strategy-selector">             <optgroup
- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- e strategy selector with event listener     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('c
- Selector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('change', function() {         /
-    // Initialize main strategy selector     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set i
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       mainStrategySelector.value = currentStrategy;     }      // Initialize
-      const newStrategy = document.getElementById('mainStrategySelector').value;         if (newStrategy !== currentStrat

### strategyMenu (3 occurrences)

- ct>     </div>      <div class="menu-content" id="strategyMenu">       <div class="strategy-controls" id="strate
- ) {         const menu = document.getElementById('strategyMenu');         if (menu) {           menu.classList.t
-  strategy menu           document.getElementById('strategyMenu').classList.remove('active');            // Log t


## Backups\StarCrypt 21.05\js\global-variables.js

### strategySelector (2 occurrences)

- / Update all strategy selectors in the UI   const strategySelectors = document.querySelectorAll('#mainStrategySelect
- ll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     if (selector && select

### strategy-selector (1 occurrences)

- ocument.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     i

### mainStrategySelector (5 occurrences)

- l strategy selector value from our global   const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategyS
- ment.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.curren
- ategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.currentStrategy     // DO NOT auto
- t strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach


## Backups\StarCrypt 21.05\js\main.js

### initializeStrategySelector (2 occurrences)

-  Initialize strategy selector   if (typeof window.initializeStrategySelector === 'function') {     window.initializeStrategySe
- lizeStrategySelector === 'function') {     window.initializeStrategySelector()     console.log('Strategy selector initialized'

### mainStrategySelector (1 occurrences)

-   const strategySelect = document.getElementById('mainStrategySelector') // Using correct ID from HTML   if (strategySel


## Backups\StarCrypt 21.05\js\ui\event-handlers.js

### strategySelector (1 occurrences)

- teLogger()         }         if (e.target.id === 'strategySelector') {           const strategy = e.target.value    


## Backups\StarCrypt 21.05\js\ui\menu-handler.js

### strategyMenu (2 occurrences)

- ) {     this.activeMenu = null     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
-    e.stopPropagation()           this.toggleMenu('strategyMenu')           return         }          // Indicato


## Backups\StarCrypt 21.05\js\ui\menu-strategy-fix.js

### strategySelector (6 occurrences)

-  Fix the strategy selector change event     const strategySelector = document.getElementById('mainStrategySelector')
- nt.getElementById('mainStrategySelector')     if (strategySelector) {       // Clone to remove old listeners       c
- to remove old listeners       const newSelector = strategySelector.cloneNode(true)       if (strategySelector.parent
- ctor = strategySelector.cloneNode(true)       if (strategySelector.parentNode) {         strategySelector.parentNode
-        if (strategySelector.parentNode) {         strategySelector.parentNode.replaceChild(newSelector, strategySele
- tegySelector.parentNode.replaceChild(newSelector, strategySelector)       }        // Add preview-only behavior on c

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')     if (strategySelector) {       // Clone to r
-         const selector = document.getElementById('mainStrategySelector')         if (!selector) return          const st

### strategyMenu (4 occurrences)

- gy)          // Close strategy menu         const strategyMenu = document.getElementById('strategyMenu')        
-     const strategyMenu = document.getElementById('strategyMenu')         if (strategyMenu) {           strategyM
- cument.getElementById('strategyMenu')         if (strategyMenu) {           strategyMenu.style.display = 'none' 
- ategyMenu')         if (strategyMenu) {           strategyMenu.style.display = 'none'         }       })     }  


## Backups\StarCrypt 21.05\js\ui\strategy-comprehensive-fix.js

### strategySelector (6 occurrences)

- ) => {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- getElementById('mainStrategySelector')       if (!strategySelector) {         console.error('[StrategyFix] Strategy 
-    return       }        const selectedStrategy = strategySelector.value       console.log('[StrategyFix] Applying s
- ntStrategy = strategy      // Update UI     const strategySelector = document.getElementById('mainStrategySelector')
- nt.getElementById('mainStrategySelector')     if (strategySelector) {       strategySelector.value = strategy     } 
- ategySelector')     if (strategySelector) {       strategySelector.value = strategy     }      // Update helper and 

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')       if (!strategySelector) {         console.
- const strategySelector = document.getElementById('mainStrategySelector')     if (strategySelector) {       strategySelec


## Backups\StarCrypt 21.05\js\ui\strategy-fix.js

### strategySelector (9 occurrences)

- ent) {       // Get selected strategy       const strategySelector = document.getElementById('mainStrategySelector')
- getElementById('mainStrategySelector')       if (!strategySelector) {         console.error('Strategy selector not f
-    return       }        const selectedStrategy = strategySelector.value       console.log('Applying strategy:', sel
-    // Get current DOM selected strategy     const strategySelector = document.getElementById('mainStrategySelector')
- elector')     let selectorStrategy = null     if (strategySelector) {       selectorStrategy = strategySelector.valu
-  if (strategySelector) {       selectorStrategy = strategySelector.value     }      // Determine correct strategy   
- trategy      // Update selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) { 
- te selector if needed     if (strategySelector && strategySelector.value !== currentStrategy) {       strategySelect
- rategySelector.value !== currentStrategy) {       strategySelector.value = currentStrategy     }      console.log('G

### mainStrategySelector (2 occurrences)

- const strategySelector = document.getElementById('mainStrategySelector')       if (!strategySelector) {         console.
- const strategySelector = document.getElementById('mainStrategySelector')     let selectorStrategy = null     if (strateg


## Backups\StarCrypt 21.05\js\ui\strategy-logic.js

### mainStrategySelector (4 occurrences)

- ateStrategyMenu = function () {   try {     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrat
- nt.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStra
- ySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStrategy     }     const mainStrat


## Backups\StarCrypt 21.05\js\ui\strategy-persistence.js

### strategySelector (3 occurrences)

-  Get current strategy from selector         const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector')         if (strategySelector) {           const selectedStrategy = strategySel
- egySelector) {           const selectedStrategy = strategySelector.value            // Save to localStorage         

### mainStrategySelector (5 occurrences)

- Strategy)          // Update the UI         const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')         if (mainStrategySelector) {           m
- etElementById('mainStrategySelector')         if (mainStrategySelector) {           mainStrategySelector.value = savedSt
- r')         if (mainStrategySelector) {           mainStrategySelector.value = savedStrategy           console.log('[Str
- const strategySelector = document.getElementById('mainStrategySelector')         if (strategySelector) {           const


## Backups\StarCrypt 21.05\js\ui\strategy-selector.js

### initializeStrategySelector (5 occurrences)

- ize strategy selector (single canonical) function initializeStrategySelector() {   // Close menu when clicking outside   funct
- tListener('DOMContentLoaded', () => {   try {     initializeStrategySelector()     if (typeof updateIndicatorMenu === 'functio
- al_toa', true)   } catch (e) {     console.error('initializeStrategySelector failed:', e)   } }) // Fallback: try to initializ
- lementById('strategySelector')) {     try {       initializeStrategySelector()       if (typeof updateIndicatorMenu === 'funct
- )     } catch (e) {       console.error('Fallback initializeStrategySelector failed:', e)     }   } }, 2000)  // Make function

### strategySelector (14 occurrences)

- = document.getElementById('strategyMenu')   const strategySelector = document.getElementById('strategySelector')   c
- const strategySelector = document.getElementById('strategySelector')   const applyStrategyButton = document.getEleme
- rading styles.         </div>         <select id="strategySelector" class="strategy-selector">           ${Object.en
- st newStrategySelector = document.getElementById('strategySelector')   const newApplyStrategyButton = document.getEl
- t mainStrategySelector = document.getElementById('strategySelector')   if (mainStrategySelector && window.currentStr
- gy function updateStrategyDescription() {   const strategySelector = document.getElementById('strategySelector')   c
- const strategySelector = document.getElementById('strategySelector')   const strategyDescription = document.getEleme
-  document.getElementById('strategyHelper')    if (strategySelector && strategyDescription && strategyHelper) {     c
- && strategyHelper) {     const selectedStrategy = strategySelector.value     const strategy = window.TRADING_STRATEG
- ) {   console.log('Applying strategy...')   const strategySelector = document.getElementById('strategySelector')   i
- const strategySelector = document.getElementById('strategySelector')   if (!strategySelector) {     console.error('S
- ocument.getElementById('strategySelector')   if (!strategySelector) {     console.error('Strategy selector not found
- ound')     return   }    const selectedStrategy = strategySelector.value   console.log('Selected strategy:', selecte
- etTimeout(() => {   if (!document.getElementById('strategySelector')) {     try {       initializeStrategySelector()

### strategy-selector (1 occurrences)

- div>         <select id="strategySelector" class="strategy-selector">           ${Object.entries(window.TRADING_STRAT

### mainStrategySelector (3 occurrences)

- playing current strategy description only   const mainStrategySelector = document.getElementById('strategySelector')   i
- document.getElementById('strategySelector')   if (mainStrategySelector && window.currentStrategy) {     // Set selector 
- ch current strategy without triggering change     mainStrategySelector.value = window.currentStrategy     updateStrategy

### strategyMenu (16 occurrences)

- nus() {     const menu = document.getElementById('strategyMenu')     if (menu && menu.classList.contains('open')
- t) => {     const menu = document.getElementById('strategyMenu')     if (menu && menu.classList.contains('open')
- document.getElementById('strategyButton')   const strategyMenu = document.getElementById('strategyMenu')   const
- )   const strategyMenu = document.getElementById('strategyMenu')   const strategySelector = document.getElementB
- / Create strategy menu if it doesn't exist   if (!strategyMenu) {     const menu = document.createElement('div')
- nu = document.createElement('div')     menu.id = 'strategyMenu'     menu.className = 'menu-content'     document
- isibility function toggleStrategyMenu() {   const strategyMenu = document.getElementById('strategyMenu')   if (s
- {   const strategyMenu = document.getElementById('strategyMenu')   if (strategyMenu) {     strategyMenu.style.di
- u = document.getElementById('strategyMenu')   if (strategyMenu) {     strategyMenu.style.display = strategyMenu.
- entById('strategyMenu')   if (strategyMenu) {     strategyMenu.style.display = strategyMenu.style.display === 'b
- (strategyMenu) {     strategyMenu.style.display = strategyMenu.style.display === 'block' ? 'none' : 'block'     
- s = document.querySelectorAll('.menu-content:not(#strategyMenu)')     otherMenus.forEach(menu => {       menu.st
- when new data arrives    // Hide the menu   const strategyMenu = document.getElementById('strategyMenu')   if (s
- u   const strategyMenu = document.getElementById('strategyMenu')   if (strategyMenu) strategyMenu.style.display 
- u = document.getElementById('strategyMenu')   if (strategyMenu) strategyMenu.style.display = 'none' }  // Update
- etElementById('strategyMenu')   if (strategyMenu) strategyMenu.style.display = 'none' }  // Update the indicator

### updateStrategyDescription (6 occurrences)

-    newStrategySelector.addEventListener('change', updateStrategyDescription)   }    if (newApplyStrategyButton) {     newAppl
- rategySelector.value = window.currentStrategy     updateStrategyDescription() // Only update description, no strategy switch 
- y description based on selected strategy function updateStrategyDescription() {   const strategySelector = document.getElemen
- r menu updated')   }    // Update strategy info   updateStrategyDescription()   if (typeof window.updateStrategyInfoPanel ===
- ow.toggleStrategyMenu = toggleStrategyMenu window.updateStrategyDescription = updateStrategyDescription window.applySelectedS
- leStrategyMenu window.updateStrategyDescription = updateStrategyDescription window.applySelectedStrategy = applySelectedStrat


## Backups\StarCrypt 25.06 success\index.html

### strategySelector (44 occurrences)

- nd: var(--text-color);     }     #tokenSelector, #strategySelector {       background: var(--secondary-bg);       co
- (0, 0, 0, 0.3);     }     #tokenSelector option, #strategySelector option {       background: var(--secondary-bg);  
- adding: 0.75rem;     }     #tokenSelector:hover, #strategySelector:hover {       background: rgba(0, 255, 255, 0.1);
-  255, 255, 0.5);     }     #tokenSelector:focus, #strategySelector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.8);     }      #strategySelector {       background-color: rgba(0, 100, 100, 0.3);
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-  {       // Get the strategy selector       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
-      // Update all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- lectorContainer.innerHTML = `         <label for="strategySelector">Select Signal Logic:</label>         <select id=
- >Select Signal Logic:</label>         <select id="strategySelector" class="strategy-selector">           ${Object.ke
- d event listener to strategy selector       const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');       if (strategySelector) {         strategy
- ent.getElementById('strategySelector');       if (strategySelector) {         strategySelector.addEventListener('cha
- Selector');       if (strategySelector) {         strategySelector.addEventListener('change', function() {          
- ', function() {           try {             const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');             if (!strategySelector) {          
- ElementById('strategySelector');             if (!strategySelector) {               logMessages.push(`[${new Date().
-           }              const selectedStrategy = strategySelector.value;             if (!selectedStrategy || !TRAD
- = `     <label>       Strategy:       <select id="strategySelector" class="strategy-selector">         <option value
- pdateLogger();       }       if (e.target.id === 'strategySelector') {         const strategy = e.target.value;     
-  // Initialize all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- const selectedStrategy = document.getElementById('strategySelector').value;          // Show strategy animation     
- ate all strategy selectors to match         const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.
- '#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => {           if (selector) { 
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-    }    // Initialize strategy selector     const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');     if (strategySelector) {       // Set initi
- ument.getElementById('strategySelector');     if (strategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       strategySelector.value = currentStrategy;        strategySelector.
-  strategySelector.value = currentStrategy;        strategySelector.addEventListener('change', function() {         c

### strategy-selector (26 occurrences)

- }     }      /* Strategy selector styling */     .strategy-selector {       background: var(--secondary-bg);       co
- no-repeat;       padding-right: 30px;     }      .strategy-selector:hover {       box-shadow: 0 0 15px rgba(0, 255, 2
-      border-color: var(--text-color);     }      .strategy-selector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.7);     }      .strategy-selector option {       background: var(--secondary-bg);  
- color: #FFF;   box-shadow: 0 0 18px #00FFFF99; } .strategy-selector {   font-family: 'Orbitron', sans-serif;   font-s
- w: 0 2px 12px #00FFFF22;   margin-right: 1rem; } .strategy-selector option {   background-color: #0a0a1a;   color: #0
- slateY(-2px); }  /* Strategy selector styling */ .strategy-selector {   background: linear-gradient(135deg, rgba(0, 2
- on: right 10px center;   padding-right: 30px; }  .strategy-selector:hover {   background-color: rgba(0, 255, 255, 0.1
- 55, 255, 0.5);   transform: translateY(-2px); }  .strategy-selector:focus {   border-color: #FF00FF;   box-shadow: 0 
-  box-shadow: 0 0 20px rgba(255, 0, 255, 0.5); }  .strategy-selector option {   background-color: #0a0a1a;   color: #0
-   }    /* Enhanced strategy selector styles */   .strategy-selector-container {     margin: 15px 0;     position: rel
-   margin: 15px 0;     position: relative;   }    .strategy-selector {     width: 100%;     padding: 10px;     backgro
- x; /* Limit height to prevent overflow */   }    .strategy-selector:hover {     border-color: rgba(0, 255, 255, 0.8);
- -shadow: 0 0 10px rgba(0, 255, 255, 0.3);   }    .strategy-selector option {     background-color: #0a1525;     color
- ading styles.         </div>          <div class="strategy-selector-container">           <label for="mainStrategySel
-          <select id="mainStrategySelector" class="strategy-selector">             <optgroup label="Core Strategies"> 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
- ment('div');       selectorContainer.className = 'strategy-selector-container';       selectorContainer.innerHTML = `
- bel>         <select id="strategySelector" class="strategy-selector">           ${Object.keys(TRADING_STRATEGIES).map
- ategy:       <select id="strategySelector" class="strategy-selector">         <option value="admiral_toa">Admiral TOA
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
- js/ui/animations.js"></script> <script src="js/ui/strategy-selector.js"></script> <script src="js/ui/update-signal-li

### mainStrategySelector (24 occurrences)

- rategy-selector-container">           <label for="mainStrategySelector">Choose Strategy:</label>           <select id="m
- r">Choose Strategy:</label>           <select id="mainStrategySelector" class="strategy-selector">             <optgroup
- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- e strategy selector with event listener     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('c
- Selector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('change', function() {         /
- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- n applySelectedStrategy() {     try {       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (!mainStrategySelector) {         log
- etElementById('mainStrategySelector');       if (!mainStrategySelector) {         logMessages.push(`[${new Date().toLoca
-   return;       }        const selectedStrategy = mainStrategySelector.value;       if (!selectedStrategy || !TRADING_ST
-    // Initialize main strategy selector     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set i
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       mainStrategySelector.value = currentStrategy;        // Add change eve
-  change event listener to update info panel       mainStrategySelector.addEventListener('change', function() {         c

### strategyMenu (11 occurrences)

-  */     #logicMenu {       top: 50px;     }      #strategyMenu {       top: 320px;     }      #indicatorMenu {  
-      <!-- Strategy Selector Menu -->     <div id="strategyMenu" class="menu-content">       <div class="strategy
- y);        // Close the strategy menu       const strategyMenu = document.getElementById('strategyMenu');       
-     const strategyMenu = document.getElementById('strategyMenu');       if (strategyMenu) {         strategyMenu
- ocument.getElementById('strategyMenu');       if (strategyMenu) {         strategyMenu.classList.remove('active'
- strategyMenu');       if (strategyMenu) {         strategyMenu.classList.remove('active');       }        // Sho
- emove('active');         document.getElementById('strategyMenu').classList.remove('active');         document.ge
-          e.stopPropagation();         toggleMenu('strategyMenu');         updateStrategyMenu();       });       
- ide any menu         const menus = ['logicMenu', 'strategyMenu', 'indicatorMenu', 'lightLogicMenu', 'thresholdsM
- st.remove('active');     document.getElementById('strategyMenu').classList.remove('active');     document.getEle
- ) {         const menu = document.getElementById('strategyMenu');         if (menu) {           // Scroll to str


## Backups\StarCrypt 25.06 success\js\global-variables.js

### strategySelector (3 occurrences)

- t mainStrategySelector = document.getElementById('strategySelector') ||                               document.getEl
- / Update all strategy selectors in the UI   const strategySelectors = document.querySelectorAll('#mainStrategySelect
- ll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     if (selector && select

### strategy-selector (1 occurrences)

- ocument.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     i

### mainStrategySelector (6 occurrences)

- l strategy selector value from our global   const mainStrategySelector = document.getElementById('strategySelector') || 
-                          document.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategyS
- ment.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.curren
- ategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.currentStrategy     // Trigger str
- n') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  // Indicators window.INDICATORS 
- t strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach

### handleStrategyChange (2 occurrences)

- change to ensure UI updates     if (typeof window.handleStrategyChange === 'function') {       window.handleStrategyChan
- ndleStrategyChange === 'function') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  /


## Backups\StarCrypt 25.06 success\js\main.js

### initializeStrategySelector (2 occurrences)

-  Initialize strategy selector   if (typeof window.initializeStrategySelector === 'function') {     window.initializeStrategySe
- lizeStrategySelector === 'function') {     window.initializeStrategySelector()     console.log('Strategy selector initialized'

### mainStrategySelector (1 occurrences)

-   const strategySelect = document.getElementById('mainStrategySelector')   const applyStrategyBtn = document.getElementB


## Backups\StarCrypt 25.06 success\js\strategy-manager.js

### strategyMenu (8 occurrences)

- = null     this.strategyContainer = null     this.strategyMenu = null     this.isHandlingStrategyChange = false 
- ument.getElementById('strategyControls')     this.strategyMenu = document.getElementById('strategyMenu')     thi
-      this.strategyMenu = document.getElementById('strategyMenu')     this.timeframeContainer = document.getEleme
- trols')      if (!this.strategyContainer || !this.strategyMenu || !this.timeframeContainer) {       console.erro
-     }   }    renderStrategyMenu() {     if (!this.strategyMenu) return      const strategyOptions = Object.entri
-           </option>         `).join('')      this.strategyMenu.innerHTML = `             <div class="strategy-co
-      `   }    toggleStrategyMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open
- yMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open')     }   }    handleStrat

### handleStrategyChange (3 occurrences)

- tail.source !== 'strategyManager') {         this.handleStrategyChange(e.detail.strategyId, true)       }     })      //
- "strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">                     ${strategyOptio
- trategyMenu.classList.toggle('open')     }   }    handleStrategyChange(strategyId, isInternalChange = false) {     if (!

### updateStrategyDescription (2 occurrences)

- ate UI       this.renderStrategyMenu()       this.updateStrategyDescription()        // Update indicators       this.updateIn
- his.isHandlingStrategyChange = false     }   }    updateStrategyDescription() {     const description = this.getStrategyDescr


## Backups\StarCrypt 25.06 success\js\ui\event-handlers.js

### strategySelector (1 occurrences)

- teLogger()         }         if (e.target.id === 'strategySelector') {           const strategy = e.target.value    


## Backups\StarCrypt 25.06 success\js\ui\menu-handler.js

### strategyMenu (3 occurrences)

-  {     this.activeMenu = null;     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
- st buttonToMenuMap = {         'strategyButton': 'strategyMenu',         'toggleMenuButton': 'indicatorMenu',   
-  first, then by ID     const buttonMap = {       'strategyMenu': { class: '.strategy-button', id: 'strategyButto


## Backups\StarCrypt 25.06 success\js\ui\strategy-logic.js

### mainStrategySelector (4 occurrences)

- ateStrategyMenu = function () {   try {     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrat
- nt.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStra
- ySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStrategy     }     const mainStrat


## Backups\StarCrypt 25.06 success\js\ui\strategy-persistence.js

### strategySelector (3 occurrences)

-  Get current strategy from selector         const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector')         if (strategySelector) {           const selectedStrategy = strategySel
- egySelector) {           const selectedStrategy = strategySelector.value            // Save to localStorage         

### mainStrategySelector (5 occurrences)

- Strategy)          // Update the UI         const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')         if (mainStrategySelector) {           m
- etElementById('mainStrategySelector')         if (mainStrategySelector) {           mainStrategySelector.value = savedSt
- r')         if (mainStrategySelector) {           mainStrategySelector.value = savedStrategy           console.log('[Str
- const strategySelector = document.getElementById('mainStrategySelector')         if (strategySelector) {           const


## Backups\StarCrypt 25.06 success\js\ui\strategy-selector.js

### initializeStrategySelector (8 occurrences)

- ize strategy selector (single canonical) function initializeStrategySelector() {   // Close menu when clicking outside   funct
- false;   } }  // Initialize on DOM ready function initializeStrategySelectorModule() {   console.log('Initializing strategy se
-               // Initialize the selector UI       initializeStrategySelector();              // Initialize strategy selectors 
-  } }  // Make functions available globally window.initializeStrategySelector = initializeStrategySelector; window.applySelecte
- able globally window.initializeStrategySelector = initializeStrategySelector; window.applySelectedStrategy = applySelectedStra
-  Initialize the main strategy selector module     initializeStrategySelectorModule();          // Attach indicator menu handle
- {   document.addEventListener('DOMContentLoaded', initializeStrategySelectorModule); } else {   initializeStrategySelectorModu
- d', initializeStrategySelectorModule); } else {   initializeStrategySelectorModule(); } window.updateIndicatorMenu = updateInd

### strategySelector (10 occurrences)

- egy     const selector = document.getElementById('strategySelector') || document.getElementById('mainStrategySelecto
-  document.getElementById('strategyMenu');   const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');   const applyStrategyButton = document.getElem
- rading styles.         </div>         <select id="strategySelector" class="strategy-selector">           ${Object.en
- st newStrategySelector = document.getElementById('strategySelector');   const newApplyStrategyButton = document.getE
-         const selector = document.getElementById('strategySelector');         if (selector) {           applySelecte
-      // Initialize strategy selectors       const strategySelectors = [         document.getElementById('strategySel
- egySelectors = [         document.getElementById('strategySelector'),         document.getElementById('mainStrategyS
- // Set the selected value for all selectors       strategySelectors.forEach(selector => {         if (selector) {   
- y);     const selector = document.getElementById('strategySelector') ||                     document.getElementById(

### strategy-selector (1 occurrences)

- div>         <select id="strategySelector" class="strategy-selector">           ${Object.entries(window.TRADING_STRAT

### mainStrategySelector (3 occurrences)

- d('strategySelector') || document.getElementById('mainStrategySelector');     if (selector) {       selector.value = win
- ategySelector'),         document.getElementById('mainStrategySelector')       ].filter(Boolean);              // Set th
- ) ||                     document.getElementById('mainStrategySelector');          if (selector) {       selector.value 

### strategyMenu (16 occurrences)

- nus() {     const menu = document.getElementById('strategyMenu');     if (menu && menu.classList.contains('open'
- vent) {     const menu = document.getElementById('strategyMenu');     if (menu && menu.classList.contains('open'
- ocument.getElementById('strategyButton');   const strategyMenu = document.getElementById('strategyMenu');   cons
- ;   const strategyMenu = document.getElementById('strategyMenu');   const strategySelector = document.getElement
- esn't exist   let menu = document.getElementById('strategyMenu');   if (!menu) {     menu = document.createEleme
- u = document.createElement('div');     menu.id = 'strategyMenu';     menu.className = 'menu-content';     menu.s
- ion();     event.preventDefault();   }      const strategyMenu = document.getElementById('strategyMenu');   if (
-     const strategyMenu = document.getElementById('strategyMenu');   if (strategyMenu) {     const isVisible = st
-  = document.getElementById('strategyMenu');   if (strategyMenu) {     const isVisible = strategyMenu.style.displ
- nu');   if (strategyMenu) {     const isVisible = strategyMenu.style.display === 'block';     strategyMenu.style
- ble = strategyMenu.style.display === 'block';     strategyMenu.style.display = isVisible ? 'none' : 'block';    
- st rect = button.getBoundingClientRect();         strategyMenu.style.top = `${rect.bottom + window.scrollY}px`; 
- op = `${rect.bottom + window.scrollY}px`;         strategyMenu.style.left = `${rect.left + window.scrollX}px`;  
- s = document.querySelectorAll('.menu-content:not(#strategyMenu)');     otherMenus.forEach(menu => {       menu.s
- () => {     const menu = document.getElementById('strategyMenu');     if (menu) menu.style.display = 'none';   }
- () => {     const menu = document.getElementById('strategyMenu');     if (menu) menu.style.display = 'none';   }

### handleStrategyChange (7 occurrences)

-   } }  // Handle strategy change event   function handleStrategyChange(event) {   const strategyId = event.target.value;
-            selector.removeEventListener('change', handleStrategyChange);           selector.addEventListener('change', h
- e);           selector.addEventListener('change', handleStrategyChange);         }       });              // Initialize 
- gyDescription = updateStrategyDescription; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI =
- trategyDescription; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI = updateStrategyUI; wind
- tifyStrategyChange = notifyStrategyChange; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI =
- tifyStrategyChange; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI = updateStrategyUI; wind

### updateStrategyDescription (14 occurrences)

-    selector.value = window.currentStrategy;       updateStrategyDescription();     }   }    document.addEventListener('moused
- newStrategySelector.removeEventListener('change', updateStrategyDescription);     newStrategySelector.addEventListener('chang
-    newStrategySelector.addEventListener('change', updateStrategyDescription);   }      if (newApplyStrategyButton) {     // R
- egy   updateStrategyUI(window.currentStrategy);   updateStrategyDescription(); }  // Toggle strategy menu visibility function
- y description based on selected strategy function updateStrategyDescription() {   try {     const strategyId = window.current
-  strategyId;      // Update strategy info panel   updateStrategyDescription();      // Update indicators for the new strategy
- ();     }   }, 100);      // Update description   updateStrategyDescription();      // Show success message   showStatusMessa
- ategyUI(strategyId);      // Update description   updateStrategyDescription();      // Update indicators for the new strategy
-   updateStrategyUI(window.currentStrategy);       updateStrategyDescription();              // Update indicators for the curr
- ySelectedStrategy = applySelectedStrategy; window.updateStrategyDescription = updateStrategyDescription; window.handleStrateg
- ectedStrategy; window.updateStrategyDescription = updateStrategyDescription; window.handleStrategyChange = handleStrategyChan
- sole.log('Updating strategy description...');     updateStrategyDescription();          console.log('Strategy module fully in
- w.toggleStrategyMenu = toggleStrategyMenu; window.updateStrategyDescription = updateStrategyDescription; window.applySelected
- eStrategyMenu; window.updateStrategyDescription = updateStrategyDescription; window.applySelectedStrategy = applySelectedStra


## Backups\StarCrypt 25.06 success\js\ui\threshold-sliders.js

### strategyMenu (1 occurrences)

- const menus = [       'threshold-sliders',       'strategyMenu',       'indicatorMenu',       'logicControls'   


## Backups\StarCrypt 25.06 success\src\strategy\strategy-manager.js

### strategyMenu (18 occurrences)

- ment.getElementById('strategyButton');       this.strategyMenu = document.getElementById('strategyMenu');       
-      this.strategyMenu = document.getElementById('strategyMenu');              if (!this.strategyButton || !this
- );              if (!this.strategyButton || !this.strategyMenu) {         console.warn('Strategy elements not fo
- ect if it doesn't exist       let selectEl = this.strategyMenu.querySelector('select');       if (!selectEl) {  
- dChild(option);         });                  this.strategyMenu.appendChild(selectEl);       }              // Cr
-  apply button if it doesn't exist       if (!this.strategyMenu.querySelector('.apply-strategy')) {         const
- yBtn.textContent = 'Apply Strategy';         this.strategyMenu.appendChild(applyBtn);       }              this.
-       // Apply strategy     const applyBtn = this.strategyMenu.querySelector('.apply-strategy');     if (applyBt
- addEventListener('click', (e) => {       if (this.strategyMenu && !this.strategyMenu.contains(e.target) &&      
- k', (e) => {       if (this.strategyMenu && !this.strategyMenu.contains(e.target) &&            this.strategyBut
- u visibility    */   toggleMenu() {     if (!this.strategyMenu) return;          const isVisible = this.strategy
- tegyMenu) return;          const isVisible = this.strategyMenu.classList.toggle('visible');          if (isVisib
- trategyButton.getBoundingClientRect();       this.strategyMenu.style.top = `${rect.bottom + window.scrollY}px`; 
- = `${rect.bottom + window.scrollY}px`;       this.strategyMenu.style.left = `${rect.left + window.scrollX}px`;  
- is.currentStrategy) {         const select = this.strategyMenu.querySelector('select');         if (select) {   
-  strategy menu    */   closeMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.remove('visi
- eMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.remove('visible');     }   }    /**    
- applySelectedStrategy() {     const select = this.strategyMenu?.querySelector('select');     if (!select) return


## generate-call-tree.js

### initializeStrategySelector (1 occurrences)

- h, 'utf8');                 if (content.includes('initializeStrategySelector') ||                      content.includes('strat

### strategySelector (1 occurrences)

- ector') ||                      content.includes('strategySelector') ||                      content.includes('strat

### strategy-selector (1 occurrences)

- ector') ||                      content.includes('strategy-selector')) {                     output += `- ${path} (${


## index.html

### strategySelector (44 occurrences)

- nd: var(--text-color);     }     #tokenSelector, #strategySelector {       background: var(--secondary-bg);       co
- (0, 0, 0, 0.3);     }     #tokenSelector option, #strategySelector option {       background: var(--secondary-bg);  
- adding: 0.75rem;     }     #tokenSelector:hover, #strategySelector:hover {       background: rgba(0, 255, 255, 0.1);
-  255, 255, 0.5);     }     #tokenSelector:focus, #strategySelector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.8);     }      #strategySelector {       background-color: rgba(0, 100, 100, 0.3);
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-  {       // Get the strategy selector       const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector');       if (!strategySelector) {         console.error('Strategy selector not f
- rn;       }              const selectedStrategy = strategySelector.value;       console.log('Applying strategy:', se
-      // Update all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- lectorContainer.innerHTML = `         <label for="strategySelector">Select Signal Logic:</label>         <select id=
- >Select Signal Logic:</label>         <select id="strategySelector" class="strategy-selector">           ${Object.ke
- d event listener to strategy selector       const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');       if (strategySelector) {         strategy
- ent.getElementById('strategySelector');       if (strategySelector) {         strategySelector.addEventListener('cha
- Selector');       if (strategySelector) {         strategySelector.addEventListener('change', function() {          
- ', function() {           try {             const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');             if (!strategySelector) {          
- ElementById('strategySelector');             if (!strategySelector) {               logMessages.push(`[${new Date().
-           }              const selectedStrategy = strategySelector.value;             if (!selectedStrategy || !TRAD
- = `     <label>       Strategy:       <select id="strategySelector" class="strategy-selector">         ${Object.keys
- pdateLogger();       }       if (e.target.id === 'strategySelector') {         const strategy = e.target.value;     
-  // Initialize all strategy selectors       const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.fo
- l('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => {         if (selector) {   
- const selectedStrategy = document.getElementById('strategySelector').value;          // Show strategy animation     
- ate all strategy selectors to match         const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.
- '#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => {           if (selector) { 
- Update all strategy selectors in the UI     const strategySelectors = document.querySelectorAll('#strategySelector, 
- t strategySelectors = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forE
- All('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {       if (selector && sele
-    }    // Initialize strategy selector     const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');     if (strategySelector) {       // Set initi
- ument.getElementById('strategySelector');     if (strategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       strategySelector.value = currentStrategy;        strategySelector.
-  strategySelector.value = currentStrategy;        strategySelector.addEventListener('change', function() {         c

### strategy-selector (25 occurrences)

- }     }      /* Strategy selector styling */     .strategy-selector {       background: var(--secondary-bg);       co
- no-repeat;       padding-right: 30px;     }      .strategy-selector:hover {       box-shadow: 0 0 15px rgba(0, 255, 2
-      border-color: var(--text-color);     }      .strategy-selector:focus {       outline: none;       box-shadow: 0 
- dow: 0 0 20px rgba(0, 255, 255, 0.7);     }      .strategy-selector option {       background: var(--secondary-bg);  
- color: #FFF;   box-shadow: 0 0 18px #00FFFF99; } .strategy-selector {   font-family: 'Orbitron', sans-serif;   font-s
- w: 0 2px 12px #00FFFF22;   margin-right: 1rem; } .strategy-selector option {   background-color: #0a0a1a;   color: #0
- slateY(-2px); }  /* Strategy selector styling */ .strategy-selector {   background: linear-gradient(135deg, rgba(0, 2
- on: right 10px center;   padding-right: 30px; }  .strategy-selector:hover {   background-color: rgba(0, 255, 255, 0.1
- 55, 255, 0.5);   transform: translateY(-2px); }  .strategy-selector:focus {   border-color: #FF00FF;   box-shadow: 0 
-  box-shadow: 0 0 20px rgba(255, 0, 255, 0.5); }  .strategy-selector option {   background-color: #0a0a1a;   color: #0
-   }    /* Enhanced strategy selector styles */   .strategy-selector-container {     margin: 15px 0;     position: rel
-   margin: 15px 0;     position: relative;   }    .strategy-selector {     width: 100%;     padding: 10px;     backgro
- x; /* Limit height to prevent overflow */   }    .strategy-selector:hover {     border-color: rgba(0, 255, 255, 0.8);
- -shadow: 0 0 10px rgba(0, 255, 255, 0.3);   }    .strategy-selector option {     background-color: #0a1525;     color
- ading styles.         </div>          <div class="strategy-selector-container">           <label for="mainStrategySel
-          <select id="mainStrategySelector" class="strategy-selector">             <!-- This will be populated dynamic
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
- ment('div');       selectorContainer.className = 'strategy-selector-container';       selectorContainer.innerHTML = `
- bel>         <select id="strategySelector" class="strategy-selector">           ${Object.keys(TRADING_STRATEGIES).map
- ategy:       <select id="strategySelector" class="strategy-selector">         ${Object.keys(TRADING_STRATEGIES).map(k
-  = document.querySelectorAll('#strategySelector, .strategy-selector');       strategySelectors.forEach(selector => { 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');         strategySelectors.forEach(selector => 
-  = document.querySelectorAll('#strategySelector, .strategy-selector');     strategySelectors.forEach(selector => {   

### mainStrategySelector (28 occurrences)

- rategy-selector-container">           <label for="mainStrategySelector">Choose Strategy:</label>           <select id="m
- r">Choose Strategy:</label>           <select id="mainStrategySelector" class="strategy-selector">             <!-- This
- elector to match the current strategy       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         main
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         mainStrategySelector.value = currentSt
- ctor');       if (mainStrategySelector) {         mainStrategySelector.value = currentStrategy;       }        // Update
- const strategySelector = document.getElementById('mainStrategySelector');       if (!strategySelector) {         console
- e strategy selector with event listener     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStra
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('c
- Selector');     if (mainStrategySelector) {       mainStrategySelector.addEventListener('change', function() {         /
- strategy selector with all strategies       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (mainStrategySelector) {         // C
- getElementById('mainStrategySelector');       if (mainStrategySelector) {         // Clear existing options         main
- ctor) {         // Clear existing options         mainStrategySelector.innerHTML = '';          // Group strategies by c
- endChild(option);           }         });         mainStrategySelector.appendChild(coreGroup);          // Add Advanced 
- endChild(option);           }         });         mainStrategySelector.appendChild(advancedGroup);          // Add AI-Po
- endChild(option);           }         });         mainStrategySelector.appendChild(aiGroup);          // Add Professiona
- endChild(option);           }         });         mainStrategySelector.appendChild(professionalGroup);       }        //
- n applySelectedStrategy() {     try {       const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');       if (!mainStrategySelector) {         log
- etElementById('mainStrategySelector');       if (!mainStrategySelector) {         logMessages.push(`[${new Date().toLoca
-   return;       }        const selectedStrategy = mainStrategySelector.value;       if (!selectedStrategy || !TRADING_ST
-    // Initialize main strategy selector     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set i
- t.getElementById('mainStrategySelector');     if (mainStrategySelector) {       // Set initial value to match currentStr
-  Set initial value to match currentStrategy       mainStrategySelector.value = currentStrategy;        // Add change eve
- stener to update info panel and helper text       mainStrategySelector.addEventListener('change', function() {         c

### strategyMenu (11 occurrences)

-  */     #logicMenu {       top: 50px;     }      #strategyMenu {       top: 320px;     }      #indicatorMenu {  
-      <!-- Strategy Selector Menu -->     <div id="strategyMenu" class="menu-content">       <div class="strategy
- y);        // Close the strategy menu       const strategyMenu = document.getElementById('strategyMenu');       
-     const strategyMenu = document.getElementById('strategyMenu');       if (strategyMenu) {         strategyMenu
- ocument.getElementById('strategyMenu');       if (strategyMenu) {         strategyMenu.classList.remove('active'
- strategyMenu');       if (strategyMenu) {         strategyMenu.classList.remove('active');       }        // Sho
- emove('active');         document.getElementById('strategyMenu').classList.remove('active');         document.ge
-          e.stopPropagation();         toggleMenu('strategyMenu');         updateStrategyMenu();       });       
- ide any menu         const menus = ['logicMenu', 'strategyMenu', 'indicatorMenu', 'lightLogicMenu', 'thresholdsM
- st.remove('active');     document.getElementById('strategyMenu').classList.remove('active');     document.getEle
- ) {         const menu = document.getElementById('strategyMenu');         if (menu) {           // Scroll to str


## js\global-variables.js

### strategySelector (3 occurrences)

- t mainStrategySelector = document.getElementById('strategySelector') ||                               document.getEl
- / Update all strategy selectors in the UI   const strategySelectors = document.querySelectorAll('#mainStrategySelect
- ll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     if (selector && select

### strategy-selector (1 occurrences)

- ocument.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach(selector => {     i

### mainStrategySelector (6 occurrences)

- l strategy selector value from our global   const mainStrategySelector = document.getElementById('strategySelector') || 
-                          document.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategyS
- ment.getElementById('mainStrategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.curren
- ategySelector')   if (mainStrategySelector) {     mainStrategySelector.value = window.currentStrategy     // Trigger str
- n') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  // Indicators window.INDICATORS 
- t strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')   strategySelectors.forEach

### handleStrategyChange (2 occurrences)

- change to ensure UI updates     if (typeof window.handleStrategyChange === 'function') {       window.handleStrategyChan
- ndleStrategyChange === 'function') {       window.handleStrategyChange({ target: mainStrategySelector })     }   } })  /


## js\main.js

### initializeStrategySelector (2 occurrences)

-  Initialize strategy selector   if (typeof window.initializeStrategySelector === 'function') {     window.initializeStrategySe
- lizeStrategySelector === 'function') {     window.initializeStrategySelector()     console.log('Strategy selector initialized'

### mainStrategySelector (1 occurrences)

-   const strategySelect = document.getElementById('mainStrategySelector')   const applyStrategyBtn = document.getElementB


## js\strategy-manager.js

### strategyMenu (8 occurrences)

- = null     this.strategyContainer = null     this.strategyMenu = null     this.isHandlingStrategyChange = false 
- ument.getElementById('strategyControls')     this.strategyMenu = document.getElementById('strategyMenu')     thi
-      this.strategyMenu = document.getElementById('strategyMenu')     this.timeframeContainer = document.getEleme
- trols')      if (!this.strategyContainer || !this.strategyMenu || !this.timeframeContainer) {       console.erro
-     }   }    renderStrategyMenu() {     if (!this.strategyMenu) return      const strategyOptions = Object.entri
-           </option>         `).join('')      this.strategyMenu.innerHTML = `             <div class="strategy-co
-      `   }    toggleStrategyMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open
- yMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.toggle('open')     }   }    handleStrat

### handleStrategyChange (3 occurrences)

- tail.source !== 'strategyManager') {         this.handleStrategyChange(e.detail.strategyId, true)       }     })      //
- "strategySelect" onchange="window.StrategyManager.handleStrategyChange(this.value)">                     ${strategyOptio
- trategyMenu.classList.toggle('open')     }   }    handleStrategyChange(strategyId, isInternalChange = false) {     if (!

### updateStrategyDescription (2 occurrences)

- ate UI       this.renderStrategyMenu()       this.updateStrategyDescription()        // Update indicators       this.updateIn
- his.isHandlingStrategyChange = false     }   }    updateStrategyDescription() {     const description = this.getStrategyDescr


## js\ui\critical-fixes.js

### strategyMenu (1 occurrences)

- loseAllMenus() {     const menus = ['logicMenu', 'strategyMenu', 'indicatorMenu', 'lightLogicMenu', 'thresholdsM


## js\ui\enhanced-light-logic-overlays.js

### strategyMenu (1 occurrences)

- ['lightLogicMenu', 'logicMenu', 'indicatorMenu', 'strategyMenu', 'thresholdsMenu'];     menus.forEach(menuId => 

### handleStrategyChange (2 occurrences)

- stener('strategyChanged', (event) => {       this.handleStrategyChange(event.detail);     });          // Listen for sig
- endChild(layer);         }       }     });   }    handleStrategyChange(strategyData) {     console.log('[EnhancedLightLo


## js\ui\enhanced-signal-logic.js

### handleStrategyChange (2 occurrences)

- ntListener('strategyChanged', (e) => {       this.handleStrategyChange(e.detail);     });          // Listen for thresho
- this.convergenceHistory.slice(-100);     }   }    handleStrategyChange(strategyData) {     // Reset signal state for new


## js\ui\event-handlers.js

### strategySelector (1 occurrences)

- teLogger()         }         if (e.target.id === 'strategySelector') {           const strategy = e.target.value    


## js\ui\menu-controller.js

### initializeStrategySelector (2 occurrences)

- event duplicate loading.     // if (typeof window.initializeStrategySelector === 'function') {     //   window.initializeStrat
- trategySelector === 'function') {     //   window.initializeStrategySelector();     // }   }    ensureMenuContainersExist() { 

### strategySelector (1 occurrences)

-         const selector = document.getElementById('strategySelector');         if (selector && window.currentStrategy

### strategy-selector (1 occurrences)

- handler.js  * - js/strategy-manager.js  * - js/ui/strategy-selector.js  *   * It handles all menu button interactions

### strategyMenu (3 occurrences)

-  {     this.activeMenu = null;     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu',
-   this.menuButtonMap = {       'strategyButton': 'strategyMenu',       'toggleMenuButton': 'indicatorMenu',     
- -specific content if needed       if (menuId === 'strategyMenu') {         // If strategy menu has a selector, u

### updateStrategyDescription (2 occurrences)

- ndow.currentStrategy;           if (typeof window.updateStrategyDescription === 'function') {             window.updateStrate
- yDescription === 'function') {             window.updateStrategyDescription();           }         }       }       else if (m


## js\ui\menu-handler.js

### strategyMenu (3 occurrences)

-  {     this.activeMenu = null;     this.menus = ['strategyMenu', 'indicatorMenu', 'thresholdsMenu', 'logicMenu']
- st buttonToMenuMap = {         'strategyButton': 'strategyMenu',         'toggleMenuButton': 'indicatorMenu',   
-  first, then by ID     const buttonMap = {       'strategyMenu': { class: '.strategy-button', id: 'strategyButto


## js\ui\strategy-logic.js

### mainStrategySelector (4 occurrences)

- ateStrategyMenu = function () {   try {     const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrat
- nt.getElementById('mainStrategySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStra
- ySelector')     if (mainStrategySelector) {       mainStrategySelector.value = currentStrategy     }     const mainStrat


## js\ui\strategy-persistence.js

### strategySelector (3 occurrences)

-  Get current strategy from selector         const strategySelector = document.getElementById('mainStrategySelector')
- etElementById('mainStrategySelector')         if (strategySelector) {           const selectedStrategy = strategySel
- egySelector) {           const selectedStrategy = strategySelector.value            // Save to localStorage         

### mainStrategySelector (5 occurrences)

- Strategy)          // Update the UI         const mainStrategySelector = document.getElementById('mainStrategySelector')
- t mainStrategySelector = document.getElementById('mainStrategySelector')         if (mainStrategySelector) {           m
- etElementById('mainStrategySelector')         if (mainStrategySelector) {           mainStrategySelector.value = savedSt
- r')         if (mainStrategySelector) {           mainStrategySelector.value = savedStrategy           console.log('[Str
- const strategySelector = document.getElementById('mainStrategySelector')         if (strategySelector) {           const


## js\ui\strategy-selector.js

### initializeStrategySelector (9 occurrences)

- ize strategy selector (single canonical) function initializeStrategySelector() {   console.trace('initializeStrategySelector c
- n initializeStrategySelector() {   console.trace('initializeStrategySelector called from:');   // Close menu when clicking out
- false;   } }  // Initialize on DOM ready function initializeStrategySelectorModule() {   console.log('Initializing strategy se
-               // Initialize the selector UI       initializeStrategySelector();              // Initialize strategy selectors 
-  } }  // Make functions available globally window.initializeStrategySelector = initializeStrategySelector; window.applySelecte
- able globally window.initializeStrategySelector = initializeStrategySelector; window.applySelectedStrategy = applySelectedStra
-  Initialize the main strategy selector module     initializeStrategySelectorModule();          // Attach indicator menu handle
-  // document.addEventListener('DOMContentLoaded', initializeStrategySelectorModule); } else {   // initializeStrategySelectorM
-  initializeStrategySelectorModule); } else {   // initializeStrategySelectorModule(); } window.updateIndicatorMenu = updateInd

### strategySelector (10 occurrences)

- egy     const selector = document.getElementById('strategySelector') || document.getElementById('mainStrategySelecto
-  document.getElementById('strategyMenu');   const strategySelector = document.getElementById('strategySelector');   
- const strategySelector = document.getElementById('strategySelector');   const applyStrategyButton = document.getElem
- rading styles.         </div>         <select id="strategySelector" class="strategy-selector">           ${Object.en
- st newStrategySelector = document.getElementById('strategySelector');   const newApplyStrategyButton = document.getE
-         const selector = document.getElementById('strategySelector');         if (selector) {           applySelecte
-      // Initialize strategy selectors       const strategySelectors = [         document.getElementById('strategySel
- egySelectors = [         document.getElementById('strategySelector'),         document.getElementById('mainStrategyS
- // Set the selected value for all selectors       strategySelectors.forEach(selector => {         if (selector) {   
- y);     const selector = document.getElementById('strategySelector') ||                     document.getElementById(

### strategy-selector (1 occurrences)

- div>         <select id="strategySelector" class="strategy-selector">           ${Object.entries(window.TRADING_STRAT

### mainStrategySelector (3 occurrences)

- d('strategySelector') || document.getElementById('mainStrategySelector');     if (selector) {       selector.value = win
- ategySelector'),         document.getElementById('mainStrategySelector')       ].filter(Boolean);              // Set th
- ) ||                     document.getElementById('mainStrategySelector');          if (selector) {       selector.value 

### strategyMenu (16 occurrences)

- nus() {     const menu = document.getElementById('strategyMenu');     if (menu && menu.classList.contains('open'
- vent) {     const menu = document.getElementById('strategyMenu');     if (menu && menu.classList.contains('open'
- ocument.getElementById('strategyButton');   const strategyMenu = document.getElementById('strategyMenu');   cons
- ;   const strategyMenu = document.getElementById('strategyMenu');   const strategySelector = document.getElement
- esn't exist   let menu = document.getElementById('strategyMenu');   if (!menu) {     menu = document.createEleme
- u = document.createElement('div');     menu.id = 'strategyMenu';     menu.className = 'menu-content';     menu.s
- ion();     event.preventDefault();   }      const strategyMenu = document.getElementById('strategyMenu');   if (
-     const strategyMenu = document.getElementById('strategyMenu');   if (strategyMenu) {     const isVisible = st
-  = document.getElementById('strategyMenu');   if (strategyMenu) {     const isVisible = strategyMenu.style.displ
- nu');   if (strategyMenu) {     const isVisible = strategyMenu.style.display === 'block';     strategyMenu.style
- ble = strategyMenu.style.display === 'block';     strategyMenu.style.display = isVisible ? 'none' : 'block';    
- st rect = button.getBoundingClientRect();         strategyMenu.style.top = `${rect.bottom + window.scrollY}px`; 
- op = `${rect.bottom + window.scrollY}px`;         strategyMenu.style.left = `${rect.left + window.scrollX}px`;  
- s = document.querySelectorAll('.menu-content:not(#strategyMenu)');     otherMenus.forEach(menu => {       menu.s
- () => {     const menu = document.getElementById('strategyMenu');     if (menu) menu.style.display = 'none';   }
- () => {     const menu = document.getElementById('strategyMenu');     if (menu) menu.style.display = 'none';   }

### handleStrategyChange (7 occurrences)

-   } }  // Handle strategy change event   function handleStrategyChange(event) {   const strategyId = event.target.value;
-            selector.removeEventListener('change', handleStrategyChange);           selector.addEventListener('change', h
- e);           selector.addEventListener('change', handleStrategyChange);         }       });              // Initialize 
- gyDescription = updateStrategyDescription; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI =
- trategyDescription; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI = updateStrategyUI; wind
- tifyStrategyChange = notifyStrategyChange; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI =
- tifyStrategyChange; window.handleStrategyChange = handleStrategyChange; window.updateStrategyUI = updateStrategyUI; wind

### updateStrategyDescription (14 occurrences)

-    selector.value = window.currentStrategy;       updateStrategyDescription();     }   }    document.addEventListener('moused
- newStrategySelector.removeEventListener('change', updateStrategyDescription);     newStrategySelector.addEventListener('chang
-    newStrategySelector.addEventListener('change', updateStrategyDescription);   }      if (newApplyStrategyButton) {     // R
- egy   updateStrategyUI(window.currentStrategy);   updateStrategyDescription(); }  // Toggle strategy menu visibility function
- y description based on selected strategy function updateStrategyDescription() {   try {     const strategyId = window.current
-  = strategyId;    // Update strategy info panel   updateStrategyDescription();    // Update indicators for the new strategy  
- ts();     }   }, 100);    // Update description   updateStrategyDescription();    // Show success message   const strategyNam
- ategyUI(strategyId);      // Update description   updateStrategyDescription();      // Update indicators for the new strategy
-   updateStrategyUI(window.currentStrategy);       updateStrategyDescription();              // Update indicators for the curr
- ySelectedStrategy = applySelectedStrategy; window.updateStrategyDescription = updateStrategyDescription; window.handleStrateg
- ectedStrategy; window.updateStrategyDescription = updateStrategyDescription; window.handleStrategyChange = handleStrategyChan
- sole.log('Updating strategy description...');     updateStrategyDescription();          console.log('Strategy module fully in
- w.toggleStrategyMenu = toggleStrategyMenu; window.updateStrategyDescription = updateStrategyDescription; window.applySelected
- eStrategyMenu; window.updateStrategyDescription = updateStrategyDescription; window.applySelectedStrategy = applySelectedStra


## js\ui\threshold-sliders.js

### strategyMenu (1 occurrences)

- const menus = [       'threshold-sliders',       'strategyMenu',       'indicatorMenu',       'logicControls'   


## src\strategy\strategy-manager.js

### strategyMenu (18 occurrences)

- ment.getElementById('strategyButton');       this.strategyMenu = document.getElementById('strategyMenu');       
-      this.strategyMenu = document.getElementById('strategyMenu');              if (!this.strategyButton || !this
- );              if (!this.strategyButton || !this.strategyMenu) {         console.warn('Strategy elements not fo
- ect if it doesn't exist       let selectEl = this.strategyMenu.querySelector('select');       if (!selectEl) {  
- dChild(option);         });                  this.strategyMenu.appendChild(selectEl);       }              // Cr
-  apply button if it doesn't exist       if (!this.strategyMenu.querySelector('.apply-strategy')) {         const
- yBtn.textContent = 'Apply Strategy';         this.strategyMenu.appendChild(applyBtn);       }              this.
-       // Apply strategy     const applyBtn = this.strategyMenu.querySelector('.apply-strategy');     if (applyBt
- addEventListener('click', (e) => {       if (this.strategyMenu && !this.strategyMenu.contains(e.target) &&      
- k', (e) => {       if (this.strategyMenu && !this.strategyMenu.contains(e.target) &&            this.strategyBut
- u visibility    */   toggleMenu() {     if (!this.strategyMenu) return;          const isVisible = this.strategy
- tegyMenu) return;          const isVisible = this.strategyMenu.classList.toggle('visible');          if (isVisib
- trategyButton.getBoundingClientRect();       this.strategyMenu.style.top = `${rect.bottom + window.scrollY}px`; 
- = `${rect.bottom + window.scrollY}px`;       this.strategyMenu.style.left = `${rect.left + window.scrollX}px`;  
- is.currentStrategy) {         const select = this.strategyMenu.querySelector('select');         if (select) {   
-  strategy menu    */   closeMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.remove('visi
- eMenu() {     if (this.strategyMenu) {       this.strategyMenu.classList.remove('visible');     }   }    /**    
- applySelectedStrategy() {     const select = this.strategyMenu?.querySelector('select');     if (!select) return


# Summary of Strategy Selector Initialization

## Initialization Flow

1. `index.html` loads and includes various JavaScript files
2. `js/main.js` is loaded and initializes the application
3. `js/ui/strategy-selector.js` is loaded and initializes the strategy selector
4. `js/ui/menu-controller.js` may also initialize the strategy selector
5. Other modules may reference or modify the strategy selector

## Recommendations

1. Ensure `initializeStrategySelector` is only called once
2. Check for duplicate event listeners on strategy selector elements
3. Verify that no other scripts are re-initializing the strategy selector
