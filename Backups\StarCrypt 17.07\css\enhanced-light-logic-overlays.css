/**
 * Enhanced Light Logic Overlays and Z-Index Fixes for StarCrypt
 * Comprehensive overlay management with proper z-index hierarchy
 */

/* Z-Index Hierarchy Definition */
:root {
  --z-base: 1;
  --z-content: 10;
  --z-signal-lights: 50;
  --z-interactive: 100;
  --z-menus: 1000;
  --z-enhanced-menus: 1100;
  --z-overlays: 2000;
  --z-tooltips: 9999;
  --z-loading: 10000;
}

/* Enhanced Light Logic Menu Positioning */
#lightLogicMenu {
  position: fixed !important;
  width: 350px !important;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
  border: 2px solid #00d4ff !important;
  border-radius: 15px !important;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  z-index: 1000 !important;
  padding: 20px !important;
  color: #ffffff !important;
  display: none !important;
  pointer-events: auto !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  /* Positioned by JavaScript to ensure momentum-indicators is visible */
}

#lightLogicMenu.active {
  display: block !important;
  animation: enhancedMenuSlideIn 0.3s ease-out !important;
}

@keyframes enhancedMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Signal Logic Menu Positioning */
#logicMenu {
  position: fixed !important;
  width: 500px !important;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
  border: 2px solid #00d4ff !important;
  border-radius: 15px !important;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  z-index: 1000 !important;
  padding: 20px !important;
  color: #ffffff !important;
  display: none !important;
  pointer-events: auto !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  /* Positioned by JavaScript to ensure momentum-indicators is visible */
}

#logicMenu.active {
  display: block !important;
  animation: enhancedMenuSlideIn 0.3s ease-out !important;
}

/* Fix Other Menu Z-Index Issues */
#indicatorMenu,
#strategyMenu,
#thresholdsMenu,
#timeframesMenu {
  z-index: var(--z-menus) !important;
  pointer-events: auto !important;
}

/* Enhanced Convergence Overlays */
.convergence-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: var(--z-overlays);
  border-radius: inherit;
  overflow: hidden;
}

.convergence-overlay.active {
  animation: convergenceOverlayPulse 2s infinite;
}

@keyframes convergenceOverlayPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* Enhanced Light Logic Layers */
.light-logic-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: var(--z-signal-lights);
  border-radius: inherit;
  transition: all 0.3s ease;
}

.light-logic-layer.conservative {
  background: radial-gradient(circle, rgba(100, 149, 237, 0.1) 0%, transparent 70%);
  filter: brightness(0.9);
}

.light-logic-layer.wallstreet {
  background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.2);
}

.light-logic-layer.vibeflow {
  background: radial-gradient(circle, rgba(138, 43, 226, 0.1) 0%, transparent 70%);
  animation: vibeflowPulse 3s infinite;
}

@keyframes vibeflowPulse {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  33% { filter: hue-rotate(120deg) brightness(1.2); }
  66% { filter: hue-rotate(240deg) brightness(1.1); }
}

.light-logic-layer.cosmic {
  background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 50%, transparent 70%);
  animation: cosmicRotate 5s linear infinite;
}

@keyframes cosmicRotate {
  from { filter: hue-rotate(0deg); }
  to { filter: hue-rotate(360deg); }
}

.light-logic-layer.chaos {
  background: radial-gradient(circle, rgba(255, 69, 0, 0.1) 0%, transparent 70%);
  animation: chaosFlicker 0.5s infinite;
}

@keyframes chaosFlicker {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.3; }
}

/* Enhanced Helper Step Overlays */
.helper-step-overlay {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  pointer-events: none;
  z-index: var(--z-content);
  border-radius: 10px;
  opacity: 0;
  transition: all 0.3s ease;
}

.helper-step.highlight-active .helper-step-overlay {
  opacity: 1;
  background: linear-gradient(45deg, 
    rgba(0, 212, 255, 0.1) 0%, 
    rgba(0, 255, 136, 0.1) 50%, 
    rgba(255, 165, 2, 0.1) 100%);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  animation: helperStepGlow 2s infinite;
}

@keyframes helperStepGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

/* Enhanced Convergence Indicators */
.enhanced-convergence-indicator {
  position: relative;
  z-index: var(--z-signal-lights);
}

.enhanced-convergence-indicator::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: conic-gradient(
    from 0deg,
    #00d4ff 0deg,
    #00ff88 90deg,
    #ffa502 180deg,
    #ff4757 270deg,
    #00d4ff 360deg
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  animation: convergenceRing 3s linear infinite;
  transition: opacity 0.3s ease;
}

.enhanced-convergence-indicator.active::before {
  opacity: 0.7;
}

@keyframes convergenceRing {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Degenism Settings Overlays */
.degenism-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: var(--z-overlays);
  border-radius: inherit;
  opacity: 0;
  transition: all 0.3s ease;
}

.degenism-overlay.degen-mode {
  opacity: 1;
  background: radial-gradient(circle, 
    rgba(255, 20, 147, 0.1) 0%, 
    rgba(0, 255, 255, 0.1) 50%, 
    transparent 70%);
  animation: degenPulse 1s infinite;
}

@keyframes degenPulse {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
}

/* Structured Light Layers */
.structured-light-container {
  position: relative;
  z-index: var(--z-signal-lights);
}

.light-layer-1 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
  border-radius: inherit;
}

.light-layer-2 {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  pointer-events: none;
  z-index: 2;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.05) 0%, transparent 70%);
  border-radius: inherit;
  animation: lightLayer2Rotate 4s linear infinite;
}

@keyframes lightLayer2Rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.light-layer-3 {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  pointer-events: none;
  z-index: 3;
  background: radial-gradient(circle, rgba(255, 165, 2, 0.05) 0%, transparent 70%);
  border-radius: inherit;
  animation: lightLayer3Pulse 2s infinite;
}

@keyframes lightLayer3Pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Interactive Element Protection */
.interactive-element {
  position: relative;
  z-index: var(--z-interactive) !important;
  pointer-events: auto !important;
}

/* Ensure buttons, sliders, and inputs are always interactive */
button,
input,
select,
.slider,
.threshold-slider,
.enhanced-slider,
.signal-circle,
.menu-button {
  position: relative;
  z-index: var(--z-interactive) !important;
  pointer-events: auto !important;
}

/* Enhanced Tooltip Z-Index */
.custom-tooltip,
.tooltip,
[data-tooltip]::after {
  z-index: var(--z-tooltips) !important;
  pointer-events: none !important;
}

/* Loading Overlay Fix */
#loading-overlay {
  z-index: var(--z-loading) !important;
}

/* Menu Backdrop Enhancement */
.menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: calc(var(--z-menus) - 1);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.menu-backdrop.active {
  opacity: 1;
  pointer-events: auto;
}

/* Enhanced Signal Light Overlays */
.signal-light-overlay {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  pointer-events: none;
  z-index: calc(var(--z-signal-lights) + 1);
  border-radius: inherit;
  opacity: 0;
  transition: all 0.3s ease;
}

.signal-circle:hover .signal-light-overlay {
  opacity: 1;
  background: radial-gradient(circle, currentColor 0%, transparent 70%);
  filter: blur(2px);
}

/* Responsive Overlay Adjustments */
@media (max-width: 768px) {
  #lightLogicMenu,
  #logicMenu {
    width: 90vw !important;
    max-width: 400px !important;
    left: 5vw !important;
    right: auto !important;
  }
  
  .convergence-overlay,
  .light-logic-layer,
  .helper-step-overlay {
    border-radius: 8px;
  }
}

/* Debug Mode for Z-Index Issues */
.debug-z-index * {
  outline: 1px solid rgba(255, 0, 0, 0.3) !important;
  position: relative !important;
}

.debug-z-index *::before {
  content: attr(class) " z:" attr(style);
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  padding: 2px;
  z-index: 99999;
  pointer-events: none;
}

/* Enhanced Timeframe Selector Styles */
.enhanced-timeframes-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.timeframe-presets {
  background: rgba(0, 20, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.timeframe-presets h4 {
  margin: 0 0 10px 0;
  color: #00d4ff;
  font-size: 1rem;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.timeframe-preset-btn {
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.timeframe-preset-btn:hover {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000000;
  transform: translateY(-1px);
}

.active-timeframes-section {
  background: rgba(0, 40, 20, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 255, 136, 0.2);
}

.active-timeframes-section h4 {
  margin: 0 0 10px 0;
  color: #00ff88;
  font-size: 1rem;
}

.active-timeframes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.active-timeframe-item {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid #00ff88;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.85rem;
}

.timeframe-label {
  color: #00ff88;
  font-weight: bold;
}

.timeframe-duration {
  color: #cccccc;
  font-size: 0.75rem;
}

.remove-timeframe-btn {
  background: #ff4757;
  border: none;
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-timeframe-btn:hover {
  background: #ff3742;
}

.available-timeframes-section {
  background: rgba(40, 20, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 165, 2, 0.2);
}

.available-timeframes-section h4 {
  margin: 0 0 15px 0;
  color: #ffa502;
  font-size: 1rem;
}

.timeframe-group {
  margin-bottom: 15px;
}

.timeframe-group h5 {
  margin: 0 0 8px 0;
  color: #ffa502;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(255, 165, 2, 0.2);
  padding-bottom: 4px;
}

.timeframe-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.timeframe-checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.timeframe-checkbox-label:hover {
  background: rgba(255, 165, 2, 0.1);
}

.timeframe-checkbox-label input[type="checkbox"] {
  margin: 0;
}

.timeframe-checkbox-label input[type="checkbox"]:disabled {
  opacity: 0.5;
}

.timeframe-text {
  color: #ffffff;
  font-weight: 500;
}

.timeframe-desc {
  color: #cccccc;
  font-size: 0.75rem;
}

.custom-timeframe-section {
  background: rgba(40, 0, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 0, 255, 0.2);
}

.custom-timeframe-section h4 {
  margin: 0 0 10px 0;
  color: #ff00ff;
  font-size: 1rem;
}

.add-custom-btn {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  border: 1px solid #ff00ff;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.add-custom-btn:hover {
  background: linear-gradient(135deg, #ff00ff, #cc00cc);
  transform: translateY(-1px);
}

.custom-timeframe-dialog {
  margin-top: 15px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 0, 255, 0.3);
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  color: #ff00ff;
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 0, 255, 0.3);
  color: #ffffff;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #ff00ff;
  box-shadow: 0 0 5px rgba(255, 0, 255, 0.3);
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.form-actions button {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  border: 1px solid #ff00ff;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.form-actions button[type="button"] {
  background: linear-gradient(135deg, #555, #666);
  border-color: #999;
}

.live-update-section {
  background: rgba(20, 40, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.live-update-section h4 {
  margin: 0 0 10px 0;
  color: #00ffff;
  font-size: 1rem;
}

.live-update-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 0.85rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

/* Toast Notifications */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast {
  font-family: 'Orbitron', monospace;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-timeframes-container {
    gap: 15px;
  }

  .preset-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .timeframe-checkboxes {
    grid-template-columns: 1fr;
  }

  .active-timeframes-list {
    flex-direction: column;
  }
}
