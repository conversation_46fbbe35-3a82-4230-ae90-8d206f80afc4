// Import services with error handling
let aiAnalysisService
try {
  aiAnalysisService = (await import('../services/AIAnalysisService.js')).aiAnalysisService
} catch (error) {
  console.error('Failed to load AIAnalysisService:', error)
  // Create a mock service to prevent errors
  aiAnalysisService = {
    initialize: async () => {
      console.warn('Using mock AIAnalysisService')
      return true
    },
    // Add other required methods as no-ops
    analyzeMarket: () => ({}),
    getRecommendations: () => [],
  }
}

export class AIDashboard {
  constructor(containerId = 'ai-dashboard') {
    this.container = document.getElementById(containerId) || this.createContainer(containerId)
    this.analysis = null
    this.theme = 'dark'
    this.initialized = false
  }

  createContainer(id) {
    const container = document.createElement('div')
    container.id = id
    container.className = 'ai-dashboard'
    document.body.appendChild(container)
    return container
  }

  async initialize() {
    console.log('AIDashboard: Starting initialization...')
    if (this.initialized) {
      console.log('AIDashboard: Already initialized, skipping...')
      return true
    }

    try {
      // Show loading state
      this.container.innerHTML = `
        <div class="ai-dashboard-loading">
          <div class="spinner"></div>
          <p>Initializing AI Dashboard...</p>
        </div>
      `

      // Load styles
      console.log('AIDashboard: Loading styles...')
      this.loadStyles()

      // Initialize AI Analysis Service
      console.log('AIDashboard: Initializing AI Analysis Service...')
      if (!aiAnalysisService || typeof aiAnalysisService.initialize !== 'function') {
        throw new Error('AI Analysis Service is not available')
      }

      await aiAnalysisService.initialize().catch(err => {
        console.warn('AI Analysis Service initialization warning:', err)
        // Continue even if service has warnings
      })

      // Mark as initialized
      this.initialized = true

      // Render the dashboard
      console.log('AIDashboard: Initialized successfully, rendering...')
      this.render()

      // Notify other components
      this.dispatchEvent(new CustomEvent('ai-dashboard:initialized'))

      return true
    } catch (error) {
      console.error('AIDashboard: Error during initialization:', error)
      this.showError(`
        <h3>Failed to initialize AI Dashboard</h3>
        <p>${error.message || 'Unknown error occurred'}</p>
        <button onclick="window.location.reload()">Try Again</button>
      `)
      return false
    }
  }

  showError(message) {
    console.error('AI Dashboard Error:', message)

    // Create error element if it doesn't exist
    let errorEl = this.container.querySelector('.ai-dashboard-error')
    if (!errorEl) {
      errorEl = document.createElement('div')
      errorEl.className = 'ai-dashboard-error'
      this.container.prepend(errorEl)
    }

    // Update error message
    errorEl.textContent = message
    errorEl.style.display = 'block'

    // Auto-hide after 10 seconds
    if (this._errorTimeout) clearTimeout(this._errorTimeout)
    this._errorTimeout = setTimeout(() => {
      errorEl.style.display = 'none'
    }, 10000)
  }

  loadStyles() {
    if (document.getElementById('ai-dashboard-styles')) return

    const style = document.createElement('style')
    style.id = 'ai-dashboard-styles'

    const css = `
      .ai-dashboard {
        position: relative;
        font-family: 'Segoe UI', Roboto, -apple-system, sans-serif;
        background: rgba(10, 14, 23, 0.95);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #e0e0e0;
        padding: 20px;
        max-width: 1200px;
        margin: 20px auto;
        overflow: hidden;
      }
      
      .ai-dashboard-error {
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 59, 48, 0.2);
        color: #ff6b6b;
        padding: 8px 16px;
        border-radius: 4px;
        border-left: 3px solid #ff3e3e;
        font-size: 14px;
        z-index: 1000;
        max-width: 90%;
        text-align: center;
        transition: opacity 0.3s ease;
      }
      
      .ai-dashboard-error.hidden {
        opacity: 0;
        pointer-events: none;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -10px); }
        to { opacity: 1; transform: translate(-50%, 0); }
      }
      
      .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .dashboard-title {
        font-size: 24px;
        font-weight: 600;
        background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0;
      }
      
      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }
      
      .dashboard-card {
        background: rgba(20, 25, 40, 0.8);
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #a0a0a0;
        margin: 0;
      }
      
      .card-value {
        font-size: 24px;
        font-weight: 700;
        margin: 10px 0;
      }
      
      .confidence-meter {
        height: 6px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        margin: 10px 0;
        overflow: hidden;
      }
      
      .confidence-level {
        height: 100%;
        border-radius: 3px;
        transition: width 0.5s ease;
      }
      
      .confidence-high { background: #00e676; }
      .confidence-medium { background: #ffab00; }
      .confidence-low { background: #ff5252; }
      
      .tag {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .tag-buy {
        background: rgba(0, 200, 83, 0.1);
        color: #00c853;
      }
      
      .tag-sell {
        background: rgba(255, 82, 82, 0.1);
        color: #ff5252;
      }
      
      .tag-neutral {
        background: rgba(158, 158, 158, 0.1);
        color: #9e9e9e;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        margin: 20px auto;
        border: 4px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top-color: #4facfe;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .dashboard-loading {
        text-align: center;
        padding: 40px 0;
      }
      
      .dashboard-loading p {
        margin-top: 15px;
        color: #a0a0a0;
      }`

    style.textContent = css
    document.head.appendChild(style)
  }

  async update(marketData) {
    if (!this.initialized) await this.initialize()

    try {
      this.analysis = await aiAnalysisService.analyzeMarket(marketData)
      this.render()
    } catch (error) {
      console.error('Error updating AI dashboard:', error)
      this.showError('Failed to update analysis')
    }
  }

  render() {
    if (!this.analysis) {
      this.container.innerHTML = `
        <div class="dashboard-loading">
          <div class="loading-spinner"></div>
          <p>Initializing AI analysis...</p>
        </div>
      `
      return
    }

    const { pricePrediction, trendAnalysis, actionRecommendation } = this.analysis

    this.container.innerHTML = `
      <div class="dashboard-header">
        <h2 class="dashboard-title">AI Market Analysis</h2>
        <div class="last-updated">${new Date().toLocaleTimeString()}</div>
      </div>
      
      <div class="dashboard-grid">
        <!-- Recommendation Card -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Recommendation</h3>
            <span class="tag tag-${actionRecommendation.action.includes('buy') ? 'buy' : actionRecommendation.action.includes('sell') ? 'sell' : 'hold'}">
              ${actionRecommendation.action.replace('_', ' ').toUpperCase()}
            </span>
          </div>
          <div class="confidence-meter">
            <div class="confidence-level confidence-${this.getConfidenceLevel(actionRecommendation.confidence)}" 
                 style="width: ${actionRecommendation.confidence * 100}%"></div>
          </div>
          <div class="confidence-value">Confidence: ${Math.round(actionRecommendation.confidence * 100)}%</div>
          
          <div class="key-levels">
            <div class="level">
              <div class="level-label">Stop Loss</div>
              <div class="level-value">$${actionRecommendation.stopLoss.toFixed(2)}</div>
            </div>
            <div class="level">
              <div class="level-label">Take Profit</div>
              <div class="level-value">$${actionRecommendation.takeProfit.toFixed(2)}</div>
            </div>
            <div class="level">
              <div class="level-label">R/R Ratio</div>
              <div class="level-value">${actionRecommendation.riskRewardRatio.toFixed(2)}</div>
            </div>
          </div>
        </div>
        
        <!-- Price Prediction Card -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Price Prediction</h3>
          </div>
          <div class="card-value">$${pricePrediction.prediction.toFixed(2)}</div>
          <div class="card-subtitle">Next 24h forecast</div>
          
          <div class="trend-indicator">
            <div class="trend-label">Trend: ${pricePrediction.indicators.trend}</div>
            <div class="confidence-meter">
              <div class="confidence-level confidence-${this.getConfidenceLevel(pricePrediction.indicators.strength)}" 
                   style="width: ${pricePrediction.indicators.strength * 100}%"></div>
            </div>
          </div>
        </div>
        
        <!-- Market Sentiment -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Market Sentiment</h3>
          </div>
          <div class="sentiment-display">
            <div class="sentiment-value">${trendAnalysis.primaryTrend.toUpperCase()}</div>
            <div class="sentiment-strength">Strength: ${Math.round(trendAnalysis.strength * 100)}%</div>
          </div>
          <div class="volume-analysis">
            <div class="volume-trend">Volume: ${trendAnalysis.volumeAnalysis.trend}</div>
            ${trendAnalysis.volumeAnalysis.anomaly ? '<div class="volume-anomaly">Volume Anomaly Detected</div>' : ''}
          </div>
        </div>
      </div>
    `

    // Add animation to confidence meters
    this.animateConfidenceMeters()
  }

  getConfidenceLevel(confidence) {
    if (confidence > 0.7) return 'high'
    if (confidence > 0.4) return 'medium'
    return 'low'
  }

  animateConfidenceMeters() {
    // This will be called after the DOM is updated to animate the confidence meters
    const meters = this.container.querySelectorAll('.confidence-level')
    meters.forEach(meter => {
      meter.style.transition = 'width 1s ease-out'
      // Force reflow
      void meter.offsetWidth
      meter.style.width = meter.style.width // Trigger the animation
    })
  }

  showError(message) {
    this.container.innerHTML = `
      <div class="error-message">
        <span class="error-icon">⚠️</span>
        <p>${message}</p>
      </div>
    `
  }
}

export default AIDashboard
