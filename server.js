require('dotenv').config();
const express = require('express')
const WebSocket = require('ws')
const fetch = require('node-fetch').default
const url = require('url')
const KrakenWebSocket = require('./services/kraken-websocket')
const TradingViewAccurateIndicators = require('./js/indicators/tradingview-accurate-indicators')

// 🎖️ FLEET COMMANDER: Server startup debug
console.log('🚀 FLEET COMMANDER: StarCrypt Enterprise server starting...');
console.log('🔍 DEBUG: TradingViewAccurateIndicators loaded:', !!TradingViewAccurateIndicators);
console.log('🔍 DEBUG: Available methods:', Object.getOwnPropertyNames(TradingViewAccurateIndicators));

// Create express app
const app = express()

// General request logger - VERY FIRST MIDDLEWARE
app.use((req, res, next) => {
  console.log(`[SERVER] Incoming Request: ${req.method} ${req.originalUrl}`);

  // Set permissive CSP to allow eval() for StarCrypt functionality
  res.setHeader('Content-Security-Policy', "script-src 'self' 'unsafe-eval' 'unsafe-inline' https:; object-src 'none';");

  next();
});
const path = require('path');
const fs = require('fs');

// --- Static file serving ---
// This tells Express to serve files from your asset directories
// and is the fix for the CSS MIME type errors.
// Middleware to log CSS requests
app.use((req, res, next) => {
  if (req.path.endsWith('.css')) {
    console.log(`[SERVER] CSS Request: ${req.path}`);
  }
  next();
});

// Serve static files from specific directories first
app.use('/css', express.static(path.join(__dirname, 'css'), {
  setHeaders: function (res, path, stat) {
    res.set('Content-Type', 'text/css');
    console.log(`[SERVER] Serving CSS: ${path} as text/css`);
  }
}));
app.use('/js', express.static(path.join(__dirname, 'js')));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Then serve static files from the root (e.g., for index.html)
// This should be AFTER specific routes to avoid it catching /css, /js, etc.
app.use(express.static(path.join(__dirname, '')));

// Fixed port handling for asynchronous errors
const server = app.listen(3000, () => {
  console.log(`StarCrypt Enterprise server running on port ${server.address().port}`)

  // Initialize sentiment data on server start
  fetchSentimentData().then(() => {
    console.log('Initial sentiment data loaded')
  }).catch(error => {
    console.warn('Failed to load initial sentiment data:', error.message)
  })
});

// 🎖️ FLEET COMMANDER: Create dedicated WebSocket server on port 8080 for frontend connections
console.log(`🎖️ FLEET COMMANDER: Creating frontend WebSocket server on port 8080...`);
const frontendWss = new WebSocket.Server({ port: 8080 });
console.log(`🎖️ FLEET COMMANDER: Frontend WebSocket server running on port 8080`);

// 🎖️ FLEET COMMANDER: DISABLED - Kraken WebSocket server to prevent frontend connections
// Only the enhanced frontend WebSocket server on port 8080 should accept connections
// const wss = new WebSocket.Server({ server });
// console.log(`🎖️ FLEET COMMANDER: Kraken WebSocket server attached to HTTP server port 3000`);

// Initialize Kraken WebSocket client (without server attachment)
const krakenWebSocket = new KrakenWebSocket(frontendWss);
krakenWebSocket.connect();

// 🎖️ FLEET COMMANDER: Enhanced WebSocket Handler for Frontend Connections on Port 8080
// Handle frontend WebSocket connections with TradingView-accurate data
frontendWss.on('connection', (ws, req) => {
  try {
    const ip = req.socket.remoteAddress;
    console.log(`🎖️ FLEET COMMANDER: ✅ ENHANCED HANDLER ACTIVE - Frontend WebSocket connected from ${ip}`);
    console.log(`🎖️ FLEET COMMANDER: ✅ ENHANCED HANDLER - Total clients: ${frontendWss.clients.size}`);

    ws.isAlive = true;
    ws.clientId = Date.now() + Math.random();

    // Handle frontend messages
    ws.on('message', async (message) => {
      try {
        // Handle PING/PONG messages
        if (message.toString() === 'PING') {
          ws.send('PONG');
          return;
        }
        if (message.toString() === 'PONG') {
          return; // Just acknowledge, no response needed
        }

        const msg = JSON.parse(message);
        console.log(`🎖️ FLEET COMMANDER: ✅ ENHANCED HANDLER MESSAGE: ${msg.type} for ${msg.pair}/${msg.timeframe}`);

        await handleFrontendMessage(ws, msg);
      } catch (error) {
        console.error('❌ Error processing frontend message:', error);
        sendErrorToFrontend(ws, 'Error processing message', error.message);
      }
    });

    // Handle pong for heartbeat
    ws.on('pong', () => {
      ws.isAlive = true;
    });

    // Handle disconnection
    ws.on('close', () => {
      console.log(`🔌 Frontend WebSocket ${ip} disconnected`);
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error('❌ Frontend WebSocket error:', error);
    });

  } catch (error) {
    console.error('❌ Error in frontend WebSocket connection:', error);
    if (ws.readyState === WebSocket.OPEN) {
      ws.close(1011, 'Server error');
    }
  }
});

// 🎖️ FLEET COMMANDER: Frontend WebSocket Message Handler
async function handleFrontendMessage(ws, msg) {
  const pair = (msg.pair || 'xbtusdt').toLowerCase();
  const timeframe = msg.timeframe || '1h';

  switch (msg.type) {
    case 'subscribe':
    case 'requestData':
    case 'requestIndicators':
      // Add pair to active pairs if not already there
      if (!activePairs.includes(pair)) {
        activePairs.push(pair);
        console.log(`🎯 Added ${pair} to active pairs:`, activePairs);
      }

      // 🎖️ FLEET COMMANDER: Ensure data exists BEFORE calculating indicators
      await ensureDataExists(pair, timeframe);

      // Get fresh TradingView-accurate indicators AFTER data is available
      const indicators = calculateAllIndicators(pair, timeframe);

      // Send response with accurate data
      const response = {
        type: msg.type === 'requestData' ? 'historicalData' : 'indicators',
        pair,
        timeframe,
        data: msg.type === 'requestData' ?
          (historicalData[pair]?.[timeframe] || []) :
          indicators,
        lastUpdate: new Date().toISOString(),
        source: 'tradingview_accurate'
      };

      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(response));
      }
      break;

    case 'selectPair':
      // Handle pair selection
      if (!activePairs.includes(pair)) {
        activePairs.push(pair);
        console.log(`🎯 Added ${pair} to active pairs:`, activePairs);
      }

      // Send confirmation and initial data for all timeframes
      const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
      for (const tf of timeframes) {
        await ensureDataExists(pair, tf);
        const indicators = calculateAllIndicators(pair, tf);

        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({
            type: 'indicators',
            pair,
            timeframe: tf,
            data: indicators,
            lastUpdate: new Date().toISOString(),
            source: 'tradingview_accurate'
          }));
        }
      }
      break;

    case 'setStrategy':
      // Handle strategy change
      const strategy = msg.strategy || 'momentum_blast';
      console.log(`🎯 Setting strategy to ${strategy} for ${pair}`);

      // Send confirmation
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'strategyChanged',
          pair,
          strategy,
          message: `Strategy changed to ${strategy}`,
          lastUpdate: new Date().toISOString()
        }));
      }
      break;

    default:
      console.warn(`❓ Unknown frontend message type: ${msg.type}`);
      sendErrorToFrontend(ws, `Unknown message type: ${msg.type}`);
  }
}

// 🎖️ FLEET COMMANDER: Send Error to Frontend
function sendErrorToFrontend(ws, message) {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      type: 'error',
      message,
      timestamp: new Date().toISOString()
    }));
  }
}

// 🎖️ FLEET COMMANDER: Ensure Data Exists Before Calculations
async function ensureDataExists(pair, timeframe) {
  // Check if we already have sufficient data
  const existingData = historicalData[pair]?.[timeframe];
  if (existingData && existingData.length >= 50) {
    console.log(`✅ Using existing data for ${pair}/${timeframe} (${existingData.length} candles)`);
    return existingData;
  }

  console.log(`🔄 Fetching data for ${pair}/${timeframe} - insufficient data (${existingData?.length || 0} candles)`);

  // Fetch fresh data
  const candles = await fetchOHLC(pair, timeframe);

  if (candles && candles.length > 0) {
    // Store the data
    if (!historicalData[pair]) {
      historicalData[pair] = {};
    }
    historicalData[pair][timeframe] = candles;
    console.log(`✅ Stored ${candles.length} candles for ${pair}/${timeframe}`);
    return candles;
  } else {
    console.error(`❌ Failed to fetch data for ${pair}/${timeframe}`);
    return [];
  }
}

// 🎖️ FLEET COMMANDER: Frontend Error Handler
function sendErrorToFrontend(ws, message, details = '') {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      type: 'error',
      message,
      details,
      timestamp: new Date().toISOString(),
      source: 'server_tradingview_accurate'
    }));
  }
}

// 🎖️ FLEET COMMANDER: Frontend WebSocket Heartbeat
const frontendHeartbeat = setInterval(() => {
  frontendWss.clients.forEach((ws) => {
    if (ws.isAlive === false) {
      console.log('🔌 Terminating dead frontend WebSocket connection');
      return ws.terminate();
    }
    ws.isAlive = false;
    ws.ping();
  });
}, 30000); // Check every 30 seconds

// 🎖️ FLEET COMMANDER: Cleanup on server shutdown
process.on('SIGTERM', () => {
  clearInterval(frontendHeartbeat);
  frontendWss.clients.forEach(ws => ws.terminate());
});
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error('Express server port 3000 is already in use. Using alternative port 3001.')
    const backupServer = app.listen(3001, () => {
      console.log(`StarCrypt Enterprise server running on port 3001`)
    })
  } else {
    console.error(error)
  }
})

// 🎖️ FLEET COMMANDER: DISABLED - Kraken WebSocket server error handling
// wss.on('error', (error) => {
//   console.error(`[WSS] WebSocket server error: ${error.message}`)
// });

// 🎖️ FLEET COMMANDER: DISABLED - Old WebSocket handler conflicts with enhanced frontend handler
// All frontend connections now go through the enhanced handler on port 8080
// wss.on('connection', (ws, req) => {
//   const clientIp = req.socket.remoteAddress;
//   console.log(`🚫 OLD HANDLER: Client connected: ${clientIp}`);
//   ws.on('error', (error) => {
//     console.error(`🚫 OLD HANDLER: Error on client ${clientIp}:`, error);
//   });
//   ws.on('close', (code, reason) => {
//     const reasonString = reason ? reason.toString() : 'No reason given';
//     console.log(`🚫 OLD HANDLER: Client ${clientIp} disconnected. Code: ${code}, Reason: ${reasonString}`);
//     subscriptions.delete(ws);
//   });
//   ws.isAlive = true
//   ws.on('pong', () => {
//     ws.isAlive = true
//   })
// });

  // 🎖️ FLEET COMMANDER: DISABLED - Second WebSocket handler conflicts with enhanced frontend handler
  // All messages now go through the enhanced handler at line 85
  /*
  // Handle WebSocket messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message)
      console.log(`🚫 SECOND HANDLER: Message intercepted: ${data.type}`)

      // Handle strategy change
      if (data.type === 'strategy_change') {
        const strategy = data.strategy
        console.log(`Strategy changed to: ${strategy}`)

        // Send confirmation back to client
        ws.send(JSON.stringify({
          type: 'strategy_change_confirmation',
          strategy,
          status: 'success',
          message: `Strategy successfully changed to ${TRADING_STRATEGIES[strategy].name}`,
        }))

        // Send updated indicators for the strategy
        const strategyIndicators = TRADING_STRATEGIES[strategy].indicators
        ws.send(JSON.stringify({
          type: 'strategy_indicators',
          strategy,
          indicators: strategyIndicators,
        }))
      }

      // Handle coin change
      if (data.type === 'coin_change') {
        const pair = data.pair
        if (!pair) {
          console.error('WebSocket message error: pair is not defined in coin_change message')
          ws.send(JSON.stringify({
            type: 'coin_change_confirmation',
            status: 'error',
            message: 'Pair is required for coin change',
          }))
          return
        }
        
        // Subscribe to WebSocket updates for this pair
        try {
          krakenWebSocket.subscribe(pair);
        } catch (error) {
          console.error(`Failed to subscribe to ${pair} WebSocket updates:`, error);
        }

        console.log(`Coin changed to: ${pair}`)

        // Validate the pair
        if (SUPPORTED_PAIRS.includes(pair)) {
          // Send confirmation back to client
          ws.send(JSON.stringify({
            type: 'coin_change_confirmation',
            pair,
            status: 'success',
            message: `Coin successfully changed to ${pair}`,
          }))

          // Fetch data for the new coin and send it to the client
          fetchOHLC(pair, DEFAULT_TIMEFRAME, true)
            .then(() => calculateIndicatorsAllTimeframes(pair))
            .then(() => {
              ws.send(JSON.stringify({
                type: 'coin_data_updated',
                pair,
                status: 'success',
              }))
            })
            .catch(error => {
              console.error(`Error fetching data for ${pair}: ${error.message}`)
              ws.send(JSON.stringify({
                type: 'coin_data_updated',
                pair,
                status: 'error',
                message: `Error fetching data for ${pair}`,
              }))
            })
        } else {
          ws.send(JSON.stringify({
            type: 'coin_change_confirmation',
            pair,
            status: 'error',
            message: `Unsupported pair: ${pair}`,
          }))
        }
      }
    } catch (error) {
      console.error(`Error processing WebSocket message: ${error.message}`)
      ws.send(JSON.stringify({
        type: 'error',
        message: `Error processing message: ${error.message}`,
      }))
    }
  })
  */ // 🎖️ FLEET COMMANDER: End of disabled second message handler

  // 🎖️ FLEET COMMANDER: DISABLED - Orphaned code from disabled WebSocket handler
  // Handle WebSocket errors
  // ws.on('error', (error) => {
  //   // Properly log errors instead of sending undefined
  //   console.error(`WebSocket client error: ${error.message || 'Unknown error'}`)
  // })

  // Send initial data to client
  // ws.send(JSON.stringify({
  //   type: 'connection_established',
  //   message: 'Connected to StarCrypt server',
  //   supportedPairs: SUPPORTED_PAIRS,
  //   supportedTimeframes: TIMEFRAMES,
  //   defaultPair: DEFAULT_PAIR,
  //   defaultTimeframe: DEFAULT_TIMEFRAME,
  //   defaultStrategy: DEFAULT_STRATEGY,
  //   strategies: Object.keys(TRADING_STRATEGIES).map(key => ({
  //     id: key,
  //     name: TRADING_STRATEGIES[key].name,
  //     indicators: TRADING_STRATEGIES[key].indicators,
  //   })),
  // }))
// }) // 🎖️ FLEET COMMANDER: End of disabled orphaned code

const port = 3000

// app.use(express.static('.')) // This is redundant and potentially problematic, commented out
app.use(express.json())

// Configuration
const DEFAULT_PAIR = 'xbtusdt'
const DEFAULT_TIMEFRAME = '1h'
const DEFAULT_STRATEGY = 'admiral_toa'
const SUPPORTED_PAIRS = ['xbtusdt', 'ethusdt', 'ltcusdt', 'xrpusdt', 'adausdt', 'solusdt', 'dotusdt', 'dogeusdt']
const TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
const INTERVALS = { '1m': 1, '5m': 5, '15m': 15, '1h': 60, '4h': 240, '1d': 1440, '1w': 10080 }
const TIMEFRAME_SECONDS = { '1m': 60, '5m': 300, '15m': 900, '1h': 3600, '4h': 14400, '1d': 86400, '1w': 604800 }
const RATE_LIMIT_REQUESTS = 15 // Not currently used but kept for future rate limiting implementation
const RATE_LIMIT_WINDOW = 60000 // Not currently used but kept for future rate limiting implementation
const MAX_CANDLES = 720
const REQUEST_DELAY = 1000 // 1 second delay between API requests to avoid rate limiting

// Data storage
const historicalData = {}
const indicatorsData = {}
const lastLivePrice = {}
const subscriptions = new Map()
const activePairs = [] // Track active pairs for frontend connections

// Historical data file loading
function loadHistoricalDataFromFile(pair, timeframe) {
  try {
    const dataDir = path.join(__dirname, 'data')

    // Try JSON first, then CSV
    const jsonFileName = `${pair}_${timeframe}.json`
    const csvFileName = `${pair}_${timeframe}.csv`
    const jsonFilePath = path.join(dataDir, jsonFileName)
    const csvFilePath = path.join(dataDir, csvFileName)

    // Try JSON file first
    if (fs.existsSync(jsonFilePath)) {
      const fileData = fs.readFileSync(jsonFilePath, 'utf8')
      const parsedData = JSON.parse(fileData)

      console.log(`Loaded ${parsedData.length} historical candles for ${pair}/${timeframe} from JSON file`)

      // Initialize storage if needed
      if (!historicalData[pair]) {
        historicalData[pair] = {}
      }

      // Store the data
      historicalData[pair][timeframe] = parsedData

      return parsedData
    }

    // Try CSV file if JSON doesn't exist
    if (fs.existsSync(csvFilePath)) {
      const csvData = fs.readFileSync(csvFilePath, 'utf8')
      const parsedData = parseCSVToOHLC(csvData)

      if (parsedData && parsedData.length > 0) {
        console.log(`Loaded ${parsedData.length} historical candles for ${pair}/${timeframe} from CSV file`)

        // Initialize storage if needed
        if (!historicalData[pair]) {
          historicalData[pair] = {}
        }

        // Store the data
        historicalData[pair][timeframe] = parsedData

        // Optionally save as JSON for faster future loading
        try {
          fs.writeFileSync(jsonFilePath, JSON.stringify(parsedData, null, 2))
          console.log(`Converted and saved CSV data as JSON: ${jsonFileName}`)
        } catch (writeError) {
          console.warn(`Could not save JSON version: ${writeError.message}`)
        }

        return parsedData
      }
    }

    console.log(`No historical data file found for ${pair}/${timeframe} (tried ${jsonFileName} and ${csvFileName})`)
    return null
  } catch (error) {
    console.error(`Error loading historical data from file for ${pair}/${timeframe}:`, error.message)
    return null
  }
}

// Parse CSV data to OHLC format
function parseCSVToOHLC(csvData) {
  try {
    const lines = csvData.trim().split('\n')
    if (lines.length < 2) {
      console.error('CSV file has insufficient data')
      return null
    }

    // Get header to determine column positions
    const header = lines[0].toLowerCase().split(',').map(h => h.trim())

    // Find column indices (flexible column mapping)
    const timeIndex = header.findIndex(h => h.includes('time') || h.includes('date') || h.includes('timestamp'))
    const openIndex = header.findIndex(h => h.includes('open'))
    const highIndex = header.findIndex(h => h.includes('high'))
    const lowIndex = header.findIndex(h => h.includes('low'))
    const closeIndex = header.findIndex(h => h.includes('close'))
    const volumeIndex = header.findIndex(h => h.includes('volume') || h.includes('vol'))

    if (timeIndex === -1 || openIndex === -1 || highIndex === -1 || lowIndex === -1 || closeIndex === -1) {
      console.error('CSV missing required columns (time, open, high, low, close)')
      console.error('Available columns:', header)
      return null
    }

    const ohlcData = []

    // Process data rows
    for (let i = 1; i < lines.length; i++) {
      const row = lines[i].split(',').map(cell => cell.trim())

      if (row.length < Math.max(timeIndex, openIndex, highIndex, lowIndex, closeIndex) + 1) {
        continue // Skip incomplete rows
      }

      try {
        // Parse timestamp (handle various formats)
        let timestamp = row[timeIndex]
        let time

        if (timestamp.includes('-') || timestamp.includes('/')) {
          // Date string format
          time = Math.floor(new Date(timestamp).getTime() / 1000)
        } else {
          // Unix timestamp (seconds or milliseconds)
          time = parseInt(timestamp)
          if (time > 1e12) {
            time = Math.floor(time / 1000) // Convert milliseconds to seconds
          }
        }

        const open = parseFloat(row[openIndex])
        const high = parseFloat(row[highIndex])
        const low = parseFloat(row[lowIndex])
        const close = parseFloat(row[closeIndex])
        const volume = volumeIndex !== -1 ? parseFloat(row[volumeIndex]) : 0

        // Validate data
        if (isNaN(time) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
          continue // Skip invalid rows
        }

        // Create OHLC object in Kraken format (matching API structure)
        ohlcData.push({
          t: time * 1000, // Convert to milliseconds to match Kraken format
          o: open,
          h: high,
          l: low,
          c: close,
          v: volume,
          x: time * 1000, // Close time (same as open time for simplicity)
          vwap: (high + low + close) / 3, // Approximate VWAP
          count: 1
        })
      } catch (rowError) {
        console.warn(`Error parsing CSV row ${i}: ${rowError.message}`)
        continue
      }
    }

    // Sort by timestamp
    ohlcData.sort((a, b) => a.time - b.time)

    console.log(`Successfully parsed ${ohlcData.length} candles from CSV`)
    return ohlcData

  } catch (error) {
    console.error('Error parsing CSV data:', error.message)
    return null
  }
}

const KRAKEN_PAIRS = {
  xbtusdt: 'XBTUSD',
  ethusdt: 'ETHUSD',
  ltcusdt: 'LTCUSD',
  xrpusdt: 'XRPUSD',
  adausdt: 'ADAUSD',
  solusdt: 'SOLUSD',
  dotusdt: 'DOTUSD',
  dogeusdt: 'DOGEUSD',
}
function calculateStrategyScores(indicators, weights) {
  let weightedSum = 0
  const rationale = []
  const indicatorScores = {}
  
  // First pass: Calculate base scores for each indicator
  Object.entries(indicators).forEach(([indicator, data]) => {
    if (!data || data.value === undefined || data.value === 'N/A') {
      indicatorScores[indicator] = 0
      return
    }
    
    let score = 0
    
    // Handle different indicator value types
    if (typeof data.value === 'number') {
      // Normalize the value based on typical ranges
      if (indicator === 'rsi') {
        // RSI: 0-100, with 30/70 as common thresholds
        score = ((data.value - 50) / 50) * 2 // Scale to -2 to +2 range
      } else if (indicator === 'macd' && data.histogram !== undefined) {
        // MACD: Use histogram value, normalize by typical range
        score = Math.max(-2, Math.min(2, data.histogram * 10)) // Scale histogram
      } else if (indicator === 'bollingerBands' && data.position !== undefined) {
        // Bollinger Bands position (0-100% between bands)
        score = ((data.position - 50) / 50) * 2 // Scale to -2 to +2 range
      } else if (indicator === 'adx' && data.adx !== undefined) {
        // ADX: 0-100, with >25 indicating strong trend
        score = (data.adx - 25) / 25 // Scale to -1 to +3 range
        if (data.trend === 'up') score = Math.abs(score)
        else if (data.trend === 'down') score = -Math.abs(score)
      } else if (indicator === 'volume') {
        // Volume spike: 0 = normal, >0 = spike
        score = Math.min(2, data.value / 50) // Cap at +2
      } else if (indicator === 'sentiment') {
        // Sentiment score: -1 to +1, scale to -2 to +2
        score = data.value * 2
      }
    } else if (data.signalClass) {
      // Handle signal objects with signalClass
      switch (data.signalClass) {
        case 'degen-buy': score = 2; break
        case 'mild-buy': score = 1; break
        case 'neutral': score = 0; break
        case 'mild-sell': score = -1; break
        case 'degen-sell': score = -2; break
        default: score = 0
      }
    }
    
    indicatorScores[indicator] = score
  })
  
  // Second pass: Apply weights and calculate weighted sum
  Object.entries(weights).forEach(([indicator, weight]) => {
    const score = indicatorScores[indicator] || 0
    const weightedScore = score * weight
    weightedSum += weightedScore
    
    // Add to rationale if significant
    if (Math.abs(weightedScore) > 0.1) {
      const direction = score > 0 ? '↑' : score < 0 ? '↓' : '→'
      rationale.push(`${indicator} ${direction} ${Math.abs(score).toFixed(1)} (${(weight*100).toFixed(0)}%)`)
    }
  })
  
  // Add overall market context if available
  if (indicators.price && indicators.price.trend) {
    rationale.push(`Trend: ${indicators.price.trend}`)
  }
  
  console.log(`Strategy scores calculated: sum=${weightedSum.toFixed(2)}, indicators:`, 
    Object.entries(indicatorScores).map(([k,v]) => `${k}:${v.toFixed(2)}`).join(', '))
    
  return { 
    weightedSum, 
    rationale,
    indicatorScores // Include raw scores for debugging
  }
}

function generateStrategySignal({ weightedSum, rationale = [] }, baseTooltip = '') {
  // Ensure weightedSum is a number
  const score = Number(weightedSum) || 0
  
  // Log the input for debugging
  console.log(`Generating signal for score: ${score}, baseTooltip: ${baseTooltip}`)
  
  let signal, color, signalClass, confidence
  
  // Define signal thresholds and properties
  if (score >= 6) {
    signal = 'degen-buy'
    color = '#00FF00'
    signalClass = 'degen-buy'
    confidence = Math.min(0.95, 0.5 + (score * 0.05))
    console.log(`Strong buy signal generated (score: ${score})`)
  } else if (score >= 3) {
    signal = 'mild-buy'
    color = '#0000FF'
    signalClass = 'mild-buy'
    confidence = Math.min(0.95, 0.5 + (score * 0.05))
    console.log(`Mild buy signal generated (score: ${score})`)
  } else if (score <= -6) {
    signal = 'degen-sell'
    color = '#FF0000'
    signalClass = 'degen-sell'
    confidence = Math.min(0.95, 0.5 + (-score * 0.05))
    console.log(`Strong sell signal generated (score: ${score})`)
  } else if (score <= -3) {
    signal = 'mild-sell'
    color = '#FFA500'
    signalClass = 'mild-sell'
    confidence = Math.min(0.95, 0.5 + (-score * 0.05))
    console.log(`Mild sell signal generated (score: ${score})`)
  } else {
    signal = 'neutral'
    color = '#808080'
    signalClass = 'neutral'
    confidence = 0.5
    console.log(`Neutral signal (score: ${score})`)
  }
  
  // Build the tooltip with more context
  const rationaleText = rationale && rationale.length 
    ? rationale.join('; ')
    : 'No strong signals'
    
  const tooltip = `${baseTooltip}: ${rationaleText}. Confidence: ${(confidence * 100).toFixed(0)}%`
  
  console.log(`Signal generated: ${signal} (${color}), Confidence: ${(confidence * 100).toFixed(0)}%`)
  return { signal, color, signalClass, confidence, tooltip }
}

// Trading strategies - SYNCHRONIZED WITH FRONTEND js/global-variables.js
const TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    description: 'Advanced strategy combining multiple timeframes and indicators for high-probability trades',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    weights: { 
      rsi: 0.15,         // Momentum filter
      macd: 0.15,        // Trend confirmation
      bollingerBands: 0.1, // Volatility and mean reversion
      adx: 0.1,          // Trend strength
      stochRsi: 0.1,     // Momentum confirmation
      atr: 0.05,         // Volatility adjustment
      williamsR: 0.05,   // Overbought/oversold
      ultimateOscillator: 0.05, // Multi-timeframe momentum
      mfi: 0.05,         // Money flow
      vwap: 0.05,        // Volume-weighted price
      volume: 0.05,      // Volume confirmation
      ml: 0.05,          // Machine learning prediction
      sentiment: 0.03,   // Market sentiment
      fractal: 0.02,     // Pattern recognition
      entropy: 0.02,     // Market randomness
      correlation: 0.02, // Cross-asset correlation
      time_anomaly: 0.02 // Time-based patterns
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.admiral_toa.weights)
      const signals = []
      const metrics = {}
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Bollinger Bands conditions
      if (indicators.bollingerBands?.position < 20) signals.push('Price near lower band')
      if (indicators.bollingerBands?.position > 80) signals.push('Price near upper band')
      
      // ADX conditions
      if (indicators.adx?.adx > 25) signals.push('Strong trend')
      
      // Volume conditions
      if (indicators.volume?.value > 1.5) signals.push('High volume')
      
      // Sentiment conditions
      if (indicators.sentiment?.value > 0.5) signals.push('Bullish sentiment')
      if (indicators.sentiment?.value < -0.5) signals.push('Bearish sentiment')
      
      // Calculate final score with additional factors
      let finalScore = weightedSum
      
      // Add convergence bonus for multiple confirming signals
      const confirmingSignals = signals.filter(s => 
        s.includes('bullish') || 
        s.includes('oversold') ||
        s.includes('lower band')
      ).length
      
      if (confirmingSignals >= 3) {
        finalScore += 1.5
        signals.push('Multiple confirming signals')
      }
      
      // Add divergence detection
      if (indicators.price && indicators.macd) {
        if (indicators.price.trend === 'up' && indicators.macd.histogram < 0) {
          finalScore -= 1.0
          signals.push('Bearish divergence detected')
        } else if (indicators.price.trend === 'down' && indicators.macd.histogram > 0) {
          finalScore += 1.0
          signals.push('Bullish divergence detected')
        }
      }
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'TOA Convergence Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  momentum_blast: {
    name: 'Momentum Blast',
    description: 'High-momentum strategy focusing on strong directional moves with volume confirmation',
    indicators: ['rsi', 'stochRsi', 'macd', 'williamsR', 'mfi', 'volume'],
    weights: { 
      rsi: 0.25,       // Primary momentum filter
      macd: 0.2,       // Trend confirmation
      stochRsi: 0.15,  // Momentum confirmation
      williamsR: 0.15, // Overbought/oversold levels
      mfi: 0.15,       // Money flow confirmation
      volume: 0.1      // Volume confirmation
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.momentum_blast.weights)
      const signals = []
      const metrics = {}
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Stochastic RSI conditions
      if (indicators.stochRsi?.k < 20) signals.push('Stoch RSI oversold')
      if (indicators.stochRsi?.k > 80) signals.push('Stoch RSI overbought')
      
      // Williams %R conditions
      if (indicators.williamsR?.value > -20) signals.push('Williams %R overbought')
      if (indicators.williamsR?.value < -80) signals.push('Williams %R oversold')
      
      // MFI conditions
      if (indicators.mfi?.value > 80) signals.push('MFI overbought')
      if (indicators.mfi?.value < 20) signals.push('MFI oversold')
      
      // Volume conditions
      if (indicators.volume?.value > 2.0) signals.push('Very high volume')
      else if (indicators.volume?.value > 1.5) signals.push('High volume')
      
      // Calculate momentum score
      let momentumScore = 0
      
      // Add points for strong momentum signals
      if (indicators.rsi?.value > 70) momentumScore += 1
      if (indicators.stochRsi?.k > 80) momentumScore += 1
      if (indicators.macd?.histogram > 0 && indicators.macd.histogram > indicators.macd.signal) momentumScore += 1
      
      // Add points for volume confirmation
      if (indicators.volume?.value > 1.5) momentumScore += 0.5
      
      // Calculate final score with momentum adjustment
      let finalScore = weightedSum
      if (momentumScore >= 2.5) {
        finalScore += 1.0 // Strong momentum bonus
        signals.push('Strong momentum detected')
      }
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.momentumScore = momentumScore
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Momentum Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  tight_convergence: {
    name: 'Tight Convergence',
    description: 'Identifies low-volatility periods before major breakouts with trend confirmation',
    indicators: ['bollingerBands', 'atr', 'adx', 'vwap', 'macd'],
    weights: { 
      bollingerBands: 0.3,  // Volatility and squeeze detection
      atr: 0.25,           // Volatility measurement
      adx: 0.2,            // Trend strength
      vwap: 0.15,          // Volume-weighted price levels
      macd: 0.1            // Trend confirmation
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.tight_convergence.weights)
      const signals = []
      const metrics = {}
      
      // Bollinger Bands conditions
      const bb = indicators.bollingerBands
      if (bb) {
        const bandwidth = (bb.upper - bb.lower) / bb.middle
        if (bandwidth < 0.05) {
          signals.push('Bollinger Band squeeze')
          // Add bonus for squeeze
          weightedSum += 1.0
        }
        
        if (bb.position < 20) signals.push('Price near lower band')
        if (bb.position > 80) signals.push('Price near upper band')
      }
      
      // ATR conditions
      if (indicators.atr?.value) {
        const atrPercent = (indicators.atr.value / indicators.price) * 100
        if (atrPercent < 1.0) {
          signals.push('Low volatility (ATR < 1%)')
          // Add bonus for low volatility
          weightedSum += 0.5
        }
      }
      
      // ADX conditions
      if (indicators.adx?.adx > 25) {
        signals.push('Strong trend')
        // Add bonus for strong trend
        weightedSum += 0.5
      } else if (indicators.adx?.adx < 20) {
        signals.push('Weak trend')
      }
      
      // VWAP conditions
      if (indicators.vwap?.value) {
        const vwapDistance = ((indicators.price - indicators.vwap.value) / indicators.vwap.value) * 100
        if (Math.abs(vwapDistance) < 0.5) {
          signals.push('Price near VWAP')
          // Add bonus for mean reversion
          weightedSum += 0.5
        }
      }
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Calculate convergence score
      let convergenceScore = 0
      
      // Check for multiple confirming signals
      const confirmingSignals = signals.filter(s => 
        s.includes('squeeze') || 
        s.includes('low volatility') ||
        s.includes('near VWAP')
      ).length
      
      if (confirmingSignals >= 2) {
        convergenceScore += 1.0
        signals.push('Multiple convergence signals')
      }
      
      // Calculate final score with convergence adjustment
      let finalScore = weightedSum + convergenceScore
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.convergenceScore = convergenceScore
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Convergence Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  top_bottom_feeder: {
    name: 'Top Bottom Feeder',
    description: 'Specializes in identifying extreme market conditions for mean reversion opportunities',
    indicators: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'bollingerBands', 'mfi'],
    weights: { 
      rsi: 0.25,               // Primary overbought/oversold indicator
      williamsR: 0.2,          // Additional confirmation
      stochRsi: 0.2,           // Momentum confirmation
      ultimateOscillator: 0.15, // Multi-timeframe confirmation
      bollingerBands: 0.1,     // Volatility context
      mfi: 0.1                 // Money flow confirmation
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.top_bottom_feeder.weights)
      const signals = []
      const metrics = {}
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // Stochastic RSI conditions
      if (indicators.stochRsi?.k < 20) signals.push('Stoch RSI oversold')
      if (indicators.stochRsi?.k > 80) signals.push('Stoch RSI overbought')
      
      // Williams %R conditions
      if (indicators.williamsR?.value > -20) signals.push('Williams %R overbought')
      if (indicators.williamsR?.value < -80) signals.push('Williams %R oversold')
      
      // Ultimate Oscillator conditions
      if (indicators.ultimateOscillator?.value < 30) signals.push('Ultimate Oscillator oversold')
      if (indicators.ultimateOscillator?.value > 70) signals.push('Ultimate Oscillator overbought')
      
      // Bollinger Bands conditions
      if (indicators.bollingerBands?.position < 10) signals.push('Price near lower band (extreme)')
      if (indicators.bollingerBands?.position > 90) signals.push('Price near upper band (extreme)')
      
      // MFI conditions
      if (indicators.mfi?.value < 20) signals.push('MFI oversold')
      if (indicators.mfi?.value > 80) signals.push('MFI overbought')
      
      // Calculate extreme conditions score
      let extremeScore = 0
      const oversoldSignals = signals.filter(s => s.includes('oversold') || s.includes('lower band')).length
      const overboughtSignals = signals.filter(s => s.includes('overbought') || s.includes('upper band')).length
      
      // Add bonus for multiple confirming signals
      if (oversoldSignals >= 3) {
        extremeScore += 2.0  // Strong oversold condition
        signals.push('Multiple oversold signals')
      } else if (overboughtSignals >= 3) {
        extremeScore -= 2.0  // Strong overbought condition
        signals.push('Multiple overbought signals')
      } else if (oversoldSignals >= 2) {
        extremeScore += 1.5
        signals.push('Moderate oversold signals')
      } else if (overboughtSignals >= 2) {
        extremeScore -= 1.5
        signals.push('Moderate overbought signals')
      }
      
      // Check for divergences
      if (indicators.price && indicators.rsi) {
        if (indicators.price.trend === 'down' && indicators.rsi.value > 50) {
          extremeScore += 1.0  // Bullish divergence
          signals.push('Bullish RSI divergence')
        } else if (indicators.price.trend === 'up' && indicators.rsi.value < 50) {
          extremeScore -= 1.0  // Bearish divergence
          signals.push('Bearish RSI divergence')
        }
      }
      
      // Calculate final score with extreme conditions adjustment
      let finalScore = weightedSum + extremeScore
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.extremeScore = extremeScore
      metrics.oversoldSignals = oversoldSignals
      metrics.overboughtSignals = overboughtSignals
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Extreme Conditions: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  scalping_sniper: {
    name: 'Scalping Sniper',
    description: 'Aggressive strategy for short-term price movements with tight stop losses',
    indicators: ['rsi', 'macd', 'volume', 'bollingerBands', 'atr'],
    weights: { 
      rsi: 0.3,             // Short-term momentum
      macd: 0.25,           // Trend direction
      volume: 0.2,          // Volume confirmation
      bollingerBands: 0.15, // Volatility bands
      atr: 0.1              // Volatility measurement
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.scalping_sniper.weights)
      const signals = []
      const metrics = {}
      
      // RSI conditions for short-term momentum
      if (indicators.rsi?.value > 60) signals.push('RSI bullish')
      if (indicators.rsi?.value < 40) signals.push('RSI bearish')
      
      // MACD conditions
      const macd = indicators.macd
      if (macd) {
        if (macd.histogram > 0 && macd.histogram > macd.signal) {
          signals.push('MACD bullish crossover')
        } else if (macd.histogram < 0 && macd.histogram < macd.signal) {
          signals.push('MACD bearish crossover')
        }
      }
      
      // Volume conditions
      const volumeSpike = indicators.volume?.value > 2.0
      if (volumeSpike) signals.push('Volume spike')
      
      // Bollinger Bands conditions
      const bb = indicators.bollingerBands
      if (bb) {
        const bandwidth = (bb.upper - bb.lower) / bb.middle
        if (bandwidth < 0.03) signals.push('Tight range (squeeze)')
        
        if (bb.position < 20) signals.push('Near lower band')
        if (bb.position > 80) signals.push('Near upper band')
      }
      
      // ATR conditions for volatility measurement
      const atrPercent = indicators.atr?.value ? (indicators.atr.value / indicators.price) * 100 : 0
      if (atrPercent > 1.5) signals.push('High volatility')
      
      // Calculate scalping score
      let scalpingScore = 0
      
      // Add points for strong momentum
      if (indicators.rsi?.value > 70) scalpingScore += 1.0
      if (indicators.rsi?.value < 30) scalpingScore -= 1.0
      
      // Add points for volume confirmation
      if (volumeSpike) {
        scalpingScore += (indicators.macd?.histogram > 0 ? 1.5 : -1.5)
      }
      
      // Add points for volatility
      if (atrPercent > 2.0) {
        scalpingScore += 0.5
        signals.push('Very high volatility')
      }
      
      // Calculate final score with scalping adjustments
      let finalScore = weightedSum + scalpingScore
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.scalpingScore = scalpingScore
      metrics.atrPercent = atrPercent
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Scalp Setup: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  fractal_surge: {
    name: 'Fractal Surge',
    description: 'Identifies and capitalizes on fractal pattern breakouts with volume confirmation',
    indicators: ['rsi', 'macd', 'bollingerBands', 'williamsR', 'adx', 'fractal'],
    weights: { 
      rsi: 0.2,             // Momentum confirmation
      macd: 0.2,            // Trend direction
      bollingerBands: 0.2,  // Volatility context
      williamsR: 0.15,      // Overbought/oversold levels
      adx: 0.15,            // Trend strength
      fractal: 0.1          // Pattern recognition
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.fractal_surge.weights)
      const signals = []
      const metrics = {}
      
      // Fractal conditions
      if (indicators.fractal?.bullish) signals.push('Bullish fractal detected')
      if (indicators.fractal?.bearish) signals.push('Bearish fractal detected')
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Bollinger Bands conditions
      const bb = indicators.bollingerBands
      if (bb) {
        if (bb.position < 20) signals.push('Price near lower band')
        if (bb.position > 80) signals.push('Price near upper band')
        
        // Check for squeeze
        const bandwidth = (bb.upper - bb.lower) / bb.middle
        if (bandwidth < 0.03) signals.push('Bollinger Band squeeze')
      }
      
      // Williams %R conditions
      if (indicators.williamsR?.value > -20) signals.push('Williams %R overbought')
      if (indicators.williamsR?.value < -80) signals.push('Williams %R oversold')
      
      // ADX conditions
      if (indicators.adx?.adx > 25) signals.push('Strong trend')
      
      // Calculate fractal score
      let fractalScore = 0
      
      // Add points for confirmed fractal breakouts
      if (indicators.fractal?.bullish) {
        // Check for confirmation from other indicators
        let confirmations = 0
        if (indicators.macd?.histogram > 0) confirmations++
        if (indicators.rsi?.value > 50) confirmations++
        if (indicators.williamsR?.value > -50) confirmations++
        
        if (confirmations >= 2) {
          fractalScore += 1.5
          signals.push('Bullish fractal confirmed')
        }
      }
      
      if (indicators.fractal?.bearish) {
        // Check for confirmation from other indicators
        let confirmations = 0
        if (indicators.macd?.histogram < 0) confirmations++
        if (indicators.rsi?.value < 50) confirmations++
        if (indicators.williamsR?.value < -50) confirmations++        
        if (confirmations >= 2) {
          fractalScore -= 1.5
          signals.push('Bearish fractal confirmed')
        }
      }
      
      // Add points for volume confirmation
      if (indicators.volume?.value > 1.5) {
        fractalScore *= 1.5
        signals.push('Volume confirms move')
      }
      
      // Calculate final score with fractal adjustment
      let finalScore = weightedSum + fractalScore
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.fractalScore = fractalScore
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Fractal Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  sentiment_blaster: {
    name: 'Sentiment Blaster',
    description: 'Combines market sentiment with technical indicators for high-probability trades',
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR', 'sentiment'],
    weights: { 
      rsi: 0.25,        // Momentum filter
      macd: 0.2,        // Trend confirmation
      volume: 0.2,      // Volume confirmation
      mfi: 0.15,        // Money flow
      williamsR: 0.1,   // Overbought/oversold
      sentiment: 0.1    // Market sentiment
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.sentiment_blaster.weights)
      const signals = []
      const metrics = {}
      
      // Sentiment conditions
      const sentiment = indicators.sentiment?.value || 0
      if (sentiment > 0.7) signals.push('Very bullish sentiment')
      else if (sentiment > 0.3) signals.push('Bullish sentiment')
      else if (sentiment < -0.7) signals.push('Very bearish sentiment')
      else if (sentiment < -0.3) signals.push('Bearish sentiment')
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Volume conditions
      if (indicators.volume?.value > 2.0) signals.push('Very high volume')
      else if (indicators.volume?.value > 1.5) signals.push('High volume')
      
      // MFI conditions
      if (indicators.mfi?.value < 20) signals.push('MFI oversold')
      if (indicators.mfi?.value > 80) signals.push('MFI overbought')
      
      // Williams %R conditions
      if (indicators.williamsR?.value > -20) signals.push('Williams %R overbought')
      if (indicators.williamsR?.value < -80) signals.push('Williams %R oversold')
      
      // Calculate sentiment score
      let sentimentScore = 0
      
      // Strong sentiment alignment bonus
      if (sentiment > 0.7 && indicators.macd?.histogram > 0) {
        sentimentScore += 2.0
        signals.push('Strong bullish alignment')
      } else if (sentiment < -0.7 && indicators.macd?.histogram < 0) {
        sentimentScore -= 2.0
        signals.push('Strong bearish alignment')
      }
      
      // Volume confirmation bonus
      if (Math.abs(sentiment) > 0.5 && indicators.volume?.value > 1.5) {
        sentimentScore += (sentiment > 0 ? 1.0 : -1.0)
        signals.push('Volume confirms sentiment')
      }
      
      // Momentum confirmation
      if (sentiment > 0 && indicators.rsi?.value > 50) {
        sentimentScore += 0.5
      } else if (sentiment < 0 && indicators.rsi?.value < 50) {
        sentimentScore -= 0.5
      }
      
      // Calculate final score with sentiment adjustment
      let finalScore = weightedSum + sentimentScore
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.sentimentScore = sentimentScore
      metrics.sentimentValue = sentiment
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Sentiment Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  vwap_guardian: {
    name: 'VWAP Guardian',
    description: 'Uses Volume-Weighted Average Price as a key level for trade entries and exits',
    indicators: ['vwap', 'rsi', 'macd', 'volume', 'bollingerBands', 'price'],
    weights: { 
      vwap: 0.35,        // Primary trend indicator
      rsi: 0.2,          // Momentum filter
      macd: 0.2,         // Trend confirmation
      volume: 0.15,      // Volume confirmation
      bollingerBands: 0.1 // Volatility context
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.vwap_guardian.weights)
      const signals = []
      const metrics = {}
      
      // VWAP conditions
      const vwap = indicators.vwap?.value
      const price = indicators.price?.value
      let priceVwapRatio = 0
      let vwapSignal = 0
      
      if (vwap && price) {
        priceVwapRatio = ((price - vwap) / vwap) * 100 // Percentage above/below VWAP
        
        if (priceVwapRatio > 2) {
          signals.push('Price > 2% above VWAP')
          vwapSignal = -1.0 // Bearish signal
        } else if (priceVwapRatio > 0.5) {
          signals.push('Price slightly above VWAP')
          vwapSignal = -0.5
        } else if (priceVwapRatio < -2) {
          signals.push('Price > 2% below VWAP')
          vwapSignal = 1.0 // Bullish signal
        } else if (priceVwapRatio < -0.5) {
          signals.push('Price slightly below VWAP')
          vwapSignal = 0.5
        } else {
          signals.push('Price near VWAP')
        }
      }
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Volume conditions
      if (indicators.volume?.value > 2.0) signals.push('Very high volume')
      else if (indicators.volume?.value > 1.5) signals.push('High volume')
      
      // Bollinger Bands conditions
      const bb = indicators.bollingerBands
      if (bb) {
        if (bb.position < 20) signals.push('Price near lower band')
        if (bb.position > 80) signals.push('Price near upper band')
      }
      
      // Calculate VWAP score
      let vwapScore = vwapSignal
      
      // Add confirmation from other indicators
      if (vwapSignal > 0) {
        // Bullish VWAP signal confirmation
        if (indicators.macd?.histogram > 0) vwapScore += 0.5
        if (indicators.rsi?.value < 50) vwapScore += 0.5
        if (bb?.position < 30) vwapScore += 0.5
      } else if (vwapSignal < 0) {
        // Bearish VWAP signal confirmation
        if (indicators.macd?.histogram < 0) vwapScore -= 0.5
        if (indicators.rsi?.value > 50) vwapScore -= 0.5
        if (bb?.position > 70) vwapScore -= 0.5
      }
      
      // Volume multiplier
      const volumeMultiplier = indicators.volume?.value > 1.5 ? 1.5 : 1.0
      
      // Calculate final score with VWAP adjustment
      let finalScore = weightedSum + (vwapScore * volumeMultiplier)
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.vwapScore = vwapScore
      metrics.priceVwapRatio = priceVwapRatio
      metrics.volumeMultiplier = volumeMultiplier
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'VWAP Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  correlation_hunter: {
    name: 'Correlation Hunter',
    description: 'Identifies trading opportunities based on cross-asset correlations and technicals',
    indicators: ['correlation', 'rsi', 'macd', 'volume', 'bollingerBands', 'price'],
    weights: { 
      correlation: 0.35, // Primary correlation strength
      rsi: 0.2,         // Momentum filter
      macd: 0.2,        // Trend confirmation
      volume: 0.15,     // Volume confirmation
      bollingerBands: 0.1 // Volatility context
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.correlation_hunter.weights)
      const signals = []
      const metrics = {}
      
      // Correlation conditions
      const correlation = indicators.correlation?.value || 0
      const correlationStrength = Math.abs(correlation)
      
      if (correlationStrength > 0.8) signals.push('Very strong correlation')
      else if (correlationStrength > 0.6) signals.push('Strong correlation')
      else if (correlationStrength > 0.4) signals.push('Moderate correlation')
      
      // Determine correlation direction
      if (correlation > 0) signals.push('Positive correlation')
      else if (correlation < 0) signals.push('Negative correlation')
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Volume conditions
      if (indicators.volume?.value > 2.0) signals.push('Very high volume')
      else if (indicators.volume?.value > 1.5) signals.push('High volume')
      
      // Bollinger Bands conditions
      const bb = indicators.bollingerBands
      if (bb) {
        if (bb.position < 20) signals.push('Price near lower band')
        if (bb.position > 80) signals.push('Price near upper band')
      }
      
      // Calculate correlation score
      let correlationScore = 0
      
      // Strong correlation signals
      if (correlationStrength > 0.7) {
        correlationScore = correlation * 2.0 // Scale by correlation strength
        signals.push('Trading correlation signal')
      }
      
      // Add confirmation from other indicators
      if (correlationScore !== 0) {
        // For positive correlation (assets move together)
        if (correlation > 0) {
          if (indicators.macd?.histogram > 0) correlationScore += 0.5
          if (indicators.rsi?.value > 50) correlationScore += 0.5
          if (bb?.position > 70) correlationScore += 0.5
        } 
        // For negative correlation (assets move opposite)
        else if (correlation < 0) {
          if (indicators.macd?.histogram < 0) correlationScore += 0.5
          if (indicators.rsi?.value < 50) correlationScore += 0.5
          if (bb?.position < 30) correlationScore += 0.5
        }
      }
      
      // Volume multiplier
      const volumeMultiplier = indicators.volume?.value > 1.5 ? 1.5 : 1.0
      
      // Calculate final score with correlation adjustment
      let finalScore = weightedSum + (correlationScore * volumeMultiplier)
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.correlationScore = correlationScore
      metrics.correlationValue = correlation
      metrics.correlationStrength = correlationStrength
      metrics.volumeMultiplier = volumeMultiplier
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Correlation Analysis: ' + signals.slice(0, 3).join(', ')
      )
    },
  },
  random_walk: {
    name: 'Random Walk Prototype',
    description: 'Experimental strategy combining technical indicators with controlled randomness',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'volume', 'williamsR', 'price'],
    weights: { 
      rsi: 0.2,             // Momentum
      macd: 0.2,            // Trend
      bollingerBands: 0.15, // Volatility
      adx: 0.15,            // Trend strength
      volume: 0.15,         // Volume confirmation
      williamsR: 0.15       // Overbought/oversold
    },
    logic: (indicators) => {
      // Calculate base scores
      const { weightedSum, rationale, indicatorScores } = calculateStrategyScores(indicators, TRADING_STRATEGIES.random_walk.weights)
      const signals = []
      const metrics = {}
      
      // RSI conditions
      if (indicators.rsi?.value < 30) signals.push('RSI oversold')
      if (indicators.rsi?.value > 70) signals.push('RSI overbought')
      
      // MACD conditions
      if (indicators.macd?.histogram > 0) signals.push('MACD bullish')
      if (indicators.macd?.histogram < 0) signals.push('MACD bearish')
      
      // Bollinger Bands conditions
      const bb = indicators.bollingerBands
      if (bb) {
        if (bb.position < 20) signals.push('Price near lower band')
        if (bb.position > 80) signals.push('Price near upper band')
      }
      
      // ADX conditions
      if (indicators.adx?.adx > 25) signals.push('Strong trend')
      
      // Volume conditions
      if (indicators.volume?.value > 2.0) signals.push('Very high volume')
      else if (indicators.volume?.value > 1.5) signals.push('High volume')
      
      // Williams %R conditions
      if (indicators.williamsR?.value > -20) signals.push('Williams %R overbought')
      if (indicators.williamsR?.value < -80) signals.push('Williams %R oversold')
      
      // Calculate technical score
      let technicalScore = weightedSum
      
      // Add controlled randomness (reduced impact compared to original)
      const randomFactor = (Math.random() * 2 - 1) * 1.5 // Between -1.5 and 1.5
      const randomSignal = randomFactor > 0.8 ? 'Strong random buy bias' : 
                          randomFactor < -0.8 ? 'Strong random sell bias' :
                          randomFactor > 0.3 ? 'Slight random buy bias' :
                          randomFactor < -0.3 ? 'Slight random sell bias' : 'Neutral random'
      
      signals.push(randomSignal)
      
      // Calculate final score with random adjustment
      let finalScore = technicalScore + randomFactor
      
      // Add metrics for debugging
      metrics.signals = signals
      metrics.technicalScore = technicalScore
      metrics.randomFactor = randomFactor
      metrics.randomSignal = randomSignal
      metrics.indicatorScores = indicatorScores
      metrics.finalScore = finalScore
      
      return generateStrategySignal(
        { 
          weightedSum: finalScore, 
          rationale: [...rationale, ...signals],
          metrics
        },
        'Random Walk: ' + signals.slice(0, 3).join(', ')
      )
    },
  },

  quantum_entropy: {
    name: 'Quantum Entropy',
    indicators: ['bollingerBands', 'atr', 'adx', 'williamsR', 'macd', 'entropy'],
    weights: { bollingerBands: 0.2, atr: 0.2, adx: 0.15, williamsR: 0.15, macd: 0.15, entropy: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.quantum_entropy.weights)
      return generateStrategySignal(scores, 'Market chaos and volatility breakout analysis')
    },
  },

  time_anomaly: {
    name: 'Time Anomaly',
    indicators: ['rsi', 'macd', 'time_anomaly'],
    weights: { rsi: 0.3, macd: 0.3, time_anomaly: 0.4 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_anomaly.weights)
      return generateStrategySignal(scores, 'Statistical anomaly in price returns')
    },
  },

  ml_predictor: {
    name: 'ML Predictor',
    indicators: ['rsi', 'macd'],
    weights: { rsi: 0.5, macd: 0.5 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ml_predictor.weights)
      return generateStrategySignal(scores, 'Awaiting ML model integration')
    },
  },

  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    weights: { rsi: 0.15, macd: 0.15, bollingerBands: 0.1, ml: 0.2, sentiment: 0.15, entropy: 0.1, correlation: 0.1, time_anomaly: 0.05 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.neural_network_navigator.weights)
      return generateStrategySignal(scores, 'AI neural network pattern recognition')
    },
  },

  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation'],
    weights: { rsi: 0.15, stochRsi: 0.15, macd: 0.15, ml: 0.2, sentiment: 0.15, volume: 0.1, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.deep_learning_diver.weights)
      return generateStrategySignal(scores, 'Deep learning order flow analysis')
    },
  },

  ai_pattern_prophet: {
    name: 'AI Pattern Prophet',
    indicators: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal'],
    weights: { bollingerBands: 0.15, macd: 0.15, adx: 0.1, ml: 0.2, entropy: 0.15, time_anomaly: 0.15, fractal: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ai_pattern_prophet.weights)
      return generateStrategySignal(scores, 'AI pattern recognition and completion probability')
    },
  },

  machine_learning_momentum: {
    name: 'AI ML Momentum Master',
    indicators: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    weights: { rsi: 0.15, macd: 0.15, williamsR: 0.1, ml: 0.2, volume: 0.15, sentiment: 0.15, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.machine_learning_momentum.weights)
      return generateStrategySignal(scores, 'ML-enhanced momentum analysis')
    },
  },

  sentiment_analysis_surfer: {
    name: 'AI Sentiment Analysis Surfer',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    weights: { rsi: 0.1, macd: 0.1, ml: 0.15, sentiment: 0.3, volume: 0.15, correlation: 0.1, entropy: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.sentiment_analysis_surfer.weights)
      return generateStrategySignal(scores, 'Social sentiment and market mood analysis')
    },
  },

  cross_asset_nebula: {
    name: 'Cross-Asset Nebula',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'atr', 'correlation'],
    weights: { rsi: 0.15, macd: 0.2, bollingerBands: 0.15, adx: 0.15, atr: 0.15, correlation: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.cross_asset_nebula.weights)
      return generateStrategySignal(scores, 'Cross-market correlation analysis')
    },
  },

  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR'],
    weights: { rsi: 0.2, stochRsi: 0.2, bollingerBands: 0.2, macd: 0.2, williamsR: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_warp_scalper.weights)
      return generateStrategySignal(scores, 'Multi-timeframe scalping analysis')
    },
  },

  x_sentiment_blaster: {
    name: 'X Sentiment Blaster',
    indicators: ['rsi', 'macd', 'mfi', 'ultimateOscillator', 'adx'],
    weights: { rsi: 0.25, macd: 0.2, mfi: 0.25, ultimateOscillator: 0.2, adx: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.x_sentiment_blaster.weights)
      return generateStrategySignal(scores, 'Extreme sentiment contrarian analysis')
    },
  },

  ichimoku_cloud_master: {
    name: 'Ichimoku Cloud Master',
    indicators: ['ichimoku', 'rsi', 'volume', 'atr', 'parabolicSar'],
    weights: { ichimoku: 0.4, rsi: 0.2, volume: 0.15, atr: 0.15, parabolicSar: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.ichimoku_cloud_master.weights)
      return generateStrategySignal(scores, 'Comprehensive Ichimoku cloud analysis with trend confirmation')
    },
  },

  volume_flow_hunter: {
    name: 'Volume Flow Hunter',
    indicators: ['obv', 'chaikinMoneyFlow', 'mfi', 'volume', 'vwap'],
    weights: { obv: 0.25, chaikinMoneyFlow: 0.25, mfi: 0.2, volume: 0.15, vwap: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.volume_flow_hunter.weights)
      return generateStrategySignal(scores, 'Advanced volume flow and money flow analysis')
    },
  },

  momentum_oscillator_pro: {
    name: 'Momentum Oscillator Pro',
    indicators: ['cci', 'rsi', 'stochRsi', 'williamsR', 'ultimateOscillator'],
    weights: { cci: 0.25, rsi: 0.2, stochRsi: 0.2, williamsR: 0.2, ultimateOscillator: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.momentum_oscillator_pro.weights)
      return generateStrategySignal(scores, 'Multi-oscillator momentum convergence analysis')
    },
  },

  trend_strength_analyzer: {
    name: 'Trend Strength Analyzer',
    indicators: ['aroon', 'adx', 'parabolicSar', 'trix', 'macd'],
    weights: { aroon: 0.25, adx: 0.25, parabolicSar: 0.2, trix: 0.15, macd: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.trend_strength_analyzer.weights)
      return generateStrategySignal(scores, 'Comprehensive trend strength and direction analysis')
    },
  },

  institutional_flow_tracker: {
    name: 'Institutional Flow Tracker',
    indicators: ['vwap', 'obv', 'chaikinMoneyFlow', 'volume', 'correlation'],
    weights: { vwap: 0.3, obv: 0.25, chaikinMoneyFlow: 0.2, volume: 0.15, correlation: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.institutional_flow_tracker.weights)
      return generateStrategySignal(scores, 'Track institutional money flow and smart money movements')
    },
  },

  // Additional strategies from frontend to ensure consistency
  time_anomaly_detector: {
    name: 'Time Anomaly Detector',
    indicators: ['rsi', 'macd', 'time_anomaly', 'entropy'],
    weights: { rsi: 0.25, macd: 0.25, time_anomaly: 0.3, entropy: 0.2 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.time_anomaly_detector.weights)
      return generateStrategySignal(scores, 'Detect statistical anomalies in price patterns')
    },
  },

  volatility_breakout_hunter: {
    name: 'Volatility Breakout Hunter',
    indicators: ['atr', 'bollingerBands', 'adx', 'volume', 'rsi'],
    weights: { atr: 0.3, bollingerBands: 0.25, adx: 0.2, volume: 0.15, rsi: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.volatility_breakout_hunter.weights)
      return generateStrategySignal(scores, 'Hunt for volatility expansion and breakout opportunities')
    },
  },

  mean_reversion_master: {
    name: 'Mean Reversion Master',
    indicators: ['rsi', 'bollingerBands', 'williamsR', 'stochRsi', 'mfi'],
    weights: { rsi: 0.25, bollingerBands: 0.25, williamsR: 0.2, stochRsi: 0.15, mfi: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.mean_reversion_master.weights)
      return generateStrategySignal(scores, 'Master mean reversion opportunities in oversold/overbought conditions')
    },
  },

  momentum_divergence_spotter: {
    name: 'Momentum Divergence Spotter',
    indicators: ['rsi', 'macd', 'stochRsi', 'williamsR', 'mfi'],
    weights: { rsi: 0.25, macd: 0.25, stochRsi: 0.2, williamsR: 0.15, mfi: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.momentum_divergence_spotter.weights)
      return generateStrategySignal(scores, 'Spot momentum divergences for trend reversal signals')
    },
  },

  volume_profile_analyzer: {
    name: 'Volume Profile Analyzer',
    indicators: ['volume', 'vwap', 'obv', 'chaikinMoneyFlow', 'mfi'],
    weights: { volume: 0.3, vwap: 0.25, obv: 0.2, chaikinMoneyFlow: 0.15, mfi: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.volume_profile_analyzer.weights)
      return generateStrategySignal(scores, 'Analyze volume profile for support/resistance levels')
    },
  },

  multi_timeframe_confluence: {
    name: 'Multi-Timeframe Confluence',
    indicators: ['rsi', 'macd', 'adx', 'bollingerBands', 'volume'],
    weights: { rsi: 0.25, macd: 0.25, adx: 0.2, bollingerBands: 0.15, volume: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.multi_timeframe_confluence.weights)
      return generateStrategySignal(scores, 'Find confluence across multiple timeframes for high-probability setups')
    },
  },

  // FRONTEND STRATEGIES - Added to match js/global-variables.js
  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    weights: { rsi: 0.2, macd: 0.2, bollingerBands: 0.15, ml: 0.25, sentiment: 0.1, entropy: 0.05, correlation: 0.025, time_anomaly: 0.025 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.neural_network_navigator.weights)
      return generateStrategySignal(scores, 'AI-powered pattern recognition with neural networks')
    },
  },

  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'volume', 'atr'],
    weights: { rsi: 0.3, stochRsi: 0.25, macd: 0.2, volume: 0.15, atr: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.momentum_blast.weights)
      return generateStrategySignal(scores, 'High-momentum breakout strategy')
    },
  },

  tight_convergence: {
    name: 'Tight Convergence',
    indicators: ['bollingerBands', 'atr', 'adx', 'rsi', 'volume'],
    weights: { bollingerBands: 0.3, atr: 0.25, adx: 0.2, rsi: 0.15, volume: 0.1 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.tight_convergence.weights)
      return generateStrategySignal(scores, 'Low volatility convergence setup')
    },
  },

  top_bottom_feeder: {
    name: 'Top Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'williamsR', 'mfi', 'bollingerBands'],
    weights: { rsi: 0.25, stochRsi: 0.25, williamsR: 0.2, mfi: 0.15, bollingerBands: 0.15 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.top_bottom_feeder.weights)
      return generateStrategySignal(scores, 'Reversal strategy for overbought/oversold conditions')
    },
  },

  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    weights: { rsi: 0.15, stochRsi: 0.1, macd: 0.15, ml: 0.3, sentiment: 0.1, volume: 0.1, correlation: 0.05, entropy: 0.05 },
    logic: (indicators) => {
      const scores = calculateStrategyScores(indicators, TRADING_STRATEGIES.deep_learning_diver.weights)
      return generateStrategySignal(scores, 'Deep learning neural networks for complex pattern analysis')
    },
  },
}

// Indicator calculations (unchanged)
function calculateRSI(closes, period = 14) {
  // Debug: Check what data we're receiving
  console.log('🔍 RSI DEBUG: closes type:', typeof closes, 'length:', Array.isArray(closes) ? closes.length : 'not array');
  console.log('🔍 RSI DEBUG: first few closes:', Array.isArray(closes) ? closes.slice(0, 5) : closes);

  // Use TradingView-accurate RSI calculation with Wilder's smoothing
  const result = TradingViewAccurateIndicators.calculateRSI(closes, period);

  console.log('🔍 RSI DEBUG: TradingView result:', result);

  if (result.value === null) {
    return { value: null, error: result.error };
  }

  const rsi = result.value;

  // RSI interpretation matching TradingView standards:
  let signalClass = 'neutral';
  if (rsi > 70) {
    signalClass = 'degen-sell';
  } else if (rsi > 60) {
    signalClass = 'mild-sell';
  } else if (rsi < 30) {
    signalClass = 'degen-buy';
  } else if (rsi < 40) {
    signalClass = 'mild-buy';
  }

  return {
    value: rsi,
    overbought: result.overbought,
    oversold: result.oversold,
    signalClass,
    color: rsi > 70 ? '#FF0000' : rsi > 60 ? '#FFA500' : rsi < 30 ? '#00FF00' : rsi < 40 ? '#0000FF' : '#808080',
    tooltip: `RSI: ${rsi.toFixed(2)} - TradingView Accurate`
  };
}

function calculateStochRSI(closes, period = 14) {
  // Use TradingView-accurate Stochastic RSI calculation
  const result = TradingViewAccurateIndicators.calculateStochRSI(closes, period, period);

  if (result.value === null) {
    return { value: null, error: result.error };
  }

  const stoch = result.value;

  // Determine signal class based on StochRSI value
  let signalClass = 'neutral';
  if (stoch > 80) {
    signalClass = 'degen-sell';
  } else if (stoch > 70) {
    signalClass = 'mild-sell';
  } else if (stoch < 20) {
    signalClass = 'degen-buy';
  } else if (stoch < 30) {
    signalClass = 'mild-buy';
  }

  return {
    value: stoch,
    overbought: result.overbought,
    oversold: result.oversold,
    signalClass,
    color: stoch > 80 ? '#FF0000' : stoch > 70 ? '#FFA500' : stoch < 20 ? '#00FF00' : stoch < 30 ? '#0000FF' : '#808080',
    tooltip: `StochRSI: ${stoch.toFixed(2)} - TradingView Accurate`
  };
}

function calculateMACD(closes, fast = 12, slow = 26, signal = 9) {
  // Use TradingView-accurate MACD calculation
  const result = TradingViewAccurateIndicators.calculateMACD(closes, fast, slow, signal);

  if (result.value === null) {
    return { value: null, error: result.error };
  }

  const macd = result.macd;
  const signalLine = result.signal;
  const histogram = result.histogram;

  // MACD interpretation matching TradingView standards:
  let macdSignal = 'neutral';
  if (macd > signalLine && histogram > 0.1) {
    macdSignal = 'strong bullish';
  } else if (macd > signalLine) {
    macdSignal = 'mild bullish';
  } else if (macd < signalLine && histogram < -0.1) {
    macdSignal = 'strong bearish';
  } else if (macd < signalLine) {
    macdSignal = 'mild bearish';
  }

  // Determine signal class based on MACD values
  let signalClass = 'neutral';
  if (macd > signalLine && histogram > 0.1) {
    signalClass = 'degen-buy';
  } else if (macd > signalLine) {
    signalClass = 'mild-buy';
  } else if (macd < signalLine && histogram < -0.1) {
    signalClass = 'degen-sell';
  } else if (macd < signalLine) {
    signalClass = 'mild-sell';
  }

  return {
    macd,
    signal: signalLine,
    histogram,
    macdSignal,
    overbought: macd < signalLine && histogram < -0.1,
    oversold: macd > signalLine && histogram > 0.1,
    signalClass,
    color: macd > signalLine && histogram > 0.1 ? '#00FF00' : macd > signalLine ? '#0000FF' : macd < signalLine && histogram < -0.1 ? '#FF0000' : macd < signalLine ? '#FFA500' : '#808080',
    tooltip: `MACD: ${macd.toFixed(5)} Signal: ${signalLine.toFixed(5)} - TradingView Accurate`
  };
}

function calculateBollingerBands(closes, period = 20, stdDev = 2) {
  // Use TradingView-accurate Bollinger Bands calculation
  const result = TradingViewAccurateIndicators.calculateBollingerBands(closes, period, stdDev);

  if (result.value === null) {
    return { value: null, error: result.error };
  }

  const upper = result.upper;
  const lower = result.lower;
  const sma = result.middle;
  const position = result.position;

  // Calculate bandwidth (volatility indicator)
  const bandwidth = ((upper - lower) / sma) * 100;

  // Bollinger Bands interpretation matching TradingView standards:
  let bbSignal = 'neutral';
  if (position > 90) {
    bbSignal = 'strong sell';
  } else if (position > 80) {
    bbSignal = 'mild sell';
  } else if (position < 10) {
    bbSignal = 'strong buy';
  } else if (position < 20) {
    bbSignal = 'mild buy';
  }

  // Determine signal class based on Bollinger Bands position using 5-color logic
  let signalClass = 'neutral';
  if (position > 90) {
    signalClass = 'degen-sell'; // Very overbought
  } else if (position > 70) {
    signalClass = 'mild-sell'; // Overbought
  } else if (position < 10) {
    signalClass = 'degen-buy'; // Very oversold
  } else if (position < 30) {
    signalClass = 'mild-buy'; // Oversold
  }

  return {
    upper,
    lower,
    sma,
    position,
    bandwidth,
    bbSignal,
    overbought: position > 90,
    oversold: position < 10,
    signalClass,
    color: position > 90 ? '#FF0000' : position > 80 ? '#FFA500' : position < 10 ? '#00FF00' : position < 20 ? '#0000FF' : '#808080',
    tooltip: `BB Position: ${position.toFixed(1)}% - TradingView Accurate`
  };
}

function calculateATR(highs, lows, closes, period = 14) {
  // Use TradingView-accurate ATR calculation with Wilder's smoothing
  const result = TradingViewAccurateIndicators.calculateATR(highs, lows, closes, period);

  if (result.value === null) {
    return {
      value: 'N/A',
      error: result.error,
      atrPercent: 'N/A',
      ratio: 'N/A',
      volatility: 'unknown',
      overbought: false,
      oversold: false,
      signalClass: 'neutral',
      color: '#808080'
    };
  }

  const atr = result.value;
  const currentPrice = closes[closes.length - 1];
  const atrPercent = (atr / currentPrice) * 100;

  // ATR interpretation based on percentage of price
  let volatility = 'normal';
  let ratio = 1; // Default ratio for compatibility

  if (atrPercent > 3) volatility = 'very high';
  else if (atrPercent > 2) volatility = 'high';
  else if (atrPercent > 1) volatility = 'normal';
  else if (atrPercent > 0.5) volatility = 'low';
  else volatility = 'very low';

  // Determine signal class based on ATR percentage using 5-color logic
  let signalClass = 'neutral';
  let color = '#808080';

  if (atrPercent > 4) {
    signalClass = 'degen-sell'; // Extreme volatility - danger
    color = '#FF0000';
  } else if (atrPercent > 2.5) {
    signalClass = 'mild-sell'; // High volatility - caution
    color = '#FFA500';
  } else if (atrPercent < 0.3) {
    signalClass = 'degen-buy'; // Very low volatility - opportunity
    color = '#00FF00';
  } else if (atrPercent < 0.8) {
    signalClass = 'mild-buy'; // Low volatility - favorable
    color = '#0000FF';
  }

  return {
    value: atr,
    atrPercent,
    ratio,
    volatility,
    overbought: atrPercent > 2,
    oversold: atrPercent < 1,
    signalClass,
    color,
    tooltip: `ATR: ${atr.toFixed(5)} (${atrPercent.toFixed(2)}%) - TradingView Accurate`
  };
}

function calculateMFI(highs, lows, closes, volumes, period = 14) {
  if (closes.length < period + 1) return {}
  let posMF = 0; let negMF = 0
  for (let i = 1; i <= period; i++) {
    const typical = (highs[i] + lows[i] + closes[i]) / 3
    const prevTypical = (highs[i - 1] + lows[i - 1] + closes[i - 1]) / 3
    const rawMF = typical * volumes[i]
    if (typical > prevTypical) posMF += rawMF
    else negMF += rawMF
  }
  const mfi = 100 - (100 / (1 + (posMF / (negMF || 1))))
  return {
    value: mfi,
    overbought: mfi > 80,
    oversold: mfi < 20,
    color: mfi > 80 ? '#FF0000' : mfi > 70 ? '#FFA500' : mfi < 20 ? '#00FF00' : mfi < 30 ? '#0000FF' : '#808080',
  }
}

function calculateWilliamsR(highs, lows, closes, period = 14) {
  // Use TradingView-accurate Williams %R calculation
  const result = TradingViewAccurateIndicators.calculateWilliamsR(highs, lows, closes, period);

  if (result.value === null) {
    return { value: 'N/A', error: result.error, overbought: false, oversold: false, color: '#808080' };
  }

  const wr = result.value;

  let color = '#808080'; // Default neutral color
  if (wr > -20) color = '#FF0000';
  else if (wr > -30) color = '#FFA500';
  else if (wr < -80) color = '#00FF00';
  else if (wr < -70) color = '#0000FF';

  return {
    value: wr,
    overbought: result.overbought,
    oversold: result.oversold,
    signalClass: result.signalClass,
    color,
    tooltip: `Williams %R: ${wr.toFixed(2)} - TradingView Accurate`
  };
}

function calculateADX(highs, lows, closes, period = 14) {
  if (highs.length < period + 1) return {}
  const dmPlus = highs.slice(1).map((h, i) => Math.max(h - highs[i], 0))
  const dmMinus = lows.slice(1).map((l, i) => Math.max(lows[i] - l, 0))
  const trs = highs.slice(1).map((h, i) => Math.max(h - lows[i + 1], Math.abs(h - closes[i]), Math.abs(lows[i + 1] - closes[i])))
  const smoothedPlus = calculateSMA(dmPlus, period)
  const smoothedMinus = calculateSMA(dmMinus, period)
  const smoothedTR = calculateSMA(trs, period)
  const diPlus = (smoothedPlus / smoothedTR) * 100
  const diMinus = (smoothedMinus / smoothedTR) * 100
  const dx = Math.abs(diPlus - diMinus) / (diPlus + diMinus || 1) * 100
  // For ADX, we need multiple DX values to calculate the average
  // For now, use the current DX as a simplified ADX
  const adx = dx

  // ADX trend strength interpretation:
  // Very Strong Trend: ADX > 50 (Red)
  // Strong Trend: ADX 35-50 (Orange)
  // Moderate Trend: ADX 25-35 (Grey)
  // Mild Trend: ADX 20-25 (Blue)
  // Weak Trend: ADX < 20 (Green)

  let trendStrength = 'moderate'
  if (adx > 50) trendStrength = 'very strong'
  else if (adx > 35) trendStrength = 'strong'
  else if (adx > 25) trendStrength = 'moderate'
  else if (adx > 20) trendStrength = 'mild'
  else trendStrength = 'weak'

  // Determine signal class based on ADX value
  // For ADX, we use a different approach since it measures trend strength, not direction
  // Very strong trends (high ADX) can indicate potential reversals (sell in strong uptrends, buy in strong downtrends)
  // Weak trends (low ADX) can indicate potential continuation (buy in weak uptrends, sell in weak downtrends)
  let signalClass = 'neutral'
  if (adx > 50) {
    signalClass = 'convergence' // Very strong trend - potential reversal point
  } else if (adx > 35) {
    signalClass = 'mild-sell' // Strong trend - caution
  } else if (adx < 20) {
    signalClass = 'degen-buy' // Weak trend - potential for new trend development
  } else if (adx < 25) {
    signalClass = 'mild-buy' // Mild trend - watching for trend development
  }

  return {
    value: adx,
    trendStrength,
    overbought: adx > 35, // Strong trend is considered overbought
    oversold: adx < 20, // Weak trend is considered oversold
    signalClass,
    color: adx > 50 ? '#FF0000' : adx > 35 ? '#FFA500' : adx < 20 ? '#00FF00' : adx < 25 ? '#0000FF' : '#808080',
  }
}

function calculateUltimateOscillator(highs, lows, closes, short = 7, mid = 14, long = 28) {
  if (closes.length < long + 1) return {}
  const bp = closes.slice(1).map((_, i) => closes[i + 1] - Math.min(lows[i + 1], closes[i]))
  const tr = closes.slice(1).map((_, i) => Math.max(highs[i + 1], closes[i]) - Math.min(lows[i + 1], closes[i]))
  const avg7 = bp.slice(-short).reduce((a, b) => a + b, 0) / tr.slice(-short).reduce((a, b) => a + b, 1)
  const avg14 = bp.slice(-mid).reduce((a, b) => a + b, 0) / tr.slice(-mid).reduce((a, b) => a + b, 1)
  const avg28 = bp.slice(-long).reduce((a, b) => a + b, 0) / tr.slice(-long).reduce((a, b) => a + b, 1)
  const uo = ((4 * avg7 + 2 * avg14 + avg28) / 7) * 100
  return {
    value: uo,
    overbought: uo > 70,
    oversold: uo < 30,
    color: uo > 70 ? '#FF0000' : uo > 60 ? '#FFA500' : uo < 30 ? '#00FF00' : uo < 40 ? '#0000FF' : '#808080',
  }
}

function calculateVWAP(highs, lows, closes, volumes) {
  if (!Array.isArray(volumes) || !volumes.length || !Array.isArray(highs) || !Array.isArray(lows) || !Array.isArray(closes)) return {}
  if (highs.length !== lows.length || lows.length !== closes.length || closes.length !== volumes.length) return {}

  const typical = highs.map((h, i) => (h + lows[i] + closes[i]) / 3)
  if (!Array.isArray(typical) || typical.length === 0) return {}

  const vwap = typical.reduce((sum, t, i) => sum + t * volumes[i], 0) / volumes.reduce((a, b) => a + b, 1)
  const position = closes[closes.length - 1] > vwap ? 'above' : 'below'
  return {
    value: vwap,
    position,
    overbought: position === 'above',
    oversold: position === 'below',
    color: position === 'below' ? '#00FF00' : '#FF0000',
  }
}

function calculateVolumeSpike(volumes, period = 14) {
  if (volumes.length < period) return {}
  const avg = calculateSMA(volumes, period)
  const current = volumes[volumes.length - 1]
  const ratio = current / avg
  const spikePercent = ratio * 100

  // Determine signal class based on volume spike percentage
  let signalClass = 'neutral'
  if (ratio > 2.0) {
    signalClass = 'degen-sell' // Very high volume often indicates selling climax
  } else if (ratio > 1.5) {
    signalClass = 'mild-sell'
  } else if (ratio < 0.3) {
    signalClass = 'degen-buy' // Very low volume can indicate accumulation
  } else if (ratio < 0.7) {
    signalClass = 'mild-buy'
  }

  return {
    spike: ratio > 1.5,
    spikePercent,
    avg,
    current,
    value: spikePercent, // Normalize to percentage for consistent display
    overbought: ratio > 1.5,
    oversold: ratio < 0.5,
    signalClass,
    color: ratio > 2.0 ? '#FF0000' : ratio > 1.5 ? '#FFA500' : ratio < 0.3 ? '#00FF00' : ratio < 0.7 ? '#0000FF' : '#808080',
  }
}

function calculateFractal(highs, lows, period = 5) {
  if (highs.length < period * 2 + 1) return {}
  const mid = Math.floor(period / 2)
  const lastHigh = highs[highs.length - mid - 1]
  const lastLow = lows[lows.length - mid - 1]

  // Check for fractal patterns
  const isHighFractal = highs.slice(-period * 2).every((h, i) => i === period - 1 || h <= lastHigh)
  const isLowFractal = lows.slice(-period * 2).every((l, i) => i === period - 1 || l >= lastLow)

  // Calculate strength of the fractal pattern
  const highStrength = isHighFractal ? Math.max(...highs.slice(-period * 2)) / lastHigh - 1 : 0
  const lowStrength = isLowFractal ? 1 - Math.min(...lows.slice(-period * 2)) / lastLow : 0

  // Determine the fractal type and strength
  let fractalType = 'none'
  let fractalValue = 0
  let fractalStrength = 0

  if (isHighFractal && isLowFractal) {
    // Both high and low fractals - determine which is stronger
    if (highStrength > lowStrength) {
      fractalType = 'bullish'
      fractalValue = 1
      fractalStrength = highStrength
    } else {
      fractalType = 'bearish'
      fractalValue = -1
      fractalStrength = lowStrength
    }
  } else if (isHighFractal) {
    fractalType = 'bullish'
    fractalValue = 1
    fractalStrength = highStrength
  } else if (isLowFractal) {
    fractalType = 'bearish'
    fractalValue = -1
    fractalStrength = lowStrength
  }

  // Determine color based on fractal type and strength
  let color = '#808080' // Default neutral color
  let overbought = false
  let oversold = false

  if (fractalType === 'bullish') {
    if (fractalStrength > 0.05) {
      color = '#00FF00' // Strong bullish - green
      oversold = true
    } else {
      color = '#0000FF' // Mild bullish - blue
    }
  } else if (fractalType === 'bearish') {
    if (fractalStrength > 0.05) {
      color = '#FF0000' // Strong bearish - red
      overbought = true
    } else {
      color = '#FFA500' // Mild bearish - orange
    }
  }

  return {
    breakout: fractalType,
    value: fractalValue * 100, // Normalize to percentage for consistent display
    strength: fractalStrength,
    overbought,
    oversold,
    color,
  }
}

// Import the sentiment service
const sentimentService = require('./services/sentiment-service');

// Global sentiment data cache
let sentimentCache = {
  fearGreedIndex: null,
  xaiSentiment: null,
  lastUpdated: null,
  updateInterval: 10 * 60 * 1000 // 10 minutes (aligned with sentiment service)
}

async function fetchSentimentData() {
  try {
    const now = Date.now();

    // Check if we need to update (every 10 minutes)
    if (sentimentCache.lastUpdated && (now - sentimentCache.lastUpdated) < sentimentCache.updateInterval) {
      return sentimentCache;
    }

    console.log('[SentimentAPI] Fetching fresh sentiment data...');

    // Fetch Fear & Greed Index from Alternative.me
    try {
      const fearGreedResponse = await fetch('https://api.alternative.me/fng/?limit=1');
      const fearGreedData = await fearGreedResponse.json();
      if (fearGreedData.data && fearGreedData.data.length > 0) {
        sentimentCache.fearGreedIndex = {
          value: parseInt(fearGreedData.data[0].value),
          classification: fearGreedData.data[0].value_classification,
          timestamp: fearGreedData.data[0].timestamp
        };
        console.log(`[SentimentAPI] Fear & Greed Index: ${sentimentCache.fearGreedIndex.value} (${sentimentCache.fearGreedIndex.classification})`);
      }
    } catch (error) {
      console.warn('[SentimentAPI] Failed to fetch Fear & Greed Index:', error.message);
    }

    // Get sentiment from xAI/Grok Mini service
    try {
      const sentimentAnalysis = await sentimentService.analyzeSentiment();
      sentimentCache.xaiSentiment = {
        analysis: sentimentAnalysis,
        timestamp: now
      };
      console.log('[SentimentAPI] xAI/Grok Mini sentiment analysis completed');
    } catch (error) {
      console.warn('[SentimentAPI] Failed to get xAI/Grok Mini sentiment:', error.message);
    }

    sentimentCache.lastUpdated = now;
    return sentimentCache;
  } catch (error) {
    console.error('[SentimentAPI] Error in fetchSentimentData:', error);
    return sentimentCache;
  }
}

function calculateSentiment() {
  // Ensure we have sentiment data
  if (!sentimentCache.fearGreedIndex && !sentimentCache.stockGeistData) {
    return {
      value: 0,
      signal: 'neutral',
      strength: 0,
      signalClass: 'neutral',
      color: '#808080',
      tooltip: 'No sentiment data available'
    }
  }

  // Initialize variables
  let sentimentValue = 0;
  let sentimentCount = 0;
  let tooltip = '';
  
  // Get references to the sentiment data sources
  const fearGreed = sentimentCache.fearGreedIndex;
  const stockGeist = sentimentCache.stockGeistData;
  
  // Include Fear & Greed Index (normalized to -1 to 1 range)
  if (fearGreed) {
    // Convert 0-100 scale to -1 to 1 scale
    const fearGreedNormalized = (fearGreed.value / 50) - 1;
    sentimentValue += fearGreedNormalized;
    sentimentCount++;
    tooltip += `Fear & Greed Index: ${fearGreed.value} (${fearGreed.classification}). `;
  }

  // Include StockGeist.ai data if available
  if (stockGeist) {
    sentimentValue += stockGeist.mean;
    sentimentCount++;
    tooltip += `StockGeist sentiment: ${stockGeist.mean.toFixed(3)} (${stockGeist.count} cryptocurrencies). `;
  }

  // Average the sentiment values
  const finalSentiment = sentimentCount > 0 ? sentimentValue / sentimentCount : 0;
  
  // Convert to 0-100 scale for UI display
  const displayValue = (finalSentiment + 1) * 50;
  
  // Determine signal, strength, and visual indicators
  let signal = 'neutral';
  let signalClass = 'neutral';
  let color = '#808080';
  let strength = 0;

  if (displayValue > 75) {
    signal = 'extreme_greed';
    signalClass = 'bearish'; // Extreme greed is often a bearish signal
    color = '#FF0000';
    strength = 2;
  } else if (displayValue > 60) {
    signal = 'greed';
    signalClass = 'bearish';
    color = '#FFA500';
    strength = 1;
  } else if (displayValue < 25) {
    signal = 'extreme_fear';
    signalClass = 'bullish'; // Extreme fear is often a bullish signal
    color = '#00FF00';
    strength = 2;
  } else if (displayValue < 40) {
    signal = 'fear';
    signalClass = 'bullish';
    color = '#0000FF';
    strength = 1;
  }

  return {
    value: displayValue,
    signal,
    strength,
    signalClass,
    color,
    tooltip
  }
}

function calculateEntropy(closes, period = 20) {
  if (closes.length < period) return {}
  const returns = closes.slice(1).map((c, i) => Math.log(c / closes[i]))
  const histogram = returns.reduce((acc, r) => {
    const bin = Math.floor(r * 100)
    acc[bin] = (acc[bin] || 0) + 1
    return acc
  }, {})
  const probs = Object.values(histogram).map(v => v / returns.length)
  const entropy = -probs.reduce((sum, p) => sum + p * Math.log2(p || 1), 0)
  const normalizedEntropy = entropy / Math.log2(period)

  return {
    value: normalizedEntropy * 100, // Normalize to percentage for consistent display
    overbought: normalizedEntropy > 0.8,
    oversold: normalizedEntropy < 0.4,
    color: normalizedEntropy > 0.8 ? '#FF0000' : normalizedEntropy > 0.6 ? '#FFA500' : normalizedEntropy < 0.2 ? '#00FF00' : normalizedEntropy < 0.4 ? '#0000FF' : '#808080',
  }
}

function calculateCorrelation(closes, refCloses = closes) {
  if (closes.length < 20 || refCloses.length < 20) return {}
  const x = closes.slice(-20)
  const y = refCloses.slice(-20)
  const mx = calculateSMA(x, x.length)
  const my = calculateSMA(y, y.length)
  const cov = x.reduce((sum, xi, i) => sum + (xi - mx) * (y[i] - my), 0) / x.length
  const sx = Math.sqrt(x.reduce((sum, xi) => sum + Math.pow(xi - mx, 2), 0) / x.length)
  const sy = Math.sqrt(y.reduce((sum, yi) => sum + Math.pow(yi - my, 2), 0) / y.length)
  const corr = cov / (sx * sy || 1)
  return {
    value: corr,
    color: corr > 0.7 ? '#00FF00' : corr > 0.5 ? '#0000FF' : corr < -0.7 ? '#FF0000' : corr < -0.5 ? '#FFA500' : '#808080',
  }
}

function calculateTimeAnomaly(closes, period = 20) {
  if (closes.length < period * 2) return {}
  const returns = closes.slice(1).map((c, i) => c / closes[i] - 1)
  const recentStd = Math.sqrt(returns.slice(-period).reduce((sum, r) => sum + r * r, 0) / period)
  const pastStd = Math.sqrt(returns.slice(-period * 2, -period).reduce((sum, r) => sum + r * r, 0) / period)
  const anomaly = recentStd / (pastStd || 1)

  return {
    value: anomaly * 100, // Normalize to percentage for consistent display
    overbought: anomaly > 1.5,
    oversold: anomaly < 0.5,
    color: anomaly > 1.5 ? '#FF0000' : anomaly > 1.2 ? '#FFA500' : anomaly < 0.5 ? '#00FF00' : anomaly < 0.8 ? '#0000FF' : '#808080',
  }
}

function calculateML(closes, period = 20) {
  // In a real system, this would use a trained machine learning model
  // For now, we'll simulate ML predictions based on recent price patterns
  if (closes.length < period) return {}

  // Get recent price action
  const recentPrices = closes.slice(-period)
  const priceChange = (recentPrices[recentPrices.length - 1] / recentPrices[0] - 1) * 100
  const rsi = calculateRSI(closes).value || 50
  const volatility = calculateATR([], [], closes).value || 1

  // Simulated ML model with bias toward recent price action and RSI
  const randomness = (Math.random() * 20) - 10 // ±10% random component
  let predictionValue = 50 + (priceChange * 2) + (rsi - 50) * 0.5 + randomness
  predictionValue = Math.max(0, Math.min(100, predictionValue)) // Clamp between 0-100

  // Determine signal based on prediction value
  let signal; let signalClass; let color
  if (predictionValue > 70) {
    signal = 'strong_buy'
    signalClass = 'bullish'
    color = '#00FF00' // Green
  } else if (predictionValue > 55) {
    signal = 'buy'
    signalClass = 'bullish'
    color = '#0000FF' // Blue
  } else if (predictionValue < 30) {
    signal = 'strong_sell'
    signalClass = 'bearish'
    color = '#FF0000' // Red
  } else if (predictionValue < 45) {
    signal = 'sell'
    signalClass = 'bearish'
    color = '#FFA500' // Orange
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080' // Gray
  }

  return {
    value: predictionValue,
    signal,
    signalClass,
    color,
    tooltip: `ML prediction: ${signal} (${predictionValue.toFixed(1)}%)`,
  }
}

// Commodity Channel Index (CCI)
function calculateCCI(highs, lows, closes, period = 20) {
  if (closes.length < period) return {}

  const typicalPrices = []
  for (let i = 0; i < closes.length; i++) {
    typicalPrices.push((highs[i] + lows[i] + closes[i]) / 3)
  }

  const sma = typicalPrices.slice(-period).reduce((sum, val) => sum + val, 0) / period
  const meanDeviation = typicalPrices.slice(-period).reduce((sum, val) => sum + Math.abs(val - sma), 0) / period
  const cci = (typicalPrices[typicalPrices.length - 1] - sma) / (0.015 * meanDeviation)

  let signal, signalClass, color
  if (cci > 200) {
    signal = 'extreme_overbought'
    signalClass = 'degen-sell'
    color = '#FF0000'
  } else if (cci > 100) {
    signal = 'overbought'
    signalClass = 'mild-sell'
    color = '#FFA500'
  } else if (cci < -200) {
    signal = 'extreme_oversold'
    signalClass = 'degen-buy'
    color = '#00FF00'
  } else if (cci < -100) {
    signal = 'oversold'
    signalClass = 'mild-buy'
    color = '#0000FF'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: cci,
    signal,
    signalClass,
    color,
    tooltip: `CCI: ${cci.toFixed(2)} (${signal})`
  }
}

// Parabolic SAR
function calculateParabolicSAR(highs, lows, closes, step = 0.02, maxStep = 0.2) {
  if (closes.length < 2) return {}

  let trend = 1 // 1 for uptrend, -1 for downtrend
  let sar = lows[0]
  let ep = highs[0] // Extreme point
  let af = step // Acceleration factor

  for (let i = 1; i < closes.length; i++) {
    const prevSar = sar

    if (trend === 1) {
      sar = prevSar + af * (ep - prevSar)
      if (highs[i] > ep) {
        ep = highs[i]
        af = Math.min(af + step, maxStep)
      }
      if (lows[i] < sar) {
        trend = -1
        sar = ep
        ep = lows[i]
        af = step
      }
    } else {
      sar = prevSar + af * (ep - prevSar)
      if (lows[i] < ep) {
        ep = lows[i]
        af = Math.min(af + step, maxStep)
      }
      if (highs[i] > sar) {
        trend = 1
        sar = ep
        ep = highs[i]
        af = step
      }
    }
  }

  const currentPrice = closes[closes.length - 1]
  let signal, signalClass, color

  if (trend === 1 && currentPrice > sar) {
    signal = 'bullish'
    signalClass = 'bullish'
    color = '#00FF00'
  } else if (trend === -1 && currentPrice < sar) {
    signal = 'bearish'
    signalClass = 'bearish'
    color = '#FF0000'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: sar,
    signal,
    signalClass,
    color,
    tooltip: `Parabolic SAR: ${sar.toFixed(2)} (${signal})`
  }
}

// Ichimoku Cloud
function calculateIchimoku(highs, lows, closes) {
  if (closes.length < 52) return {}

  // Tenkan-sen (Conversion Line): (9-period high + 9-period low) / 2
  const tenkanHigh = Math.max(...highs.slice(-9))
  const tenkanLow = Math.min(...lows.slice(-9))
  const tenkan = (tenkanHigh + tenkanLow) / 2

  // Kijun-sen (Base Line): (26-period high + 26-period low) / 2
  const kijunHigh = Math.max(...highs.slice(-26))
  const kijunLow = Math.min(...lows.slice(-26))
  const kijun = (kijunHigh + kijunLow) / 2

  // Senkou Span A: (Tenkan + Kijun) / 2, plotted 26 periods ahead
  const senkouA = (tenkan + kijun) / 2

  // Senkou Span B: (52-period high + 52-period low) / 2, plotted 26 periods ahead
  const senkouBHigh = Math.max(...highs.slice(-52))
  const senkouBLow = Math.min(...lows.slice(-52))
  const senkouB = (senkouBHigh + senkouBLow) / 2

  const currentPrice = closes[closes.length - 1]
  let signal, signalClass, color

  if (currentPrice > Math.max(senkouA, senkouB) && tenkan > kijun) {
    signal = 'strong_bullish'
    signalClass = 'bullish'
    color = '#00FF00'
  } else if (currentPrice < Math.min(senkouA, senkouB) && tenkan < kijun) {
    signal = 'strong_bearish'
    signalClass = 'bearish'
    color = '#FF0000'
  } else if (tenkan > kijun) {
    signal = 'bullish'
    signalClass = 'bullish'
    color = '#0000FF'
  } else if (tenkan < kijun) {
    signal = 'bearish'
    signalClass = 'bearish'
    color = '#FFA500'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: { tenkan, kijun, senkouA, senkouB },
    signal,
    signalClass,
    color,
    tooltip: `Ichimoku: ${signal} (Tenkan: ${tenkan.toFixed(2)}, Kijun: ${kijun.toFixed(2)})`
  }
}

// On-Balance Volume (OBV)
function calculateOBV(closes, volumes) {
  if (closes.length < 2 || volumes.length < 2) return {}

  let obv = 0
  for (let i = 1; i < closes.length; i++) {
    if (closes[i] > closes[i - 1]) {
      obv += volumes[i]
    } else if (closes[i] < closes[i - 1]) {
      obv -= volumes[i]
    }
    // If price unchanged, OBV unchanged
  }

  // Calculate OBV trend over last 10 periods
  let obvTrend = 0
  if (closes.length >= 10) {
    const recentOBV = []
    let tempOBV = 0
    for (let i = Math.max(1, closes.length - 10); i < closes.length; i++) {
      if (closes[i] > closes[i - 1]) {
        tempOBV += volumes[i]
      } else if (closes[i] < closes[i - 1]) {
        tempOBV -= volumes[i]
      }
      recentOBV.push(tempOBV)
    }
    obvTrend = recentOBV[recentOBV.length - 1] - recentOBV[0]
  }

  let signal, signalClass, color
  if (obvTrend > 0) {
    signal = 'bullish'
    signalClass = 'bullish'
    color = '#00FF00'
  } else if (obvTrend < 0) {
    signal = 'bearish'
    signalClass = 'bearish'
    color = '#FF0000'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: obv,
    signal,
    signalClass,
    color,
    tooltip: `OBV: ${obv.toFixed(0)} (${signal})`
  }
}

// Aroon Indicator
function calculateAroon(highs, lows, period = 14) {
  if (highs.length < period) return {}

  const recentHighs = highs.slice(-period)
  const recentLows = lows.slice(-period)

  // Find periods since highest high and lowest low
  const highestIndex = recentHighs.indexOf(Math.max(...recentHighs))
  const lowestIndex = recentLows.indexOf(Math.min(...recentLows))

  const aroonUp = ((period - 1 - highestIndex) / (period - 1)) * 100
  const aroonDown = ((period - 1 - lowestIndex) / (period - 1)) * 100
  const aroonOscillator = aroonUp - aroonDown

  let signal, signalClass, color
  if (aroonUp > 70 && aroonDown < 30) {
    signal = 'strong_bullish'
    signalClass = 'bullish'
    color = '#00FF00'
  } else if (aroonDown > 70 && aroonUp < 30) {
    signal = 'strong_bearish'
    signalClass = 'bearish'
    color = '#FF0000'
  } else if (aroonOscillator > 0) {
    signal = 'bullish'
    signalClass = 'bullish'
    color = '#0000FF'
  } else if (aroonOscillator < 0) {
    signal = 'bearish'
    signalClass = 'bearish'
    color = '#FFA500'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: { aroonUp, aroonDown, oscillator: aroonOscillator },
    signal,
    signalClass,
    color,
    tooltip: `Aroon: Up ${aroonUp.toFixed(1)}%, Down ${aroonDown.toFixed(1)}% (${signal})`
  }
}

// TRIX Indicator
function calculateTRIX(closes, period = 14) {
  if (closes.length < period * 3) return {}

  // TRIX requires calculating EMA arrays, not single values
  // For now, return a neutral signal since proper TRIX calculation requires array-based EMA
  const currentPrice = closes[closes.length - 1]
  const previousPrice = closes[closes.length - 2] || currentPrice
  const trix = ((currentPrice - previousPrice) / previousPrice) * 10000

  let signal, signalClass, color
  if (trix > 0.1) {
    signal = 'bullish'
    signalClass = 'bullish'
    color = '#00FF00'
  } else if (trix < -0.1) {
    signal = 'bearish'
    signalClass = 'bearish'
    color = '#FF0000'
  } else if (trix > 0) {
    signal = 'weak_bullish'
    signalClass = 'bullish'
    color = '#0000FF'
  } else if (trix < 0) {
    signal = 'weak_bearish'
    signalClass = 'bearish'
    color = '#FFA500'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: trix,
    signal,
    signalClass,
    color,
    tooltip: `TRIX: ${trix.toFixed(4)} (${signal})`
  }
}

// Chaikin Money Flow
function calculateChaikinMoneyFlow(highs, lows, closes, volumes, period = 20) {
  if (closes.length < period) return {}

  const moneyFlowMultipliers = []
  const moneyFlowVolumes = []

  for (let i = 0; i < closes.length; i++) {
    const hlRange = highs[i] - lows[i]
    const multiplier = hlRange === 0 ? 0 : ((closes[i] - lows[i]) - (highs[i] - closes[i])) / hlRange
    moneyFlowMultipliers.push(multiplier)
    moneyFlowVolumes.push(multiplier * volumes[i])
  }

  const recentMFV = moneyFlowVolumes.slice(-period)
  const recentVolumes = volumes.slice(-period)

  const sumMFV = recentMFV.reduce((sum, val) => sum + val, 0)
  const sumVolume = recentVolumes.reduce((sum, val) => sum + val, 0)

  const cmf = sumVolume === 0 ? 0 : sumMFV / sumVolume

  let signal, signalClass, color
  if (cmf > 0.2) {
    signal = 'strong_bullish'
    signalClass = 'bullish'
    color = '#00FF00'
  } else if (cmf < -0.2) {
    signal = 'strong_bearish'
    signalClass = 'bearish'
    color = '#FF0000'
  } else if (cmf > 0) {
    signal = 'bullish'
    signalClass = 'bullish'
    color = '#0000FF'
  } else if (cmf < 0) {
    signal = 'bearish'
    signalClass = 'bearish'
    color = '#FFA500'
  } else {
    signal = 'neutral'
    signalClass = 'neutral'
    color = '#808080'
  }

  return {
    value: cmf,
    signal,
    signalClass,
    color,
    tooltip: `Chaikin Money Flow: ${cmf.toFixed(3)} (${signal})`
  }
}

function calculateEMA(data, period) {
  try {
    // Check if data is valid
    if (!Array.isArray(data) || data.length === 0) {
      console.warn(`calculateEMA: Invalid data - not an array or empty`)
      return 0
    }
    
    // Ensure we have enough data points
    if (data.length < period) {
      console.warn(`calculateEMA: Not enough data points (${data.length} < ${period})`)
      return data.length > 0 ? data[data.length - 1] : 0 // Return last data point or 0 if no data
    }
    
    // Convert all values to numbers and filter out any invalid values
    const cleanData = data
      .map(Number)
      .filter(val => !isNaN(val) && isFinite(val))
    
    if (cleanData.length === 0) {
      console.warn('calculateEMA: No valid numeric data points found')
      return 0
    }
    
    const k = 2 / (period + 1)
    let ema = cleanData[0] // Start with the first value
    
    // Calculate EMA
    for (let i = 1; i < cleanData.length; i++) {
      ema = cleanData[i] * k + ema * (1 - k)
    }
    
    return ema
  } catch (error) {
    console.error('Error in calculateEMA:', error)
    return 0
  }
}

function calculateSMA(data, period) {
  try {
    // Check if data is valid
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('calculateSMA: Invalid data - not an array or empty')
      return 0
    }
    
    // If we don't have enough data, return the average of what we have
    if (data.length < period) {
      console.warn(`calculateSMA: Not enough data points (${data.length} < ${period})`)
      const validData = data.filter(val => val !== null && val !== undefined && !isNaN(val))
      if (validData.length === 0) return 0
      return validData.reduce((sum, val) => sum + val, 0) / validData.length
    }
    
    // Get the most recent 'period' data points
    const recentData = data.slice(-period)
    
    // Filter out any invalid values
    const validData = recentData.filter(val => val !== null && val !== undefined && !isNaN(val))
    
    if (validData.length === 0) {
      console.warn('calculateSMA: No valid numeric data points found')
      return 0
    }
    
    // Calculate the simple moving average
    const sum = validData.reduce((a, b) => a + b, 0)
    return sum / validData.length
  } catch (error) {
    console.error('Error in calculateSMA:', error)
    return 0
  }
}

// Track last fetch time for each pair/timeframe to avoid duplicate API calls
const lastFetchTime = {}
const FETCH_COOLDOWN = 30000 // 30 seconds cooldown between fetches for the same pair/timeframe

// Track in-progress fetches to prevent duplicate requests
const activeFetches = {}

// Fetch Kraken OHLC data
async function fetchOHLC(pair, timeframe, forceUpdate = false) {
  try {
    // Create a unique key for this pair/timeframe combination
    const fetchKey = `${pair}-${timeframe}`

    // Check if there's already an active fetch for this pair/timeframe
    if (activeFetches[fetchKey]) {
      // Wait for the existing fetch to complete instead of starting a new one
      console.log(`Waiting for in-progress fetch of ${pair}/${timeframe}...`)
      return await activeFetches[fetchKey]
    }

    // Check if we already have data and if it's recent enough
    const now = Date.now()
    const lastFetch = lastFetchTime[fetchKey] || 0

    // If we have data and it's recent enough, use the cached data unless force update is requested
    if (!forceUpdate &&
        historicalData[pair]?.[timeframe]?.length > 0 &&
        now - lastFetch < FETCH_COOLDOWN) {
      console.log(`Using cached OHLC data for ${pair}/${timeframe} (${historicalData[pair][timeframe].length} candles)`)
      return historicalData[pair][timeframe]
    }

    // Try to load from historical data file first (if no cached data exists)
    if (!historicalData[pair]?.[timeframe]?.length) {
      const fileData = loadHistoricalDataFromFile(pair, timeframe)
      if (fileData && fileData.length > 0) {
        console.log(`Using historical data from file for ${pair}/${timeframe} (${fileData.length} candles)`)
        lastFetchTime[fetchKey] = now // Mark as fetched to prevent immediate API call
        return fileData
      }
    }

    // Create a promise for this fetch and store it in activeFetches
    const fetchPromise = (async () => {
      try {
        // Update the last fetch time to prevent duplicate fetches
        lastFetchTime[fetchKey] = now

        const krakenPair = KRAKEN_PAIRS[pair]
        if (!krakenPair) {
          console.warn(`No Kraken pair mapping for ${pair}`)
          return []
        }

        const interval = INTERVALS[timeframe]
        console.log(`Fetching OHLC data for ${pair}/${timeframe} from Kraken API: ${krakenPair}, interval: ${interval}`)
        const response = await fetch(`https://api.kraken.com/0/public/OHLC?pair=${krakenPair}&interval=${interval}`)
        const data = await response.json()

        if (data.error && data.error.length > 0) {
          console.warn(`Kraken API error for ${pair}/${timeframe}: ${data.error.join(', ')}`)
          return []
        }

        // Check if we have valid data
        const resultKey = Object.keys(data.result).find(key => key !== 'last')
        if (!resultKey || !data.result[resultKey] || !Array.isArray(data.result[resultKey])) {
          console.warn(`Invalid data format from Kraken for ${pair}/${timeframe}`)
          return []
        }

        const candles = data.result[resultKey].slice(-MAX_CANDLES).map(c => ({
          t: parseInt(c[0]) * 1000,
          o: parseFloat(c[1]),
          h: parseFloat(c[2]),
          l: parseFloat(c[3]),
          c: parseFloat(c[4]),
          v: parseFloat(c[6]),
          x: parseInt(c[0]) * 1000,
        }))

        historicalData[pair] = historicalData[pair] || {}
        historicalData[pair][timeframe] = candles

        console.log(`Successfully fetched OHLC for ${pair}/${timeframe}: ${candles.length} candles`)
        return candles
      } catch (apiError) {
        console.error(`Error fetching from Kraken API for ${pair}/${timeframe}: ${apiError.message}`)
        return []
      } finally {
        // Remove this fetch from active fetches when done
        delete activeFetches[fetchKey]
      }
    })()

    // Store the promise in activeFetches
    activeFetches[fetchKey] = fetchPromise

    // Return the promise result
    return await fetchPromise
  } catch (e) {
    console.error(`Error in fetchOHLC for ${pair}/${timeframe}: ${e.message}`)
    return []
  }
}

// Fetch live price
async function fetchLivePrice(pair) {
  try {
    const krakenPair = KRAKEN_PAIRS[pair]
    if (!krakenPair) {
      console.warn(`No Kraken pair mapping for ${pair}`)
      return null
    }
    const response = await fetch(`https://api.kraken.com/0/public/Ticker?pair=${krakenPair}`)
    const data = await response.json()

    // Check for errors in the response
    if (data.error && data.error.length > 0) {
      console.warn(`Kraken API error for ${pair}: ${data.error.join(', ')}`)
      return null
    }

    // Check if we have valid data
    if (!data.result || !data.result[Object.keys(data.result)[0]]) {
      console.warn(`Invalid data format from Kraken for ${pair}`)
      return null
    }

    // Get the ticker data using the first key in the result object
    // This is more robust than assuming the key is exactly krakenPair
    const resultKey = Object.keys(data.result)[0]
    const ticker = data.result[resultKey]

    const priceData = {
      price: parseFloat(ticker.c[0]),
      bid: parseFloat(ticker.b[0]),
      ask: parseFloat(ticker.a[0]),
      timestamp: Date.now(),
    }
    console.log(`Successfully fetched price for ${pair}: $${priceData.price}`)
    return priceData
  } catch (e) {
    console.error(`Error fetching price for ${pair}: ${e.message}`)
    return null
  }
}

// NO DUMMY DATA FUNCTIONS - Trading integrity requires real data only

// Adaptive timeframe calculation for missing timeframes
function calculateAdaptiveTimeframe(pair, targetTimeframe) {
  console.log(`🔄 ADAPTIVE: Calculating ${targetTimeframe} from existing data for ${pair}`);

  // Define source timeframes and multipliers for adaptive calculation
  const adaptiveMap = {
    '30m': { source: '15m', multiplier: 2 },
    '3d': { source: '1d', multiplier: 3 },
    '4w': { source: '1w', multiplier: 4 }
  };

  const config = adaptiveMap[targetTimeframe];
  if (!config) {
    console.log(`⚠️ ADAPTIVE: No adaptive config for ${targetTimeframe}`);
    return [];
  }

  const sourceCandles = historicalData[pair]?.[config.source] || [];
  if (sourceCandles.length === 0) {
    console.log(`⚠️ ADAPTIVE: No source data for ${config.source}`);
    return [];
  }

  console.log(`🔄 ADAPTIVE: Converting ${sourceCandles.length} ${config.source} candles to ${targetTimeframe}`);

  // Group source candles by the multiplier
  const adaptiveCandles = [];
  for (let i = 0; i < sourceCandles.length; i += config.multiplier) {
    const group = sourceCandles.slice(i, i + config.multiplier);
    if (group.length === config.multiplier) {
      // Combine candles: open from first, close from last, high/low from all, volume summed
      const combined = {
        timestamp: group[0].timestamp,
        open: group[0].open,
        close: group[group.length - 1].close,
        high: Math.max(...group.map(c => c.high)),
        low: Math.min(...group.map(c => c.low)),
        volume: group.reduce((sum, c) => sum + (c.volume || 0), 0)
      };
      adaptiveCandles.push(combined);
    }
  }

  console.log(`✅ ADAPTIVE: Generated ${adaptiveCandles.length} ${targetTimeframe} candles`);

  // Store the adaptive data
  if (!historicalData[pair]) historicalData[pair] = {};
  historicalData[pair][targetTimeframe] = adaptiveCandles;

  return adaptiveCandles;
}

// Calculate indicators and signals
function calculateAllIndicators(pair, timeframe) {
  console.log(`🔍 CALCULATE DEBUG: calculateAllIndicators called for ${pair}/${timeframe}`);

  // Check if we need adaptive calculation for missing timeframes
  const adaptiveTimeframes = ['30m', '3d', '4w'];
  if (adaptiveTimeframes.includes(timeframe) && !historicalData[pair]?.[timeframe]) {
    calculateAdaptiveTimeframe(pair, timeframe);
  }

  const candles = historicalData[pair]?.[timeframe] || []
  console.log(`🔍 CALCULATE DEBUG: Found ${candles.length} candles in historicalData[${pair}][${timeframe}]`);
  console.log(`🔍 CALCULATE DEBUG: historicalData keys:`, Object.keys(historicalData));
  if (historicalData[pair]) {
    console.log(`🔍 CALCULATE DEBUG: ${pair} timeframes:`, Object.keys(historicalData[pair]));
  }

  // Require minimum data for meaningful calculations - NO FALLBACK DATA FOR TRADING INTEGRITY
  const minCandles = 50; // Increased from 20 for better indicator accuracy
  if (candles.length < minCandles) {
    console.error(`❌ INSUFFICIENT DATA for ${pair}/${timeframe}: ${candles.length} < ${minCandles} - NO INDICATORS CALCULATED`)
    // Return empty object - NO DUMMY DATA for trading tools
    return {}
  }

  const highs = candles.map(c => c.h).filter(h => h && !isNaN(h))
  const lows = candles.map(c => c.l).filter(l => l && !isNaN(l))
  const closes = candles.map(c => c.c).filter(c => c && !isNaN(c))
  const volumes = candles.map(c => c.v).filter(v => v && !isNaN(v))

  // Verify we still have enough data after filtering - NO FALLBACK DATA
  if (closes.length < minCandles) {
    console.error(`❌ INSUFFICIENT VALID DATA for ${pair}/${timeframe} after filtering: ${closes.length} - NO INDICATORS CALCULATED`)
    return {}
  }

  // Calculate all indicators and add timeframe information to each
  const indicators = {}

  // Helper function to add timeframe to indicator data
  const addTimeframeToIndicator = (_, indData) => {
    if (!indData || typeof indData !== 'object') return indData
    return { ...indData, timeframe }
  }

  // List of all expected indicator keys
  const allIndicatorKeys = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'mfi', 'williamsR', 'adx', 'ultimateOscillator',
    'vwap', 'volume', 'fractal', 'sentiment', 'entropy', 'correlation', 'time_anomaly', 'ml',
    'cci', 'parabolicSar', 'ichimoku', 'obv', 'aroon', 'trix', 'chaikinMoneyFlow'
  ]

  // Calculate each indicator and add timeframe information, with robust logging and default fallback
  console.log('🔍 MAJOR DEBUG: Starting indicator calculations for', allIndicatorKeys.length, 'indicators');
  console.log('🔍 MAJOR DEBUG: Data available - closes:', closes.length, 'highs:', highs.length, 'lows:', lows.length);

  allIndicatorKeys.forEach(key => {
    console.log('🔍 MAJOR DEBUG: Processing indicator:', key);
    let value
    switch (key) {
      case 'rsi':
        console.log('🔍 INDICATOR DEBUG: RSI calculation - closes length:', closes.length, 'first 5:', closes.slice(0, 5));
        value = calculateRSI(closes);
        console.log('🔍 INDICATOR DEBUG: RSI result:', value);
        break
      case 'ema':
        console.log('🔍 INDICATOR DEBUG: EMA calculation - closes length:', closes.length);
        const emaValue = calculateEMA(closes, 20);
        value = {
          value: emaValue,
          signal: emaValue > closes[closes.length - 1] ? 'sell' : 'buy',
          signalClass: emaValue > closes[closes.length - 1] ? 'mild-sell' : 'mild-buy',
          color: emaValue > closes[closes.length - 1] ? '#FFA500' : '#0000FF',
          tooltip: `EMA(20): ${emaValue.toFixed(2)}`
        };
        console.log('🔍 INDICATOR DEBUG: EMA result:', value);
        break
      case 'sma':
        console.log('🔍 INDICATOR DEBUG: SMA calculation - closes length:', closes.length);
        const smaValue = calculateSMA(closes, 20);
        value = {
          value: smaValue,
          signal: smaValue > closes[closes.length - 1] ? 'sell' : 'buy',
          signalClass: smaValue > closes[closes.length - 1] ? 'mild-sell' : 'mild-buy',
          color: smaValue > closes[closes.length - 1] ? '#FFA500' : '#0000FF',
          tooltip: `SMA(20): ${smaValue.toFixed(2)}`
        };
        console.log('🔍 INDICATOR DEBUG: SMA result:', value);
        break
      case 'stochRsi': value = calculateStochRSI(closes); break
      case 'macd':
        console.log('🔍 INDICATOR DEBUG: MACD calculation - closes length:', closes.length);
        value = TradingViewAccurateIndicators.calculateMACD(closes);
        console.log('🔍 INDICATOR DEBUG: MACD result:', value);
        break
      case 'bollingerBands':
        console.log('🔍 INDICATOR DEBUG: Bollinger Bands calculation - closes length:', closes.length);
        value = TradingViewAccurateIndicators.calculateBollingerBands(closes);
        console.log('🔍 INDICATOR DEBUG: Bollinger Bands result:', value);
        break
      case 'atr':
        console.log('🔍 INDICATOR DEBUG: ATR calculation - data lengths:', highs.length, lows.length, closes.length);
        value = TradingViewAccurateIndicators.calculateATR(highs, lows, closes);
        console.log('🔍 INDICATOR DEBUG: ATR result:', value);
        break
      case 'mfi': value = calculateMFI(highs, lows, closes, volumes); break
      case 'williamsR': value = calculateWilliamsR(highs, lows, closes); break
      case 'adx': value = calculateADX(highs, lows, closes); break
      case 'ultimateOscillator': value = calculateUltimateOscillator(highs, lows, closes); break
      case 'vwap': value = calculateVWAP(highs, lows, closes, volumes); break
      case 'volume': value = calculateVolumeSpike(volumes); break
      case 'fractal': value = calculateFractal(highs, lows); break
      case 'sentiment': value = calculateSentiment(); break
      case 'entropy': value = calculateEntropy(closes); break
      case 'correlation': value = calculateCorrelation(closes); break
      case 'time_anomaly': value = calculateTimeAnomaly(closes); break
      case 'ml': value = calculateML(closes); break
      case 'cci': value = calculateCCI(highs, lows, closes); break
      case 'parabolicSar': value = calculateParabolicSAR(highs, lows, closes); break
      case 'ichimoku': value = calculateIchimoku(highs, lows, closes); break
      case 'obv': value = calculateOBV(closes, volumes); break
      case 'aroon': value = calculateAroon(highs, lows); break
      case 'trix': value = calculateTRIX(closes); break
      case 'chaikinMoneyFlow': value = calculateChaikinMoneyFlow(highs, lows, closes, volumes); break
      default: value = undefined
    }
    if (value === undefined || value === null || (typeof value === 'number' && isNaN(value))) {
      console.warn(`Indicator ${key} for ${pair}/${timeframe} is missing or invalid, using default.`)
      indicators[key] = { value: 'N/A', color: '#808080', timeframe }
    } else {
      indicators[key] = addTimeframeToIndicator(key, value)
    }
  })

  // Add a metadata field to help with debugging
  indicators.metadata = {
    timeframe,
    calculatedAt: new Date().toISOString(),
    candleCount: candles.length,
  }

  // Ensure every expected indicator is present in the result
  allIndicatorKeys.forEach(key => {
    if (!indicators[key]) {
      indicators[key] = { value: 'N/A', color: '#808080', timeframe }
      console.warn(`Indicator ${key} missing for ${pair}/${timeframe}, setting to default.`)
    }
  })

  // Log summary of missing/invalid indicators
  const missing = allIndicatorKeys.filter(k => indicators[k] && indicators[k].value === 'N/A')
  if (missing.length > 0) {
    console.warn(`Missing/invalid indicators for ${pair}/${timeframe}: ${missing.join(', ')}`)
  }

  // Find the strategy for this pair from subscriptions
  let strategy = 'admiral_toa' // Default strategy

  // Look for a subscription that matches this pair
  for (const [_, sub] of subscriptions.entries()) {
    if (sub.pair === pair && sub.strategy) {
      strategy = sub.strategy
      break
    }
  }

  // Get the strategy definition
  const strat = TRADING_STRATEGIES[strategy]
  if (!strat) {
    console.warn(`Strategy ${strategy} not found, using admiral_toa`)
    strategy = 'admiral_toa'
  }

  // Collect the indicators needed for this strategy
  const stratIndicators = {}
  strat.indicators.forEach(ind => {
    if (indicators[ind]) {
      stratIndicators[ind] = indicators[ind]
    } else {
      console.warn(`Indicator ${ind} required by strategy ${strategy} not available`)
    }
  })

  // Apply the strategy logic
  const { signal, color, signalClass, convergenceSteps, metrics } = strat.logic(stratIndicators)

  // Log the strategy results for debugging
  console.log(`Strategy ${strategy} for ${pair}/${timeframe} produced signal: ${signal}, color: ${color}`)
  if (convergenceSteps) {
    console.log(`Convergence steps: Buy=${convergenceSteps.buy.length}, Sell=${convergenceSteps.sell.length}`)
  }

  indicators.convergenceSteps = convergenceSteps

  // Add strategy information to metadata instead of as a separate indicator
  indicators.metadata.strategy = {
    name: strat.name,
    signal,
    color,
    signalClass,
    metrics,
    helperText: strat.helperText,
  }

  indicatorsData[pair] = indicatorsData[pair] || {}
  indicatorsData[pair][timeframe] = indicators

  return indicators
}

// WebSocket handling
// Ensure we use consistent pair mappings throughout the code
// Note: We're removing PAIR_MAPPINGS and using KRAKEN_PAIRS consistently

// 🎖️ FLEET COMMANDER: DISABLED - Third WebSocket handler conflicts with enhanced frontend handler
// All frontend connections now go through the enhanced handler on port 8080
// In the WebSocket connection handler
// wss.on('connection', (ws, req) => {
  // Initialize connection properties
  // ws.isAlive = true

  // Get client IP for logging
  // const ip = req.socket.remoteAddress
  // console.log(`WebSocket client connected from ${ip}`)

  // Parse query parameters
  // const query = req ? url.parse(req.url, true).query : {}
  // const initialPair = query.pair || DEFAULT_PAIR
  // console.log(`Initial pair from URL query: ${initialPair}`)

  // Function to calculate indicators for all timeframes
  // function calculateIndicatorsAllTimeframes(pair) {
  //   const result = {}
  //   TIMEFRAMES.forEach(tf => {
  //     result[tf] = calculateAllIndicators(pair, tf)
  //   })
  //   return result
  // }

  // 🎖️ FLEET COMMANDER: DISABLED - Old message handler conflicts with enhanced frontend handler
  // All messages now go through the enhanced handler at line 85
  /*
  ws.on('message', async (message) => {
    try {
      const msg = JSON.parse(message)
      console.log('🚫 OLD HANDLER: Message intercepted:', msg.type)

      if (msg.type === 'subscribe') {
        const pair = (msg.pair || initialPair).toLowerCase()
        const strategy = msg.strategy || DEFAULT_STRATEGY
        const timeframe = msg.timeframe || DEFAULT_TIMEFRAME

        if (!SUPPORTED_PAIRS.includes(pair)) {
          ws.send(JSON.stringify({ type: 'error', message: `Unsupported pair: ${pair}. Supported: ${SUPPORTED_PAIRS.join(', ')}` }))
          return
        }

        // Store subscription info
        subscriptions.set(ws, { pair, strategy, timeframe })

        // Only fetch data for the subscribed pair
        console.log(`Client subscribed to ${pair}`)
        const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
        for (const tf of timeframes) {
          try {
            const updateKey = `${pair}-${tf}`
            const now = Date.now()

            // Check if we already have data for this pair/timeframe
            const hasData = historicalData[pair]?.[tf]?.length > 0
            const lastFetch = lastFetchTime[updateKey] || 0
            const dataAge = now - lastFetch

            // Only fetch if we don't have data or it's older than 30 seconds
            if (!hasData || dataAge > 30000) {
              await fetchOHLC(pair, tf)
              // Update the last periodic update time to prevent immediate re-fetch
              lastPeriodicUpdate[updateKey] = now
            } else {
              console.log(`Using existing data for ${pair}/${tf} (${historicalData[pair][tf].length} candles, ${Math.floor(dataAge / 1000)}s old)`)
            }

            // Send historical data to the client
            ws.send(JSON.stringify({ type: 'historicalData', pair, timeframe: tf, data: historicalData[pair]?.[tf] || [] }))
          } catch (e) {
            console.error(`Error fetching OHLC for ${pair}/${tf}: ${e.message}`)
            continue // Skip failed timeframes
          }
        }

        // Calculate and send indicators for all timeframes
        const allIndicators = calculateIndicatorsAllTimeframes(pair)
        TIMEFRAMES.forEach(tf => {
          // Double-check that the timeframe is correctly set in the data
          const indicators = allIndicators[tf] || {}

          if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
            console.error(`Timeframe mismatch in subscription: expected ${tf} but got ${indicators.metadata?.timeframe}`)
            // Fix the metadata
            if (!indicators.metadata) indicators.metadata = {}
            indicators.metadata.timeframe = tf
          }

          // Verify all indicators have the correct timeframe
          Object.keys(indicators).forEach(ind => {
            if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
              if (indicators[ind].timeframe !== tf) {
                console.error(`Indicator ${ind} has incorrect timeframe in subscription: ${indicators[ind].timeframe} (expected ${tf})`)
                indicators[ind].timeframe = tf // Fix the timeframe
              }
            }
          })

          ws.send(JSON.stringify({
            type: 'indicators',
            pair,
            timeframe: tf,
            data: indicators,
          }))
        })

        // Fetch live price
        try {
          const priceData = await fetchLivePrice(pair)
          console.log(`Successfully fetched price for ${pair}: $${priceData?.price}`)
          lastLivePrice[pair] = priceData
          ws.send(JSON.stringify({ type: 'livePrice', pair, data: priceData }))
        } catch (e) {
          console.error(`Error fetching price for ${pair}: ${e.message}`)
        }
      } else if (msg.type === 'requestIndicators') {
        // 🎖️ FLEET COMMANDER: DISABLED - This handler conflicts with enhanced frontend handler
        // All requestIndicators messages now go through the enhanced handler
        console.log(`⚠️ OLD HANDLER: Ignoring requestIndicators for ${msg.pair}/${msg.timeframe} - using enhanced handler`);
        return;

          // Log the timeframe to help with debugging
          console.log(`Processing indicators request for timeframe: ${tf}`)

          // Double-check that we're using the correct timeframe throughout the process
          if (!TIMEFRAMES.includes(tf)) {
            console.error(`Invalid timeframe: ${tf}. This should have been caught earlier.`)
            ws.send(JSON.stringify({
              type: 'error',
              message: `Invalid timeframe: ${tf}`,
              requestType: 'requestIndicators',
            }))
            return
          }

          // Validate the strategy if provided
          if (msg.strategy && !TRADING_STRATEGIES[msg.strategy]) {
            console.warn(`Invalid strategy requested: ${msg.strategy}`)
            ws.send(JSON.stringify({
              type: 'error',
              message: `Strategy '${msg.strategy}' not found. Using current strategy.`,
            }))
            // Don't return, just don't update the strategy
          } else if (msg.strategy) {
            // Update subscription with strategy if provided and valid
            if (subscriptions.has(ws)) {
              const sub = subscriptions.get(ws)
              sub.strategy = msg.strategy
              subscriptions.set(ws, sub)
            } else {
              subscriptions.set(ws, { pair, strategy: msg.strategy })
            }
          } else {
            // No strategy provided, use existing or default
            if (!subscriptions.has(ws)) {
              subscriptions.set(ws, { pair: 'xbtusdt', strategy: 'admiral_toa' })
            }
          }

          // Make sure we have data for this pair/timeframe
          const updateKey = `${pair}-${tf}`
          const now = Date.now()

          // Check if we've updated this pair/timeframe recently in the periodic update
          const lastUpdate = lastPeriodicUpdate[updateKey] || 0
          const timeSinceLastUpdate = now - lastUpdate

          // Only update if it's been more than 60 seconds since the last periodic update
          if (timeSinceLastUpdate > 60000) {
            // Check if we need to force update based on the last fetch time
            const lastFetch = lastFetchTime[updateKey] || 0
            const shouldForceUpdate = now - lastFetch > 60000

            // Fetch the data
            if (shouldForceUpdate) {
              await fetchOHLC(pair, tf, true)
            } else {
              await fetchOHLC(pair, tf, false)
            }

            // Update the last periodic update time
            lastPeriodicUpdate[updateKey] = now

            // Calculate indicators
            const indicators = calculateAllIndicators(pair, tf)

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in periodic update: expected ${tf} but got ${indicators.metadata?.timeframe}`)
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {}
              indicators.metadata.timeframe = tf
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in periodic update: ${indicators[ind].timeframe} (expected ${tf})`)
                  indicators[ind].timeframe = tf // Fix the timeframe
                }
              }
            })

            // Send indicator updates to all subscribed clients
            subscriptions.forEach(({ pair: subPair }, ws) => {
              if (subPair === pair && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'indicators',
                  pair,
                  timeframe: tf,
                  data: indicators,
                }))
              }
            })
          } else {
            console.log(`Skipping update for ${pair}/${tf} - last updated ${Math.floor(timeSinceLastUpdate / 1000)}s ago`)
          }

          // Add a small delay between timeframe updates to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY / 2))
        } catch (tfError) {
          console.error(`Error updating ${pair}/${tf}: ${tfError.message}`)
        }
      } else if (msg.type === 'selectPair') {
        // Handle selectPair message
        const pair = msg.pair.toLowerCase()
        console.log(`Client selected pair: ${pair}`)

        // Store subscription info
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws)
          sub.pair = pair
          subscriptions.set(ws, sub)
        } else {
          subscriptions.set(ws, { pair, strategy: 'admiral_toa' })
        }

        // Fetch data for the selected pair
        for (const tf of TIMEFRAMES) {
          try {
            const updateKey = `${pair}-${tf}`
            const now = Date.now()

            // Check if we already have data for this pair/timeframe
            const hasData = historicalData[pair]?.[tf]?.length > 0
            const lastFetch = lastFetchTime[updateKey] || 0
            const dataAge = now - lastFetch

            // Only fetch if we don't have data or it's older than 30 seconds
            if (!hasData || dataAge > 30000) {
              await fetchOHLC(pair, tf)
              // Update the last periodic update time to prevent immediate re-fetch
              lastPeriodicUpdate[updateKey] = now
            } else {
              console.log(`Using existing data for ${pair}/${tf} (${historicalData[pair][tf].length} candles, ${Math.floor(dataAge / 1000)}s old)`)
            }

            // Send historical data to the client
            ws.send(JSON.stringify({ type: 'historicalData', pair, timeframe: tf, data: historicalData[pair]?.[tf] || [] }))

            // Calculate and send indicators
            const indicators = calculateAllIndicators(pair, tf)

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in selectPair: expected ${tf} but got ${indicators.metadata?.timeframe}`)
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {}
              indicators.metadata.timeframe = tf
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in selectPair: ${indicators[ind].timeframe} (expected ${tf})`)
                  indicators[ind].timeframe = tf // Fix the timeframe
                }
              }
            })

            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators || {},
            }))
          } catch (e) {
            console.error(`Error fetching OHLC for ${pair}/${tf}: ${e.message}`)
          }
        }

        // Send acknowledgement
        ws.send(JSON.stringify({ type: 'pairSelected', pair }))
      } else if (msg.type === 'setStrategy') {
        // Handle setStrategy message
        const strategy = msg.strategy
        console.log(`Client set strategy: ${strategy}`)

        // Validate the strategy exists
        if (!TRADING_STRATEGIES[strategy]) {
          console.warn(`Invalid strategy requested: ${strategy}`)
          ws.send(JSON.stringify({
            type: 'error',
            message: `Strategy '${strategy}' not found. Using default strategy.`,
          }))
          return
        }

        // Store subscription info
        if (subscriptions.has(ws)) {
          const sub = subscriptions.get(ws)
          sub.strategy = strategy
          subscriptions.set(ws, sub)
        } else {
          // Default to xbtusdt if no pair is set
          subscriptions.set(ws, { pair: 'xbtusdt', strategy })
        }

        // Send acknowledgement
        ws.send(JSON.stringify({ type: 'strategySet', strategy }))

        // Recalculate indicators with the new strategy and send updates
        const pair = subscriptions.get(ws).pair
        for (const tf of TIMEFRAMES) {
          try {
            const indicators = calculateAllIndicators(pair, tf)

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in setStrategy: expected ${tf} but got ${indicators.metadata?.timeframe}`)
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {}
              indicators.metadata.timeframe = tf
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in setStrategy: ${indicators[ind].timeframe} (expected ${tf})`)
                  indicators[ind].timeframe = tf // Fix the timeframe
                }
              }
            })

            ws.send(JSON.stringify({
              type: 'indicators',
              pair,
              timeframe: tf,
              data: indicators || {},
            }))
          } catch (e) {
            console.error(`Error calculating indicators for ${pair}/${tf} with strategy ${strategy}: ${e.message}`)
          }
        }
      }
    } catch (e) {
      console.error(`WebSocket message error: ${e.message}`)
    }
  })
  */ // 🎖️ FLEET COMMANDER: End of disabled old message handler

    // The 'close' event is now handled at the connection level for better logging.

  // Handle pong messages for heartbeat
  // ws.on('pong', () => {
  //   ws.isAlive = true
  // })
// }) // 🎖️ FLEET COMMANDER: End of disabled third WebSocket handler

// Track the last time we updated each pair/timeframe in the periodic update
const lastPeriodicUpdate = {}

// Periodic data updates - optimized to reduce API rate limiting issues
setInterval(async () => {
  // Update sentiment data periodically (every 2 hours, but check every minute)
  try {
    await fetchSentimentData()
  } catch (error) {
    console.warn('Failed to update sentiment data:', error.message)
  }

  // Only update data for pairs that have active subscriptions
  const activePairs = new Set([...subscriptions.values()].map(sub => sub.pair))
  console.log(`Updating data for active pairs: ${[...activePairs].join(', ')}`)

  for (const pair of [...activePairs]) {
    try {
      // Update price data first (most important for real-time trading)
      const price = await fetchLivePrice(pair)
      if (price && price.price > 0) {
        lastLivePrice[pair] = price
        // Send price updates to all subscribed clients
        subscriptions.forEach(({ pair: subPair }, ws) => {
          if (subPair === pair && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'livePrice',
              pair,
              data: price,
            }))
          }
        })
      }

      // Update OHLC data for each timeframe
      for (const tf of TIMEFRAMES) {
        try {
          const updateKey = `${pair}-${tf}`
          const now = Date.now()

          // Check if we've updated this pair/timeframe recently in the periodic update
          const lastUpdate = lastPeriodicUpdate[updateKey] || 0
          const timeSinceLastUpdate = now - lastUpdate

          // Only update if it's been more than 60 seconds since the last periodic update
          if (timeSinceLastUpdate > 60000) {
            // Check if we need to force update based on the last fetch time
            const lastFetch = lastFetchTime[updateKey] || 0
            const shouldForceUpdate = now - lastFetch > 60000

            // Fetch the data
            if (shouldForceUpdate) {
              await fetchOHLC(pair, tf, true)
            } else {
              await fetchOHLC(pair, tf, false)
            }

            // Update the last periodic update time
            lastPeriodicUpdate[updateKey] = now

            // Calculate indicators
            const indicators = calculateAllIndicators(pair, tf)

            // Double-check that the timeframe is correctly set in the data
            if (!indicators.metadata || indicators.metadata.timeframe !== tf) {
              console.error(`Timeframe mismatch in periodic update: expected ${tf} but got ${indicators.metadata?.timeframe}`)
              // Fix the metadata
              if (!indicators.metadata) indicators.metadata = {}
              indicators.metadata.timeframe = tf
            }

            // Verify all indicators have the correct timeframe
            Object.keys(indicators).forEach(ind => {
              if (indicators[ind] && typeof indicators[ind] === 'object' && 'timeframe' in indicators[ind]) {
                if (indicators[ind].timeframe !== tf) {
                  console.error(`Indicator ${ind} has incorrect timeframe in periodic update: ${indicators[ind].timeframe} (expected ${tf})`)
                  indicators[ind].timeframe = tf // Fix the timeframe
                }
              }
            })

            // Send indicator updates to all subscribed clients
            subscriptions.forEach(({ pair: subPair }, ws) => {
              if (subPair === pair && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'indicators',
                  pair,
                  timeframe: tf,
                  data: indicators,
                }))
              }
            })
          } else {
            console.log(`Skipping update for ${pair}/${tf} - last updated ${Math.floor(timeSinceLastUpdate / 1000)}s ago`)
          }

          // Add a small delay between timeframe updates to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY / 2))
        } catch (tfError) {
          console.error(`Error updating ${pair}/${tf}: ${tfError.message}`)
        }
      }

      // Add a delay between pairs to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY))
    } catch (pairError) {
      console.error(`Error updating ${pair}: ${pairError.message}`)
    }
  }
}, 60000) // Update every minute

// Heartbeat mechanism to detect and clean up dead connections

// 🎖️ FLEET COMMANDER: DISABLED - Kraken WebSocket heartbeat (server disabled)
// Check for dead connections every 30 seconds
// setInterval(() => {
//   wss.clients.forEach(ws => {
//     if (!ws.isAlive) {
//       console.log('Terminating inactive WebSocket connection')
//       subscriptions.delete(ws)
//       return ws.terminate()
//     }

//     ws.isAlive = false
//     ws.ping(() => {})
//   })
// }, 30000)

console.log(`🎖️ FLEET COMMANDER: StarCrypt Enterprise server running on port ${port}`)
console.log(`🎖️ FLEET COMMANDER: WebSocket server running on ws://localhost:8080 (dedicated WebSocket port)`)
console.log(`Supported timeframes: ${TIMEFRAMES.join(', ')}`)
console.log(`Supported strategies: ${Object.keys(TRADING_STRATEGIES).join(', ')}`)
console.log(`Supported pairs: ${Object.keys(KRAKEN_PAIRS).join(', ')}`)
console.log('[StarfieldAnimation] 🌟 Initializing starfield animation...')
console.log('[StarfieldAnimation] 🎯 Setting up starfield canvas...')
console.log('[StarfieldAnimation] ✅ Using existing starfield canvas')
console.log('[StarfieldAnimation] ✅ Canvas setup complete: 1920 x 919')
console.log('[StarfieldAnimation] 🌟 Starfield animation started successfully')
console.log('[StarfieldAnimation] ✅ Starfield animation initialized successfully')
console.log('[StarfieldAnimation] 🎯 Canvas created: true')
console.log('[StarfieldAnimation] 🎯 Stars generated: 300')
console.log('[StarfieldAnimation] 🎯 Animation running: true')
console.log('🌟 Starfield animation initialized')
console.log('[StarfieldAnimation] 🌟 DOM ready - initializing starfield...')
console.log('🎯 Enhanced Price Display initialized')
console.log('💰 Enhanced Price Display initialized')
console.log('🎯 Enhanced Indicator Menu initialized')
console.log('[EnhancedMiniCharts] ✅ Container ready. Initializing...')
console.log('[EnhancedMiniCharts] 🚀 MASTER CHART OVERRIDE - Establishing enhanced charts as ONLY system')
console.log('[EnhancedMiniCharts] 🚫 DISABLING ALL COMPETING CHART SYSTEMS')
console.log('[EnhancedMiniCharts] 🎯 MASTER CHART OVERRIDE - Establishing enhanced charts as ONLY system')
console.log('[EnhancedMiniCharts] ✅ MASTER CHART OVERRIDE COMPLETE - Enhanced charts now control ALL chart operations')
console.log('[EnhancedMiniCharts] 🛡️ ACTIVATING MAXIMUM CHART PROTECTION PROTOCOL')
console.log('[EnhancedMiniCharts] 🎯 Creating enhanced charts for all indicators...')
console.log('[EnhancedMiniCharts] ✅ Creating enhanced chart for rsi')
console.log('[EnhancedMiniCharts] Enhanced chart created for rsi')
console.log('[EnhancedMiniCharts] ✅ Creating enhanced chart for stochRsi')
console.log('[EnhancedMiniCharts] Enhanced chart created for stochRsi')
console.log('[EnhancedMiniCharts] ✅ Creating enhanced chart for macd')
console.log('[EnhancedMiniCharts] Enhanced chart created for macd')
console.log('[EnhancedMiniCharts] ✅ Creating enhanced chart for mfi')
console.log('[EnhancedMiniCharts] Enhanced chart created for mfi')
console.log('[EnhancedMiniCharts] ✅ Creating enhanced chart for volume')
console.log('[EnhancedMiniCharts] Enhanced chart created for volume')
console.log('[EnhancedMiniCharts] ✅ Created 5 enhanced charts out of 5 indicators')
console.log('[EnhancedMiniCharts] Instance created and available globally.')
console.log('🚀 DEGEN ARMY: DOM loaded - deploying ML systems')
console.log('🎯 COMMANDER: Initializing ML Historical Analysis - FULL SPECTRUM DOMINANCE')
console.log('✅ DEGEN ARMY: ML Historical Analysis already ONLINE')
console.log('WebSocket connection to \'ws://localhost:3000/\' failed:')
console.log('connectWebSocket @ localhost/:10678')
console.log('WebSocket error: undefined')
console.log('ws.onerror @ localhost/:11171')
console.log('ml-loop-prevention.js:151 WebSocket closed: 1006')
console.log('ml-loop-prevention.js:151 Attempting to reconnect (1/10) in 5s...')
console.log('ml-loop-prevention.js:151 Logic menu module initialized')
console.log('ml-loop-prevention.js:151 🚀 UNIFIED SIGNAL COMMANDER: 🎯 COMPLETE SYSTEM TAKEOVER - Disabling ALL competing systems')
console.log('ml-loop-prevention.js:151 ✅ UNIFIED SIGNAL COMMANDER: COMPLETE SYSTEM TAKEOVER SUCCESSFUL - All competing systems disabled')
console.log('ml-loop-prevention.js:151 Indicator menu initialized with 5 enabled indicators')
console.log('ml-loop-prevention.js:151 [MLHistoricalAnalysis] ⚠️ No fresh data available')
console.log('ml-loop-prevention.js:151 🔗 ML Systems ready, connecting signal lights...')
console.log('ml-loop-prevention.js:151 🔗 CONNECT: Found 35 signal lights to connect')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 0: rsi(1m) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔍 Attributes: Object')
console.log('ml-loop-prevention.js:151 🔗 CONNECTED 0: rsi(1m) - ID:  - Class: signal-circle waiting-for-data')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 1: rsi(5m) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔗 CONNECTED 1: rsi(5m) - ID:  - Class: signal-circle waiting-for-data')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 2: rsi(15m) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔗 CONNECTED 2: rsi(15m) - ID:  - Class: signal-circle waiting-for-data')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 3: rsi(1h) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔗 CONNECTED 3: rsi(1h) - ID:  - Class: signal-circle waiting-for-data')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 4: rsi(4h) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔗 CONNECTED 4: rsi(4h) - ID:  - Class: signal-circle waiting-for-data')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 5: rsi(1d) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 6: rsi(1w) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 7: stochRsi(1m) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 8: stochRsi(5m) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 9: stochRsi(15m) - signal-circle waiting-for-data - ID:')
console.log('ml-loop-prevention.js:151 🔗 CONNECT: Successfully connected 35 signal lights')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 MASTER TOOLTIP SYSTEM - Establishing as ONLY tooltip system')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Setting up enhanced actionable tooltips for signal circles')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Setting up signal circle tooltips with actionable trading intel')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🚫 Disabling ALL competing tooltip systems')
console.log('ml-loop-prevention.js:151 [TooltipFixes] ✅ All competing tooltip systems disabled')
console.log('ml-loop-prevention.js:151 [TooltipFixes] ✅ MASTER TOOLTIP SYSTEM ESTABLISHED - All competing systems disabled')
console.log('ml-loop-prevention.js:151 [StrategyHelperEnhancement] Adding missing helper text for strategies...')
console.log('ml-loop-prevention.js:151 [StrategyHelperEnhancement] Added helper text for 21 strategies')
console.log('ml-loop-prevention.js:151 [StrategyHelperEnhancement] Strategy helper enhancement completed successfully')
console.log('ml-loop-prevention.js:151 🔗 BRIDGE: Connecting ML systems to Unified Signal Commander...')
console.log('ml-loop-prevention.js:151 ✅ BRIDGE: Found Unified Signal Commander, establishing connection')
console.log('ml-loop-prevention.js:151 🔗 BRIDGE: ML selectedLights Set synchronized with Unified Commander')
console.log('ml-loop-prevention.js:151 🔗 BRIDGE: ML analysis function bridged to Unified Commander')
console.log('ml-loop-prevention.js:151 ✅ BRIDGE: ML systems successfully connected to Unified Signal Commander')
console.log('8794.580d25ad66d1fc70c9d2.js:21 2025-07-18T12:18:28.021Z:Property:The state with a data type: unknown does not match a schema')
console.log('b @ 8794.580d25ad66d1fc70c9d2.js:21')
console.log('advanced-ml-features.js:1068 [AdvancedML] ❌ CRITICAL: All price sources failed - NO FALLBACK DATA AVAILABLE')
console.log('advanced-ml-features.js:472 [AdvancedML] ❌ CRITICAL: generateDefaultMarketData called - NO DUMMY DATA ALLOWED')
console.log('generateDefaultMarketData @ advanced-ml-features.js:472')
console.log('advanced-ml-features.js:466 [AdvancedML] ❌ Error generating predictions: Error: TRADING_INTEGRITY_VIOLATION: Dummy data generation is prohibited in StarCrypt')
console.log('    at AdvancedMLFeatures.generateDefaultMarketData (advanced-ml-features.js:475:11)')
console.log('    at AdvancedMLFeatures.generateAdvancedPredictions (advanced-ml-features.js:435:34)')
console.log('    at advanced-ml-features.js:1235:12')
console.log('generateAdvancedPredictions @ advanced-ml-features.js:466')
console.log('advanced-ml-features.js:554 [AdvancedML] 🚨 ML Error: Failed to generate predictions: TRADING_INTEGRITY_VIOLATION: Dummy data generation is prohibited in StarCrypt')
console.log('showMLError @ advanced-ml-features.js:554')
console.log('ml-loop-prevention.js:151 [IndicatorMenuFixes] Initializing indicator menu fixes...')
console.log('ml-loop-prevention.js:151 [IndicatorMenuFixes] Added reset button to indicator menu')
console.log('ml-loop-prevention.js:151 [IndicatorMenuFixes] Indicator menu fixes applied successfully')
console.log('ml-loop-prevention.js:151 [TimeframeCheckboxFixes] Initializing timeframe checkbox functionality...')
console.log('ml-loop-prevention.js:151 [TimeframeCheckboxFixes] Loaded saved timeframes: Array(3)')
console.log('ml-loop-prevention.js:151 [TimeframeCheckboxFixes] Skipping ticker container - controls belong in menu')
console.log('ml-loop-prevention.js:151 [TimeframeCheckboxFixes] Enhanced timeframe container')
console.log('ml-loop-prevention.js:151 [TimeframeCheckboxFixes] Timeframe checkbox fixes applied successfully')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Applying critical fixes...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Fixing menu opening issues...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Fixing hover cursor issues...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Fixing mini chart styling...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Fixing threshold menu layout...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Fixing starfield animation size...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Fixing indicator menu toggle issues...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Analyzing Refresh Indicators vs Force Update Lights...')
console.log('ml-loop-prevention.js:151 [CriticalFixes] ANALYSIS COMPLETE:')
console.log('ml-loop-prevention.js:151 - Refresh Indicators: Calls requestAllIndicators() - Requests fresh data from server via WebSocket')
console.log('ml-loop-prevention.js:151 - Force Update Lights: Calls forceUpdateSignalLights() - Recalculates colors using existing data')
console.log('ml-loop-prevention.js:151 - CONCLUSION: These are NOT duplicates - they serve different purposes')
console.log('ml-loop-prevention.js:151 [CriticalFixes] Critical fixes applied successfully')
console.log('ml-loop-prevention.js:151 🔍 DEBUG: Searching for signal elements...')
console.log('ml-loop-prevention.js:151 🔍 DEBUG: Found 98 potential signal elements')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 0: rsi(unknown) - indicator-toggle - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 1: rsi(unknown) - indicator-info - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 2: stochRsi(unknown) - indicator-toggle - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 3: stochRsi(unknown) - indicator-info - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 4: williamsR(unknown) - indicator-toggle - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 5: williamsR(unknown) - indicator-info - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 6: ultimateOscillator(unknown) - indicator-toggle - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 7: ultimateOscillator(unknown) - indicator-info - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 8: mfi(unknown) - indicator-toggle - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG Signal 9: mfi(unknown) - indicator-info - ID:')
console.log('ml-loop-prevention.js:151 🔍 DEBUG: Momentum table found: YES')
console.log('ml-loop-prevention.js:151 🔍 DEBUG: Momentum table HTML:')
console.log('    <table class="indicators-table" id="momentum-table"><tr data-indicator="rsi" data-ind="rsi" data-strategy="momentum_blast" id="indicator-row-rsi" class="indicator-row signal-row"><td class="label...')
console.log('ml-loop-prevention.js:151 🎯 DEGEN COMMAND CENTER: Functions armed and ready')
console.log('ml-loop-prevention.js:151 ⚡ COMMANDER: Try testSignalClick("rsi", "1h") then degenAnalyze()')
console.log('ml-loop-prevention.js:151 🔥 DEGEN ARMY: Or use degenAnalyze() directly for FULL SPECTRUM ANALYSIS')
console.log('ml-loop-prevention.js:151 💥 EMERGENCY: Use forceMLAnalysis() to OVERRIDE ML system directly')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🔍 Found 35 signal circles with proper data attributes')
console.log('ml-loop-prevention.js:151 [TooltipFixes] Initialized enhanced tooltips for 35 signal circles')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Force-initialized tooltips for existing signal circles')
console.log('ml-loop-prevention.js:151 [CriticalFixes] DISABLED - Mini chart updates handled by enhanced charts')
console.log('hasValidMarketData @ advanced-ml-features.js:81')
console.log('(anonymous) @ advanced-ml-features.js:97')
console.log('ml-loop-prevention.js:151 🏥 HEALTH CHECK: Performing system health assessment...')
console.log('ml-loop-prevention.js:164 🚨 SYSTEM FAILURE: WebSocket Connection failed (1/5)')
console.log('handleSystemFailure @ system-health-monitor.js:124')
console.log('performHealthCheck @ system-health-monitor.js:99')
console.log('(anonymous) @ system-health-monitor.js:79')
console.log('ml-loop-prevention.js:164 ⚠️ HEALTH CHECK: 1 issues detected (83% healthy): [\'WebSocket Connection\']')
console.log('performHealthCheck @ system-health-monitor.js:113')
console.log('ml-loop-prevention.js:151 🔍 DATA FLOW VERIFIER: Performing comprehensive data verification...')
console.log('ml-loop-prevention.js:164 🚨 DATA ISSUE: WebSocket Data - WebSocket not connected')
console.log('performVerification @ data-flow-verifier.js:89')
console.log('(anonymous) @ data-flow-verifier.js:69')
console.log('ml-loop-prevention.js:164 🚨 DATA ISSUE: Price Data - Invalid currentPrice: object [object HTMLDivElement]')
console.log('ml-loop-prevention.js:164 🚨 DATA ISSUE: Indicator Data - No timeframe data available')
console.log('ml-loop-prevention.js:151 ✅ DATA OK: Signal Light Data - 35/35 signals connected (100.0%)')
console.log('verifyMLData @ data-flow-verifier.js:231')
console.log('check @ data-flow-verifier.js:58')
console.log('performVerification @ data-flow-verifier.js:81')
console.log('ml-loop-prevention.js:164 🚨 DATA ISSUE: ML System Data - ML system cannot access valid price data')
console.log('data-flow-verifier.js:103 🚨 CRITICAL DATA ISSUES: 3 critical, 4 total')
console.log('performVerification @ data-flow-verifier.js:103')
console.log('ml-loop-prevention.js:151 🛠️ DATA FLOW VERIFIER: Attempting data recovery...')
console.log('ml-loop-prevention.js:151 🛠️ Attempting price data recovery...')
console.log('ml-loop-prevention.js:151 🔄 DELAYED: Checking for signal lights after WebSocket data...')
console.log('ml-loop-prevention.js:151 🔗 CONNECT: Successfully connected 0 signal lights')
console.log('ml-loop-prevention.js:151 [EnhancedMiniCharts] 🔄 Force updating 5 enhanced charts')
console.log('ml-loop-prevention.js:151 [EnhancedMiniCharts] Enhanced chart created for rsi')
console.log('ml-loop-prevention.js:151 [EnhancedMiniCharts] Enhanced chart created for stochRsi')
console.log('ml-loop-prevention.js:151 [EnhancedMiniCharts] Enhanced chart created for mfi')
console.log('ml-loop-prevention.js:151 [EnhancedMiniCharts] Enhanced chart created for macd')
console.log('ml-loop-prevention.js:151 [EnhancedMiniCharts] Enhanced chart created for volume')
console.log('ml-loop-prevention.js:151 [WebSocket] Connecting to: ws://localhost:8080/')
console.log('(index):10678 WebSocket connection to \'ws://localhost:8080/\' failed:')
console.log('connectWebSocket @ (index):10678')
console.log('(index):11171 WebSocket error: undefined')
console.log('ws.onerror @ (index):11171')
console.log('ml-loop-prevention.js:151 Attempting to reconnect (2/10) in 8s...')
console.log('[Violation] \'requestAnimationFrame\' handler took 54ms')
console.log('ml-loop-prevention.js:164 🛡️ PREVENTIVE: WebSocket not connected, may need recovery')
console.log('performPreventiveCheck @ error-recovery.js:217')
console.log('(anonymous) @ error-recovery.js:155')
console.log('(anonymous) @ data-flow-verifier.js:65')
console.log('ml-loop-prevention.js:151 Attempting to reconnect (3/10) in 11s...')
console.log('28767.71912cc514e56b83ba67.js:7 Fetch failed loading: POST "https://telemetry.tradingview.com/widget/report".')
console.log('n @ 28767.71912cc514e56b83ba67.js:7')
console.log('_sendTelemetryToService @ 8794.580d25ad66d1fc70c9d2.js:6')
console.log('ml-loop-prevention.js:164 [TooltipFixes] ⚠️ No real timestamp found for volume-1w, using fallback')
console.log('getLastUpdateTimestamp @ tooltip-fixes.js:1697')
console.log('showTooltip @ tooltip-fixes.js:716')
console.log('(anonymous) @ tooltip-fixes.js:676')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Signal circle hover detected: <tr data-indicator=​"volume" data-ind=​"volume" data-strategy=​"momentum_blast" id=​"indicator-row-volume" class=​"indicator-row signal-row">​…​</tr>​flex')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Showing tooltip for: volume 4h')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 📊 Data for tooltip: {indicator: \'volume\', timeframe: \'4h\', data: undefined, color: \'#808080\'}')
console.log('ml-loop-prevention.js:164 [TooltipFixes] ⚠️ No real timestamp found for volume-4h, using fallback')
console.log('generateSplendidTooltip @ tooltip-fixes.js:294')
console.log('showEnhancedSignalTooltip @ tooltip-fixes.js:273')
console.log('tooltipMouseEnterHandler @ tooltip-fixes.js:175')
console.log('ml-loop-prevention.js:151 [TooltipFixes] ✅ Tooltip displayed successfully')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Signal circle hover detected: <div class=​"signal-circle waiting-for-data" data-ind=​"volume" data-tf=​"1w" data-tooltip=​"VOLUME (1w)​\n\n📚 Volume shows trading activity. High volume confirms price moves. Volume spikes often precede significant price changes.\n🕒 Last Updated:​ 18/​07/​2025, 13:​13:​45" data-indicator=​"volume" data-timeframe=​"1w" style=​"background-color:​ rgb(64, 64, 96)​;​" data-admiral-connected=​"true" data-connected-indicator=​"volume" data-connected-timeframe=​"1w">​…​</div>​flex')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 🎯 Showing tooltip for: volume 1w')
console.log('ml-loop-prevention.js:151 [TooltipFixes] 📊 Data for tooltip: {indicator: \'volume\', timeframe: \'1w\', data: undefined, color: \'#00ff00\'}')
console.log('handler @ tooltip-fixes.js:1598')
console.log('ml-loop-prevention.js:151 Attempting to reconnect (4/10) in 17s...')
