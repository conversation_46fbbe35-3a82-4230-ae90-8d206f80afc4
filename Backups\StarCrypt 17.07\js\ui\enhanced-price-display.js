/**
 * Enhanced Price Display Component for StarCrypt
 * Provides professional price display with bid/ask, spread, volume, and real-time updates
 */

class EnhancedPriceDisplay {
  constructor() {
    this.currentPrice = null;
    this.bidPrice = null;
    this.askPrice = null;
    this.volume24h = null;
    this.priceChange24h = null;
    this.priceChangePercent24h = null;
    this.lastUpdateTime = null;
    this.priceHistory = [];
    this.maxHistoryLength = 100;
    
    // Animation and visual state
    this.isFlashing = false;
    this.lastDirection = 'neutral'; // 'up', 'down', 'neutral'
    
    this.init();
  }
  
  init() {
    this.enhancePriceDisplayElements();
    this.bindEvents();
    console.log('🎯 Enhanced Price Display initialized');
  }
  
  enhancePriceDisplayElements() {
    const currentPriceElement = document.getElementById('currentPrice');
    const bidAskElement = document.getElementById('bidAskPrice');
    
    if (currentPriceElement) {
      // Enhance current price element with StarCrypt styling
      currentPriceElement.innerHTML = `
        <div class="enhanced-price-container">
          <div class="price-header">
            <span class="crypto-pair">⚡ BTC/USD</span>
            <span class="live-indicator">🔴 LIVE</span>
          </div>
          <div class="price-main">
            <span class="price-value" id="priceValue">Loading...</span>
            <span class="price-direction" id="priceDirection">●</span>
          </div>
          <div class="price-stats">
            <div class="price-change">
              <span class="change-24h" id="change24h">+0.00%</span>
              <span class="change-amount" id="changeAmount">+$0.00</span>
            </div>
            <div class="price-volume">
              <span class="volume-label">24h Vol:</span>
              <span class="volume-value" id="volumeValue">$0</span>
            </div>
          </div>
        </div>
      `;
    }
    
    if (bidAskElement) {
      // Enhance bid/ask element with professional trading display
      bidAskElement.innerHTML = `
        <div class="enhanced-bidask-container">
          <div class="bid-section">
            <div class="bid-header">
              <span class="bid-label">📉 BID</span>
              <span class="bid-size" id="bidSize">0.00</span>
            </div>
            <span class="bid-value" id="bidValue">Loading...</span>
          </div>
          <div class="spread-section">
            <div class="spread-header">
              <span class="spread-label">📊 SPREAD</span>
            </div>
            <div class="spread-values">
              <span class="spread-value" id="spreadValue">$0.00</span>
              <span class="spread-percent" id="spreadPercent">(0.00%)</span>
            </div>
          </div>
          <div class="ask-section">
            <div class="ask-header">
              <span class="ask-label">📈 ASK</span>
              <span class="ask-size" id="askSize">0.00</span>
            </div>
            <span class="ask-value" id="askValue">Loading...</span>
          </div>
        </div>
      `;
    }
    
    // Add enhanced styles
    this.addEnhancedStyles();
  }
  
  addEnhancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .enhanced-price-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.9), rgba(0, 10, 30, 0.9));
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px;
        margin: 10px 0;
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
        transition: all 0.3s ease;
      }
      
      .enhanced-price-container:hover {
        border-color: rgba(0, 255, 255, 0.6);
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.4);
      }
      
      .price-main {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 8px;
      }
      
      .price-label {
        font-size: 0.9rem;
        color: #00FFFF;
        font-weight: bold;
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
      }
      
      .price-value {
        font-size: 2rem;
        font-weight: bold;
        font-family: 'Orbitron', monospace;
        color: #FFFFFF;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
      }
      
      .price-direction {
        font-size: 1.5rem;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px currentColor;
      }
      
      .price-direction.up {
        color: #00FF00;
        animation: pulseGreen 1s ease-in-out;
      }
      
      .price-direction.down {
        color: #FF0000;
        animation: pulseRed 1s ease-in-out;
      }
      
      .price-direction.neutral {
        color: #808080;
      }
      
      .price-change {
        display: flex;
        gap: 15px;
        font-size: 0.9rem;
      }
      
      .change-24h, .change-amount {
        font-family: 'Orbitron', monospace;
        font-weight: bold;
        transition: all 0.3s ease;
      }
      
      .change-24h.positive, .change-amount.positive {
        color: #00FF00;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
      }
      
      .change-24h.negative, .change-amount.negative {
        color: #FF0000;
        text-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
      }
      
      .enhanced-bidask-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        background: linear-gradient(135deg, rgba(0, 30, 60, 0.8), rgba(0, 15, 40, 0.8));
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        margin: 10px 0;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
      }
      
      .bid-section, .ask-section, .spread-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
      }
      
      .bid-label, .ask-label, .spread-label {
        font-size: 0.7rem;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      
      .bid-label {
        color: #00FF00;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
      }
      
      .ask-label {
        color: #FF6B6B;
        text-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
      }
      
      .spread-label {
        color: #FFD700;
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
      }
      
      .bid-value, .ask-value, .spread-value {
        font-family: 'Orbitron', monospace;
        font-weight: bold;
        font-size: 1.1rem;
        color: #FFFFFF;
      }
      
      .spread-percent {
        font-size: 0.8rem;
        color: #CCCCCC;
        font-family: 'Orbitron', monospace;
      }
      
      @keyframes pulseGreen {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.2); color: #00FF88; }
      }
      
      @keyframes pulseRed {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.2); color: #FF4444; }
      }
      
      @keyframes priceFlash {
        0%, 100% { background: transparent; }
        50% { background: rgba(0, 255, 255, 0.2); }
      }
      
      .price-flash {
        animation: priceFlash 0.5s ease-in-out;
      }
      
      .enhanced-price-container.price-up {
        border-color: rgba(0, 255, 0, 0.5);
        box-shadow: 0 0 25px rgba(0, 255, 0, 0.3);
      }
      
      .enhanced-price-container.price-down {
        border-color: rgba(255, 0, 0, 0.5);
        box-shadow: 0 0 25px rgba(255, 0, 0, 0.3);
      }
    `;
    document.head.appendChild(style);
  }
  
  updatePrice(priceData) {
    const {
      price,
      bid,
      ask,
      volume24h,
      priceChange24h,
      priceChangePercent24h
    } = priceData;
    
    // Store previous price for comparison
    const previousPrice = this.currentPrice;
    
    // Update internal state
    this.currentPrice = price;
    this.bidPrice = bid;
    this.askPrice = ask;
    this.volume24h = volume24h;
    this.priceChange24h = priceChange24h;
    this.priceChangePercent24h = priceChangePercent24h;
    this.lastUpdateTime = Date.now();
    
    // Add to price history
    this.priceHistory.push({
      price,
      timestamp: this.lastUpdateTime
    });
    
    if (this.priceHistory.length > this.maxHistoryLength) {
      this.priceHistory.shift();
    }
    
    // Determine price direction
    let direction = 'neutral';
    if (previousPrice !== null) {
      if (price > previousPrice) {
        direction = 'up';
      } else if (price < previousPrice) {
        direction = 'down';
      }
    }
    this.lastDirection = direction;
    
    // Update UI elements
    this.updatePriceElements(direction);
    this.updateBidAskElements();
    
    // Trigger visual effects
    this.triggerPriceAnimation(direction);
    
    console.log(`💰 Price updated: $${this.formatPrice(price)} (${direction})`);
  }
  
  updatePriceElements(direction) {
    const priceValue = document.getElementById('priceValue');
    const priceDirection = document.getElementById('priceDirection');
    const change24h = document.getElementById('change24h');
    const changeAmount = document.getElementById('changeAmount');
    const container = document.querySelector('.enhanced-price-container');
    
    if (priceValue) {
      priceValue.textContent = `$${this.formatPrice(this.currentPrice)}`;
    }
    
    if (priceDirection) {
      priceDirection.className = `price-direction ${direction}`;
      priceDirection.textContent = direction === 'up' ? '▲' : direction === 'down' ? '▼' : '●';
    }
    
    if (change24h && this.priceChangePercent24h !== null) {
      const isPositive = this.priceChangePercent24h >= 0;
      change24h.textContent = `${isPositive ? '+' : ''}${this.priceChangePercent24h.toFixed(2)}%`;
      change24h.className = `change-24h ${isPositive ? 'positive' : 'negative'}`;
    }
    
    if (changeAmount && this.priceChange24h !== null) {
      const isPositive = this.priceChange24h >= 0;
      changeAmount.textContent = `${isPositive ? '+' : ''}$${this.formatPrice(Math.abs(this.priceChange24h))}`;
      changeAmount.className = `change-amount ${isPositive ? 'positive' : 'negative'}`;
    }
    
    if (container) {
      container.classList.remove('price-up', 'price-down');
      if (direction !== 'neutral') {
        container.classList.add(`price-${direction}`);
      }
    }
  }
  
  updateBidAskElements() {
    const bidValue = document.getElementById('bidValue');
    const askValue = document.getElementById('askValue');
    const spreadValue = document.getElementById('spreadValue');
    const spreadPercent = document.getElementById('spreadPercent');
    
    if (bidValue && this.bidPrice !== null) {
      bidValue.textContent = `$${this.formatPrice(this.bidPrice)}`;
    }
    
    if (askValue && this.askPrice !== null) {
      askValue.textContent = `$${this.formatPrice(this.askPrice)}`;
    }
    
    if (spreadValue && spreadPercent && this.bidPrice !== null && this.askPrice !== null) {
      const spread = this.askPrice - this.bidPrice;
      const spreadPercentValue = (spread / this.askPrice) * 100;
      
      spreadValue.textContent = `$${this.formatPrice(spread)}`;
      spreadPercent.textContent = `(${spreadPercentValue.toFixed(3)}%)`;
    }
  }
  
  triggerPriceAnimation(direction) {
    const container = document.querySelector('.enhanced-price-container');
    if (container && direction !== 'neutral') {
      container.classList.add('price-flash');
      setTimeout(() => {
        container.classList.remove('price-flash');
      }, 500);
    }
  }
  
  formatPrice(price) {
    if (price === null || price === undefined) return '0.00';
    
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  }
  
  bindEvents() {
    // Listen for price updates from WebSocket
    document.addEventListener('priceUpdate', (event) => {
      this.updatePrice(event.detail);
    });
    
    // Add click handler for additional info
    const container = document.querySelector('.enhanced-price-container');
    if (container) {
      container.addEventListener('click', () => {
        this.showPriceDetails();
      });
    }
  }
  
  showPriceDetails() {
    if (this.priceHistory.length === 0) return;
    
    const recent = this.priceHistory.slice(-10);
    const minPrice = Math.min(...recent.map(p => p.price));
    const maxPrice = Math.max(...recent.map(p => p.price));
    const avgPrice = recent.reduce((sum, p) => sum + p.price, 0) / recent.length;
    
    const details = `
      📊 Price Statistics (Last 10 updates):
      • Current: $${this.formatPrice(this.currentPrice)}
      • Min: $${this.formatPrice(minPrice)}
      • Max: $${this.formatPrice(maxPrice)}
      • Avg: $${this.formatPrice(avgPrice)}
      • Spread: $${this.formatPrice(this.askPrice - this.bidPrice)}
      • Last Update: ${new Date(this.lastUpdateTime).toLocaleTimeString()}
    `;
    
    console.log(details);
    
    // Show tooltip or modal with details
    this.showTooltip(details);
  }
  
  showTooltip(text) {
    // Create temporary tooltip
    const tooltip = document.createElement('div');
    tooltip.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 20, 40, 0.95);
      color: #FFFFFF;
      padding: 20px;
      border-radius: 8px;
      border: 2px solid rgba(0, 255, 255, 0.5);
      font-family: 'Orbitron', monospace;
      font-size: 0.9rem;
      white-space: pre-line;
      z-index: 10000;
      box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
    `;
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    // Remove after 3 seconds
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 3000);
  }
  
  // Public method to get current price data
  getCurrentPriceData() {
    return {
      price: this.currentPrice,
      bid: this.bidPrice,
      ask: this.askPrice,
      spread: this.askPrice - this.bidPrice,
      spreadPercent: ((this.askPrice - this.bidPrice) / this.askPrice) * 100,
      direction: this.lastDirection,
      lastUpdate: this.lastUpdateTime,
      priceHistory: this.priceHistory.slice(-10) // Last 10 prices
    };
  }
}

// Initialize enhanced price display
window.EnhancedPriceDisplay = EnhancedPriceDisplay;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (!window.enhancedPriceDisplay) {
    window.enhancedPriceDisplay = new EnhancedPriceDisplay();
    console.log('💰 Enhanced Price Display initialized');
  }
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
  // DOM is still loading
} else {
  // DOM is already loaded
  if (!window.enhancedPriceDisplay) {
    window.enhancedPriceDisplay = new EnhancedPriceDisplay();
    console.log('💰 Enhanced Price Display initialized (immediate)');
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedPriceDisplay;
}
