/* =========================================================
   StarCrypt Indicator Display System - CLEAN IMPLEMENTATION
   This implementation fixes the following issues:
   1. Prevents [object Object] in the UI
   2. Ensures exactly 7 signal lights per indicator row
   3. <PERSON><PERSON><PERSON> handles neutral state (always gray, never blank)
   4. Prevents memory leaks and duplicate event handlers
   5. Optimizes performance with controlled refresh rates
 ========================================================= */

// Global tracking for initialization
window.indicatorsInitialized = false;

// Function to get proper signal color with 5-color logic
function getSignalColor(signal, strength = 0.5) {
  // Force neutral to be gray, never blank
  if (!signal || signal === 'neutral') {
    return '#808080'; // Medium gray for neutral
  }

  // 5-color logic implementation
  if (signal === 'buy') {
    return strength > 0.6 ? '#00FF00' : '#00AAFF'; // Strong vs mild buy
  } else if (signal === 'sell') {
    return strength > 0.6 ? '#FF0000' : '#FFA500'; // Strong vs mild sell
  }

  // Fallback
  return '#808080';
}

// Create signal lights for all indicators
function createAllIndicatorLights() {
  console.log('[IndicatorDisplay] Creating signal lights for all indicators');

  // Get all required indicators based on current strategy
  const indicators = getAllRequiredIndicators();
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];

  // Process each indicator row
  indicators.forEach(indicator => {
    try {
      // Skip invalid indicators
      if (typeof indicator !== 'string') {
        console.error('[IndicatorDisplay] Invalid indicator:', indicator);
        return;
      }

      // Find or create the indicator row
      const row = getOrCreateIndicatorRow(indicator);
      
      // Create signal lights container if it doesn't exist
      let lightsContainer = row.querySelector('.signal-lights');
      if (!lightsContainer) {
        lightsContainer = document.createElement('div');
        lightsContainer.className = 'signal-lights';
        row.appendChild(lightsContainer);
      }

      // Clear existing lights
      lightsContainer.innerHTML = '';

      // Create a light for each timeframe
      timeframes.forEach(timeframe => {
        const light = document.createElement('div');
        light.className = 'signal-light';
        light.dataset.indicator = indicator;
        light.dataset.timeframe = timeframe;
        light.title = `${indicator.toUpperCase()} (${timeframe})`;
        
        // Set initial state
        light.style.backgroundColor = '#808080';
        
        lightsContainer.appendChild(light);
      });

      // Add a small spacer between lights and chart
      const spacer = document.createElement('div');
      spacer.className = 'light-chart-spacer';
      row.appendChild(spacer);
      
    } catch (error) {
      console.error(`[IndicatorDisplay] Error creating lights for ${indicator}:`, error);
    }
  });
}

// Get or create an indicator row
function getOrCreateIndicatorRow(indicator) {
  const container = document.getElementById('momentum-indicators');
  if (!container) {
    console.error('[IndicatorDisplay] Could not find momentum-indicators container');
    return null;
  }

  // Try to find existing row
  let row = container.querySelector(`.indicator-row[data-indicator="${indicator}"]`);
  
  if (!row) {
    // Create new row
    row = document.createElement('div');
    row.className = 'indicator-row';
    row.dataset.indicator = indicator;
    
    // Add label
    const label = document.createElement('div');
    label.className = 'indicator-label';
    label.textContent = indicator.toUpperCase();
    row.appendChild(label);
    
    container.appendChild(row);
  }
  
  return row;
}

// Create mini charts for all indicators
function createAllMiniCharts() {
  console.log('[IndicatorDisplay] Creating mini charts for all indicators');
  
  const indicators = getAllRequiredIndicators();
  indicators.forEach(indicator => {
    try {
      createMiniChart(indicator);
    } catch (error) {
      console.error(`[IndicatorDisplay] Error creating chart for ${indicator}:`, error);
    }
  });
}

// Create mini chart for a specific indicator
function createMiniChart(indicator) {
  const row = getOrCreateIndicatorRow(indicator);
  if (!row) return;
  
  // Remove existing chart container if it exists
  const existingChart = row.querySelector('.mini-chart-container');
  if (existingChart) {
    row.removeChild(existingChart);
  }
  
  // Create chart container
  const container = document.createElement('div');
  container.className = 'mini-chart-container';
  
  const canvas = document.createElement('canvas');
  canvas.className = 'mini-chart';
  container.appendChild(canvas);
  
  row.appendChild(container);
  
  // Create chart instance
  const ctx = canvas.getContext('2d');
  const chart = new Chart(ctx, {
    type: 'line',
    data: createEnhancedChartData(indicator),
    options: createEnhancedChartOptions(indicator)
  });
  
  return chart;
}

// Create enhanced chart data
function createEnhancedChartData(indicator) {
  const timeLabels = Array(24).fill(0).map((_, i) => `${i}:00`);
  const primaryColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF';
  const secondaryColor = adjustColorOpacity(primaryColor, 0.7);
  
  const baseDataset = {
    fill: true,
    pointRadius: 0,
    borderWidth: 1.5,
    tension: 0.3
  };

  // Customize data based on indicator type
  if (indicator === 'macd') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'MACD',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 10),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.2)
        },
        {
          ...baseDataset,
          label: 'Signal',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 5),
          borderColor: secondaryColor,
          backgroundColor: 'transparent'
        },
        {
          ...baseDataset,
          label: 'Histogram',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 3),
          borderColor: adjustColorOpacity(primaryColor, 0.5),
          backgroundColor: createGradient(primaryColor, 0.1),
          borderWidth: 0
        }
      ]
    };
  } else {
    // Default chart for other indicators
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: indicator.toUpperCase(),
          data: Array(24).fill(0).map(() => Math.random() * 100),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.2)
        }
      ]
    };
  }
}

// Create chart options
function createEnhancedChartOptions(indicator) {
  const isOscillator = ['rsi', 'stochrsi', 'williamsr', 'ultimateoscillator'].includes(indicator.toLowerCase());
  
  return {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 0
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: false
      }
    },
    scales: {
      x: {
        display: false
      },
      y: {
        display: false,
        min: isOscillator ? 0 : null,
        max: isOscillator ? 100 : null
      }
    },
    elements: {
      line: {
        borderWidth: 1.5
      },
      point: {
        radius: 0
      }
    }
  };
}

// Helper function to create gradient
function createGradient(color, opacity) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const gradient = ctx.createLinearGradient(0, 0, 0, 40);
  gradient.addColorStop(0, adjustColorOpacity(color, opacity));
  gradient.addColorStop(1, adjustColorOpacity(color, 0));
  return gradient;
}

// Helper function to adjust color opacity
function adjustColorOpacity(color, opacity) {
  // If color is already in rgba format
  if (color.startsWith('rgba')) {
    return color.replace(/[\d\.]+\)$/, `${opacity})`);
  }
  
  // If color is in hex format
  if (color.startsWith('#')) {
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  // Default fallback
  return `rgba(0, 0, 0, ${opacity})`;
}

// Get all required indicators for current strategy
function getAllRequiredIndicators() {
  try {
    if (window.StrategyManager && window.StrategyManager.getCurrentStrategy) {
      const strategy = window.StrategyManager.getCurrentStrategy();
      if (strategy && strategy.indicators) {
        return strategy.indicators;
      }
    }
    
    // Default indicators if no strategy is set
    return ['rsi', 'macd', 'stochrsi', 'williamsr', 'ultimateoscillator'];
  } catch (error) {
    console.error('[IndicatorDisplay] Error getting required indicators:', error);
    return [];
  }
}

// Update all indicators
function updateAllIndicators() {
  if (!window.indicatorsInitialized) {
    console.log('[IndicatorDisplay] Indicators not initialized, skipping update');
    return;
  }
  
  const indicators = getAllRequiredIndicators();
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
  
  indicators.forEach(indicator => {
    timeframes.forEach(timeframe => {
      updateIndicator(indicator, timeframe);
    });
  });
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe) {
  try {
    const data = getIndicatorData(indicator, timeframe);
    updateSignalLight(indicator, timeframe, data);
    updateChart(indicator, data);
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating ${indicator} (${timeframe}):`, error);
  }
}

// Get indicator data (simulated for now)
function getIndicatorData(indicator, timeframe) {
  // In a real implementation, this would fetch data from your data source
  return {
    value: Math.random() * 100,
    signal: Math.random() > 0.5 ? 'buy' : 'sell',
    strength: Math.random(),
    timestamp: Date.now()
  };
}

// Update signal light
function updateSignalLight(indicator, timeframe, data) {
  const light = document.querySelector(`.signal-light[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
  if (!light) return;
  
  const color = getSignalColor(data.signal, data.strength);
  light.style.backgroundColor = color;
  
  // Update tooltip with current value
  light.title = `${indicator.toUpperCase()} (${timeframe}): ${data.signal} (${(data.strength * 100).toFixed(0)}%)`;
}

// Update chart data
function updateChart(indicator, data) {
  const row = getOrCreateIndicatorRow(indicator);
  if (!row) return;
  
  const chartContainer = row.querySelector('.mini-chart-container');
  if (!chartContainer) return;
  
  const canvas = chartContainer.querySelector('canvas');
  if (!canvas) return;
  
  const chart = Chart.getChart(canvas);
  if (!chart) return;
  
  // Update chart data
  chart.data.datasets.forEach(dataset => {
    // Shift data left and add new value
    dataset.data.shift();
    dataset.data.push(data.value);
  });
  
  chart.update('none'); // 'none' prevents animation
}

// Cleanup function to prevent memory leaks
function cleanup() {
  window.indicatorsInitialized = false;
  
  // Remove all charts
  document.querySelectorAll('.mini-chart-container').forEach(container => {
    const canvas = container.querySelector('canvas');
    if (canvas) {
      const chart = Chart.getChart(canvas);
      if (chart) {
        chart.destroy();
      }
    }
  });
}

// Initialize the indicator display system
function initializeIndicatorDisplay() {
  try {
    // Clean up any existing instances
    cleanup();
    
    // Create indicator rows and lights
    createAllIndicatorLights();
    createAllMiniCharts();
    
    // Set up periodic updates
    setInterval(updateAllIndicators, 1000);
    
    // Mark as initialized
    window.indicatorsInitialized = true;
    console.log('[IndicatorDisplay] Indicator display system initialized');
  } catch (error) {
    console.error('[IndicatorDisplay] Error initializing indicator display:', error);
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initializeIndicatorDisplay);

// Export functions
window.initializeIndicatorDisplay = initializeIndicatorDisplay;
window.updateAllIndicators = updateAllIndicators;
window.createAllIndicatorLights = createAllIndicatorLights;
window.createAllMiniCharts = createAllMiniCharts;
