update-signal-lights.js:406 Signal lights module loaded in compatibility mode
(anonymous) @ update-signal-lights.js:406
enhanced-ml-chart.js:297 [Enhanced<PERSON><PERSON><PERSON>] Historical data loader not available
loadHistoricalData @ enhanced-ml-chart.js:297
init @ enhanced-ml-chart.js:48
await in init
EnhancedML<PERSON><PERSON> @ enhanced-ml-chart.js:34
(anonymous) @ enhanced-ml-chart.js:677
ml-loop-prevention.js:164 🛡️ ML LOOP PREVENTION: Preventing fast interval: 50 ms
console.warn @ ml-loop-prevention.js:164
window.setInterval @ ml-loop-prevention.js:183
setupEventCoordination @ system-coordinator.js:145
startCoordination @ system-coordinator.js:110
init @ system-coordinator.js:23
SystemCoordinator @ system-coordinator.js:15
(anonymous) @ system-coordinator.js:437
ml-loop-prevention.js:164 🛡️ ML LOOP PREVENTION: Preventing fast interval: 100 ms
runIntroAnimation @ (index):17179
(anonymous) @ (index):17451
advanced-ml-features.js:1094 [AdvancedML] ❌ CRITICAL: All price sources failed - NO FALLBACK DATA AVAILABLE
getSafeCurrentPrice @ advanced-ml-features.js:1094
hasValidMarketData @ advanced-ml-features.js:81
init @ advanced-ml-features.js:58
AdvancedMLFeatures @ advanced-ml-features.js:41
initializeMLSystems @ (index):20980
(anonymous) @ (index):17714
ml-loop-prevention.js:164 WebSocket not connected, will retry when connection is established
requestAllIndicators @ (index):15725
(anonymous) @ (index):17980
ml-loop-prevention.js:164 [EnhancedThresholdSliders] Container not found for indicator: cci
updateSliderSegments @ enhanced-threshold-sliders.js:1040
(anonymous) @ enhanced-threshold-sliders.js:1115
updateAllSliders @ enhanced-threshold-sliders.js:1114
renderThresholdSliders @ enhanced-threshold-sliders.js:111
(anonymous) @ (index):18040
ml-loop-prevention.js:164 [EnhancedThresholdSliders] Container not found for indicator: mlPrediction
(index):17448 [Violation] 'DOMContentLoaded' handler took 182ms
[Violation] Forced reflow while executing JavaScript took 89ms
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/1m not available
(anonymous) @ ml-historical-analysis.js:1377
(anonymous) @ ml-historical-analysis.js:1372
getCurrentMarketData @ ml-historical-analysis.js:1370
updateLivePredictor @ ml-historical-analysis.js:1335
(anonymous) @ ml-historical-analysis.js:1261
Promise.then
startHistoricalAnalysis @ ml-historical-analysis.js:1238
init @ ml-historical-analysis.js:49
MLHistoricalAnalysis @ ml-historical-analysis.js:34
initializeMLSystems @ (index):20974
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/1m not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/5m not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/5m not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/15m not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/15m not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/1h not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/1h not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/4h not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/4h not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/1d not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/1d not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator ema/1w not available
ml-loop-prevention.js:164 ❌ NO REAL DATA: Indicator sma/1w not available
ml-historical-analysis.js:1345 [MLHistoricalAnalysis] Error updating live predictor: TypeError: Cannot read properties of null (reading 'signal')
    at ml-historical-analysis.js:1430:19
    at Array.forEach (<anonymous>)
    at ml-historical-analysis.js:1429:44
    at MLHistoricalAnalysis.generateShortTermPrediction (ml-historical-analysis.js:1427:25)
    at MLHistoricalAnalysis.generateLivePredictions (ml-historical-analysis.js:1410:23)
    at MLHistoricalAnalysis.updateLivePredictor (ml-historical-analysis.js:1338:32)
    at ml-historical-analysis.js:1261:14
updateLivePredictor @ ml-historical-analysis.js:1345
8794.580d25ad66d1fc70c9d2.js:21 2025-07-19T16:29:51.115Z:Property:The state with a data type: unknown does not match a schema
b @ 8794.580d25ad66d1fc70c9d2.js:21
(anonymous) @ 8794.580d25ad66d1fc70c9d2.js:21
f @ 38430.9df1121eb1da08ad6596.js:8
addProperty @ 38430.9df1121eb1da08ad6596.js:9
merge @ 38430.9df1121eb1da08ad6596.js:8
pm @ 85957.ffdfb4634a12741827f1.js:253
_createChartWidget @ 85957.ffdfb4634a12741827f1.js:299
_addNewChartWidget @ 85957.ffdfb4634a12741827f1.js:300
setLayout @ 85957.ffdfb4634a12741827f1.js:288
loadContent @ 85957.ffdfb4634a12741827f1.js:292
og @ 85957.ffdfb4634a12741827f1.js:287
540921 @ embed_advanced_chart.dbd467c22ebf9dbb320b.js:46
s @ runtime-embed_advanced_chart.2bd86d34709d792bc8d0.js:1
(anonymous) @ embed_advanced_chart.dbd467c22ebf9dbb320b.js:54
s.O @ runtime-embed_advanced_chart.2bd86d34709d792bc8d0.js:1
a @ runtime-embed_advanced_chart.2bd86d34709d792bc8d0.js:25
(anonymous) @ embed_advanced_chart.dbd467c22ebf9dbb320b.js:1
85957.ffdfb4634a12741827f1.js:269 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
(anonymous) @ 85957.ffdfb4634a12741827f1.js:269
_fireImpl @ 65117.1dd6f50d335cb8a88125.js:1
(anonymous) @ 85957.ffdfb4634a12741827f1.js:270
_makeDefaultModel @ 85957.ffdfb4634a12741827f1.js:271
_init @ 85957.ffdfb4634a12741827f1.js:269
_requestMetadataAndProcessModel @ 85957.ffdfb4634a12741827f1.js:274
85957.ffdfb4634a12741827f1.js:269 [Violation] Added non-passive event listener to a scroll-blocking 'touchmove' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
(anonymous) @ advanced-ml-features.js:97
setInterval
window.setInterval @ ml-loop-prevention.js:187
waitForValidData @ advanced-ml-features.js:96
init @ advanced-ml-features.js:62
ml-loop-prevention.js:159 🛡️ ML SPAM DETECTED - Throttling warnings: [AdvancedML] ⚠️ Null/undefined price detected - NO FALLBACK DATA
console.warn @ ml-loop-prevention.js:159
validatePrice @ advanced-ml-features.js:1003
formatSafePrice @ advanced-ml-features.js:1116
updateLivePredictor @ advanced-ml-features.js:524
generateAdvancedPredictions @ advanced-ml-features.js:461
(anonymous) @ advanced-ml-features.js:1261
setTimeout
startMLAnalysisEngine @ advanced-ml-features.js:1260
init @ advanced-ml-features.js:66
price-scale-mode-buttons-renderer.b714aada96c32f6e1491.js:4 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
p @ price-scale-mode-buttons-renderer.b714aada96c32f6e1491.js:4
_createScaleModeButtons @ 85957.ffdfb4634a12741827f1.js:60
[Violation] 'requestAnimationFrame' handler took 58ms
[Violation] 'requestAnimationFrame' handler took 65ms
[Violation] 'requestAnimationFrame' handler took 51ms
ml-loop-prevention.js:164 🚨 SYSTEM FAILURE: WebSocket Connection failed (1/5)
handleSystemFailure @ system-health-monitor.js:124
performHealthCheck @ system-health-monitor.js:99
(anonymous) @ system-health-monitor.js:79
startHealthChecking @ system-health-monitor.js:79
init @ system-health-monitor.js:22
SystemHealthMonitor @ system-health-monitor.js:15
(anonymous) @ system-health-monitor.js:262
ml-loop-prevention.js:164 ⚠️ HEALTH CHECK: 1 issues detected (83% healthy): ['WebSocket Connection']
performHealthCheck @ system-health-monitor.js:113
ml-loop-prevention.js:164 🚨 DATA ISSUE: WebSocket Data - WebSocket not connected
performVerification @ data-flow-verifier.js:89
(anonymous) @ data-flow-verifier.js:69
startVerification @ data-flow-verifier.js:69
init @ data-flow-verifier.js:20
DataFlowVerifier @ data-flow-verifier.js:13
(anonymous) @ data-flow-verifier.js:348
ml-loop-prevention.js:164 🚨 DATA ISSUE: Price Data - Invalid currentPrice: object [object HTMLDivElement]
verifyMLData @ data-flow-verifier.js:231
check @ data-flow-verifier.js:58
performVerification @ data-flow-verifier.js:81
ml-loop-prevention.js:164 🚨 DATA ISSUE: ML System Data - ML system cannot access valid price data
data-flow-verifier.js:103 🚨 CRITICAL DATA ISSUES: 2 critical, 3 total
performVerification @ data-flow-verifier.js:103
enhanced-mini-charts.js:238 [EnhancedMiniCharts] Error in force update: TypeError: this.updateChart is not a function
    at enhanced-mini-charts.js:230:18
    at NodeList.forEach (<anonymous>)
    at EnhancedMiniCharts.forceUpdateAllCharts (enhanced-mini-charts.js:219:16)
    at enhanced-mini-charts.js:113:14
forceUpdateAllCharts @ enhanced-mini-charts.js:238
(anonymous) @ enhanced-mini-charts.js:113
init @ enhanced-mini-charts.js:112
EnhancedMiniCharts @ enhanced-mini-charts.js:88
initEnhancedCharts @ enhanced-mini-charts.js:1314
(anonymous) @ enhanced-mini-charts.js:1325
main.js:249 Global error: Uncaught TypeError: Cannot read properties of null (reading 'value') at http://localhost:3000/:17920:77 TypeError: Cannot read properties of null (reading 'value')
    at HTMLButtonElement.<anonymous> ((index):17920:77)
window.onerror @ main.js:249
VM3485 main.js:255 Global error: TypeError: Cannot read properties of null (reading 'value')
    at HTMLButtonElement.<anonymous> ((index):17920:77) ErrorEvent {isTrusted: true, message: "Uncaught TypeError: Cannot read properties of null (reading 'value')", filename: 'http://localhost:3000/', lineno: 17920, colno: 77, …}
(anonymous) @ VM3485 main.js:255
main.js:255 Global error: TypeError: Cannot read properties of null (reading 'value')
(anonymous) @ main.js:255
error-recovery.js:222 🚨 ERROR RECOVERY: Handling error in global: TypeError: Cannot read properties of null (reading 'value')
handleError @ error-recovery.js:222
(anonymous) @ error-recovery.js:299
system-health-monitor.js:205 🚨 GLOBAL ERROR: TypeError: Cannot read properties of null (reading 'value')
(anonymous) @ system-health-monitor.js:205
ml-loop-prevention.js:164 🛡️ PREVENTIVE: WebSocket not connected, may need recovery
performPreventiveCheck @ error-recovery.js:217
(anonymous) @ error-recovery.js:155
setupPreventiveMeasures @ error-recovery.js:154
init @ error-recovery.js:21
ErrorRecoverySystem @ error-recovery.js:13
(anonymous) @ error-recovery.js:295
(anonymous) @ data-flow-verifier.js:65
startVerification @ data-flow-verifier.js:64
ml-loop-prevention.js:164 🚨 SYSTEM FAILURE: WebSocket Connection failed (2/5)
(anonymous) @ system-health-monitor.js:75
startHealthChecking @ system-health-monitor.js:74
initializeActionableSignals @ advanced-ml-features.js:1231
(anonymous) @ advanced-ml-features.js:108
waitForValidData @ advanced-ml-features.js:105
(anonymous) @ ml-historical-analysis.js:1254
(anonymous) @ ml-historical-analysis.js:1243
    at ml-historical-analysis.js:1254:18
(anonymous) @ advanced-ml-features.js:1240
initializeActionableSignals @ advanced-ml-features.js:1239
(anonymous) @ (index):18207
(anonymous) @ (index):18191

