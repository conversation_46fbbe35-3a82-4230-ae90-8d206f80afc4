/* thresholds-menu.css */
.thresholds-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(10, 20, 40, 0.98);
  border: 1px solid #00a8ff;
  border-radius: 8px;
  padding: 15px;
  width: 400px;
  max-width: 95vw;
  max-height: 80vh;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  color: #e0e0e0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: none;
  overflow-y: auto;
  margin-top: 10px;
  border-top-right-radius: 0;
  border-top-left-radius: 8px;
  border-bottom-right-radius: 8px;
  transition: all 0.3s ease;
}

.thresholds-menu.active {
  display: block;
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure the menu stays within viewport on small screens */
@media (max-width: 768px) {
  .thresholds-menu {
    width: 95%;
    right: 2.5%;
    left: auto;
  }
}

.threshold-sliders-container {
  max-height: 65vh;
  overflow-y: auto;
  padding: 10px 5px;
  margin-right: -5px;
  padding-right: 5px;
  scrollbar-width: thin;
  scrollbar-color: #00a8ff rgba(0, 0, 0, 0.2);
}

/* Menu header styles */
.thresholds-menu .menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 200, 255, 0.3);
}

.thresholds-menu .menu-header h3 {
  margin: 0;
  color: #00ccff;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.2rem;
}

/* Close button */
.thresholds-menu .close-menu {
  background: none;
  border: none;
  color: #ff4d4d;
  font-size: 1.5rem;
  cursor: pointer;
  line-height: 1;
  padding: 0 5px;
  transition: color 0.2s;
}

.thresholds-menu .close-menu:hover {
  color: #ff0000;
}

/* Custom scrollbar for WebKit browsers */
.threshold-sliders-container::-webkit-scrollbar {
  width: 6px;
}

.threshold-sliders-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.threshold-sliders-container::-webkit-scrollbar-thumb {
  background-color: var(--accent-color, #00b4d8);
  border-radius: 3px;
}

.slider-group {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.slider-group:hover {
  background: rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 10px rgba(0, 180, 216, 0.1);
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

.slider-header h4 {
  margin: 0;
  color: var(--accent-color, #00b4d8);
  font-size: 0.95rem;
  font-weight: 600;
  text-transform: capitalize;
}

.threshold-values {
  font-size: 0.8rem;
  color: var(--text-secondary, #8e9aaf);
  background: rgba(0, 0, 0, 0.3);
  padding: 5px 10px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  box-sizing: border-box;
}

.threshold-container h4 {
  margin: 0 0 10px 0;
  color: var(--accent-color, #00b4d8);
  font-size: 0.9rem;
}

.slider-container {
  position: relative;
  height: 40px;
  margin: 15px 0 30px;
  padding: 10px 0;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
}

.slider-track {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-50%);
  border-radius: 4px;
  overflow: visible;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  width: 100%;
  box-sizing: border-box;
}

.slider-segment {
  position: absolute;
  height: 100%;
  transition: all 0.2s ease;
}

.slider-segment.green {
  left: 0;
  background: linear-gradient(90deg, rgba(0, 200, 83, 0.7), rgba(0, 200, 83, 0.9));
  border-radius: 4px 0 0 4px;
}

.slider-segment.blue {
  background: linear-gradient(90deg, rgba(33, 150, 243, 0.7), rgba(33, 150, 243, 0.9));
}

.slider-segment.neutral {
  background: rgba(255, 255, 255, 0.05);
}

.slider-segment.orange {
  background: linear-gradient(90deg, rgba(255, 152, 0, 0.7), rgba(255, 152, 0, 0.9));
}

.slider-segment.red {
  background: linear-gradient(90deg, rgba(244, 67, 54, 0.7), rgba(244, 67, 54, 0.9));
  border-radius: 0 4px 4px 0;
}

.slider-thumb {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(0, 180, 216, 0.5);
  transition: all 0.2s ease;
  z-index: 2;
}

.slider-thumb:hover, .slider-thumb:active {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 3px rgba(0, 180, 216, 0.7);
}

.slider-thumb:active {
  cursor: grabbing;
}

/* Tooltip for thumbs */
.slider-thumb::after {
  content: attr(data-threshold);
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.slider-thumb:hover::after {
  opacity: 1;
}

/* Reset button */
.reset-thresholds-button {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 15px;
  background: rgba(244, 67, 54, 0.2);
  color: #ff5252;
  border: 1px solid #ff5252;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.reset-thresholds-button:hover {
  background: rgba(244, 67, 54, 0.3);
  transform: translateY(-1px);
}

.reset-thresholds-button:active {
  transform: translateY(0);
}

/* Ensure menu doesn't overlap signal lights */
@media (min-width: 1200px) {
  .ticker-container {
    position: relative;
  }
  
  .thresholds-menu {
    position: absolute;
    right: 0;
    left: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .thresholds-menu {
    width: 90%;
    right: 5%;
    left: 5%;
  }
  
  .slider-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .threshold-values {
    margin-top: 5px;
    width: 100%;
    text-align: left;
  }
}

.segment {
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
}

.segment.green { background: #27ae60; }
.segment.blue { background: #3498db; }
.segment.grey { background: #7f8c8d; }
.segment.orange { background: #f39c12; }
.segment.red { background: #e74c3c; }

.slider-thumb {
  position: absolute;
  width: 12px;
  height: 20px;
  background: white;
  border-radius: 3px;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0,0,0,0.5);
  z-index: 2;
}

.green-thumb { background: #27ae60; }
.blue-thumb { background: #3498db; }
.orange-thumb { background: #f39c12; }
.red-thumb { background: #e74c3c; }

.threshold-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
  font-size: 0.7rem;
  color: var(--text-secondary, #a0a0a0);
}

.threshold-label {
  padding: 2px 5px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.reset-thresholds-button {
  display: block;
  width: 100%;
  padding: 8px;
  margin-top: 10px;
  background: var(--accent-color, #00b4d8);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background 0.2s;
}

.reset-thresholds-button:hover {
  background: var(--accent-secondary, #0077b6);
}

/* Menu button styles */
#toggleThresholdsButton {
  background: linear-gradient(90deg, #00FFFF 0%, #0055FF 100%);
  color: #0a0a1a;
  border: 1px solid #00FFFF;
  border-radius: 4px;
  padding: 0.5em 1em;
  font-family: 'Orbitron', sans-serif;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 255, 255, 0.2);
  margin-right: 0.5rem;
}

#toggleThresholdsButton:hover {
  background: linear-gradient(90deg, #00CCCC 0%, #0044CC 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

#toggleThresholdsButton:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(0, 255, 255, 0.2);
}

/* Essential Threshold Slider Styles */
.slider-container {
  position: relative;
  margin: 15px 0;
  padding: 10px;
  background: rgba(0, 20, 40, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.slider-label {
  color: #00FFFF;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.threshold-slider-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background: #333;
  border-radius: 10px;
  overflow: hidden;
  margin: 10px 0;
}

.threshold-segment {
  position: absolute;
  height: 100%;
  top: 0;
}

.green-segment {
  background: linear-gradient(90deg, #00FF00, #00CC00);
  left: 0;
}

.blue-segment {
  background: linear-gradient(90deg, #0088FF, #0066CC);
}

.gray-segment {
  background: linear-gradient(90deg, #808080, #606060);
}

.orange-segment {
  background: linear-gradient(90deg, #FFA500, #CC8400);
}

.red-segment {
  background: linear-gradient(90deg, #FF0000, #CC0000);
}

.threshold-thumb {
  position: absolute;
  top: -5px;
  width: 12px;
  height: 30px;
  border: 2px solid #FFFFFF;
  border-radius: 6px;
  cursor: grab;
  transform: translateX(-50%);
  z-index: 10;
  transition: all 0.2s ease;
}

.threshold-thumb:hover {
  transform: translateX(-50%) scale(1.1);
}

.threshold-thumb:active {
  cursor: grabbing;
}

.green-thumb {
  background: #00FF00;
}

.blue-thumb {
  background: #0088FF;
}

.orange-thumb {
  background: #FFA500;
}

.red-thumb {
  background: #FF0000;
}

.threshold-value {
  position: absolute;
  top: 35px;
  transform: translateX(-50%);
  font-size: 10px;
  color: #CCCCCC;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 4px;
  border-radius: 3px;
  pointer-events: none;
}