/**
 * Migration script to update strategy handling to use StrategyCore
 * Run this script after including strategy-core.js
 */

(function() {
  console.log('[Migration] Starting strategy handling migration...');
  
  // Wait for StrategyCore to be available
  const MAX_RETRIES = 10;
  const RETRY_DELAY = 100; // ms
  
  function migrateWhenReady(retryCount = 0) {
    if (window.StrategyCore) {
      console.log('[Migration] StrategyCore found, starting migration...');
      performMigration();
    } else if (retryCount < MAX_RETRIES) {
      console.log(`[Migration] Waiting for StrategyCore... (${retryCount + 1}/${MAX_RETRIES})`);
      setTimeout(() => migrateWhenReady(retryCount + 1), RETRY_DELAY);
    } else {
      console.error('[Migration] Failed to find StrategyCore after maximum retries');
    }
  }
  
  function performMigration() {
    try {
      // 1. Remove duplicate event listeners
      removeDuplicateListeners();
      
      // 2. Update global functions to use StrategyCore
      updateGlobalFunctions();
      
      // 3. Patch existing strategy-related functions
      patchExistingFunctions();
      
      console.log('[Migration] Strategy handling migration completed successfully');
    } catch (error) {
      console.error('[Migration] Error during migration:', error);
    }
  }
  
  function removeDuplicateListeners() {
    console.log('[Migration] Removing duplicate event listeners...');
    
    // Get all elements with change listeners
    const elements = document.querySelectorAll('select[id*="strategy"], .strategy-selector');
    
    elements.forEach(el => {
      // Clone the element to remove all event listeners
      const clone = el.cloneNode(true);
      el.parentNode.replaceChild(clone, el);
      
      // Add a single change handler that delegates to StrategyCore
      clone.addEventListener('change', (e) => {
        if (e.target.matches('select[id*="strategy"], .strategy-selector')) {
          window.StrategyCore.handleStrategyChange(e);
        }
      }, { passive: true });
    });
    
    // Remove any global strategy change listeners
    const oldHandler = (e) => {
      if (e.type === 'strategyChanged' && !e.detail?.fromStrategyCore) {
        e.stopImmediatePropagation();
      }
    };
    
    document.removeEventListener('strategyChanged', oldHandler, true);
    document.addEventListener('strategyChanged', oldHandler, { capture: true, passive: true });
  }
  
  function updateGlobalFunctions() {
    console.log('[Migration] Updating global functions...');
    
    // Replace global strategy functions with StrategyCore equivalents
    window.applySelectedStrategy = function(strategyId) {
      console.log('[Migration] applySelectedStrategy called, delegating to StrategyCore');
      return window.StrategyCore.applyStrategy(strategyId, { source: 'legacy' });
    };
    
    window.updateStrategyDescription = function() {
      console.log('[Migration] updateStrategyDescription called, delegating to StrategyCore');
      return window.StrategyCore.updateStrategyInfo();
    };
    
    window.updateStrategyInfoPanel = function(strategy) {
      console.log('[Migration] updateStrategyInfoPanel called, delegating to StrategyCore');
      return window.StrategyCore.applyStrategy(strategy || window.StrategyCore.currentStrategy, { 
        source: 'legacy',
        force: true 
      });
    };
    
    // Add a way to access the current strategy
    window.getCurrentStrategy = function() {
      return window.StrategyCore.currentStrategy;
    };
  }
  
  function patchExistingFunctions() {
    console.log('[Migration] Patching existing functions...');
    
    // Patch any functions that might directly manipulate strategy state
    const originalFunctions = {
      // Add any functions that need to be patched
    };
    
    // Store originals in case we need them
    window._originalStrategyFunctions = originalFunctions;
    
    // Patch menu controller if it exists
    if (window.MenuController) {
      const originalInit = window.MenuController.prototype.initializeStrategySelector;
      window.MenuController.prototype.initializeStrategySelector = function() {
        console.log('[Migration] MenuController.initializeStrategySelector intercepted');
        // Let StrategyCore handle initialization
        window.StrategyCore.init();
        
        // Call original with proper context if needed
        if (originalInit) {
          return originalInit.call(this);
        }
      };
    }
  }
  
  // Start migration
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', migrateWhenReady);
  } else {
    migrateWhenReady();
  }
})();
