/**
 * StarCrypt Menu & Strategy Fix
 *
 * Fixes critical issues:
 * 1. Menu buttons breaking when strategy selector is opened
 * 2. Strategy inconsistencies between definition and display
 * 3. Missing indicator rows for strategies
 * 4. "Script error at :0:0" and other DOM-related errors
 */

// Wait until DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('[MenuStrategyFix] Initializing fixes')

  // Fix all menu buttons to ensure they work regardless of state
  fixAllMenuButtons()

  // Fix the strategy selector to ensure all strategies are consistent
  fixStrategyDefinitions()

  // Fix the strategy switching mechanism
  fixStrategySwitch()

  // Add click outside detection to close menus
  setupClickOutsideToCloseMenus()

  console.log('[MenuStrategyFix] All fixes applied')
})

// Fix all menu buttons to ensure they work in all states
function fixAllMenuButtons() {
  try {
    // Get all menu buttons
    const menuButtons = document.querySelectorAll('.menu-button')
    if (!menuButtons.length) {
      console.warn('[MenuStrategyFix] No menu buttons found')
      return
    }

    // For each button, replace with cloned version to remove old listeners
    menuButtons.forEach(button => {
      const newButton = button.cloneNode(true)
      if (button.parentNode) {
        button.parentNode.replaceChild(newButton, button)
      }

      // Add our robust click handler
      newButton.addEventListener('click', function (e) {
        e.preventDefault()
        e.stopPropagation()

        const targetId = this.getAttribute('data-target')
        if (!targetId) return

        const targetMenu = document.getElementById(targetId)
        if (!targetMenu) return

        // Close all other menus
        document.querySelectorAll('.menu-content').forEach(menu => {
          if (menu.id !== targetId) {
            menu.style.display = 'none'
          }
        })

        // Toggle this menu
        targetMenu.style.display = targetMenu.style.display === 'block' ? 'none' : 'block'
      })
    })

    console.log('[MenuStrategyFix] Fixed all menu buttons')
  } catch (e) {
    console.error('[MenuStrategyFix] Error fixing menu buttons:', e)
  }
}

// Fix strategy definitions to ensure consistency
function fixStrategyDefinitions() {
  try {
    // Ensure TRADING_STRATEGIES exists
    if (!window.TRADING_STRATEGIES) {
      console.error('[MenuStrategyFix] TRADING_STRATEGIES not defined')
      return
    }

    // Fix all strategies to ensure consistency
    Object.keys(window.TRADING_STRATEGIES).forEach(key => {
      const strategy = window.TRADING_STRATEGIES[key]

      // Ensure it has all required properties
      if (!strategy.name) {
        strategy.name = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }

      if (!strategy.description) {
        strategy.description = `Strategy based on ${strategy.indicators ? strategy.indicators.join(', ') : 'various indicators'}.`
      }

      if (!strategy.color) {
        strategy.color = '#4CAF50' // Default green
      }

      // Ensure indicators list is valid and complete
      if (!Array.isArray(strategy.indicators) || strategy.indicators.length === 0) {
        // Set default indicators
        strategy.indicators = ['rsi', 'macd', 'bollingerBands', 'volume']
      }

      // Validate each indicator exists
      strategy.indicators = strategy.indicators.filter(ind => typeof ind === 'string')

      // Ensure all strategies have volume
      if (!strategy.indicators.includes('volume')) {
        strategy.indicators.push('volume')
      }

      // AI strategies should have ML indicators
      if (key.includes('ai') || key.includes('ml') || key.includes('neural') || key.includes('learning')) {
        if (!strategy.indicators.includes('ml')) strategy.indicators.push('ml')
        if (!strategy.indicators.includes('sentiment')) strategy.indicators.push('sentiment')
      }

      // Generate helper steps if missing
      if (!strategy.helperText) {
        strategy.helperText = generateHelperSteps(strategy)
      }
    })

    // Check for specific strategies and ensure they have necessary indicators
    const specificFixes = {
      scalping_sniper: ['rsi', 'macd', 'bollingerBands', 'volume', 'atr'],
      neural_network_navigator: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
      deep_learning_diver: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
      ai_pattern_prophet: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal', 'volume'],
      machine_learning_momentum: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    }

    // Apply specific fixes
    Object.keys(specificFixes).forEach(key => {
      if (window.TRADING_STRATEGIES[key]) {
        window.TRADING_STRATEGIES[key].indicators = specificFixes[key]
      }
    })

    console.log('[MenuStrategyFix] Fixed all strategy definitions')
  } catch (e) {
    console.error('[MenuStrategyFix] Error fixing strategy definitions:', e)
  }
}

// Generate helper steps for a strategy
function generateHelperSteps(strategy) {
  try {
    const indicators = strategy.indicators || []
    let steps = `<p><strong>${strategy.name}</strong> - ${strategy.description}</p>
                <p>Trading Steps:</p>
                <ol>`

    // Add steps based on indicators
    if (indicators.includes('rsi')) {
      steps += `<li>Check if RSI is showing overbought (>70) or oversold (<30) conditions</li>`
    }

    if (indicators.includes('macd')) {
      steps += `<li>Confirm with MACD cross (bullish when MACD crosses above signal line, bearish when below)</li>`
    }

    if (indicators.includes('bollingerBands')) {
      steps += `<li>Verify price position relative to Bollinger Bands (breakouts or bounces)</li>`
    }

    if (indicators.includes('volume')) {
      steps += `<li>Ensure volume confirms price movement (high volume on breakouts)</li>`
    }

    if (indicators.includes('atr')) {
      steps += `<li>Use ATR to set appropriate stop-loss distances</li>`
    }

    if (indicators.includes('ml') || indicators.includes('sentiment')) {
      steps += `<li>Confirm signal with AI/ML predictions</li>`
    }

    // General advice
    steps += `<li>Wait for confirmation across multiple timeframes</li>
              <li>Enter trade when ${Math.min(3, indicators.length)}+ indicators align</li>
              <li>Use proper position sizing (1-2% risk per trade)</li>
              </ol>`

    return steps
  } catch (e) {
    console.error('[MenuStrategyFix] Error generating helper steps:', e)
    return '<p>Strategy helper steps unavailable. Please check indicators manually.</p>'
  }
}

// Fix strategy switching mechanism
function fixStrategySwitch() {
  try {
    // Fix the strategy selector change event
    const strategySelector = document.getElementById('mainStrategySelector')
    if (strategySelector) {
      // Clone to remove old listeners
      const newSelector = strategySelector.cloneNode(true)
      if (strategySelector.parentNode) {
        strategySelector.parentNode.replaceChild(newSelector, strategySelector)
      }

      // Add preview-only behavior on change
      newSelector.addEventListener('change', function () {
        // Just update the preview, don't switch strategy yet
        if (typeof window.updateStrategyInfoPanel === 'function') {
          window.updateStrategyInfoPanel(this.value)
        }

        console.log('[MenuStrategyFix] Strategy previewed:', this.value)
      })
    }

    // Fix Apply button
    const applyButton = document.getElementById('applyStrategyButton')
    if (applyButton) {
      // Clone to remove old listeners
      const newButton = applyButton.cloneNode(true)
      if (applyButton.parentNode) {
        applyButton.parentNode.replaceChild(newButton, applyButton)
      }

      // Add robust apply behavior
      newButton.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()

        // Get selected strategy
        const selector = document.getElementById('mainStrategySelector')
        if (!selector) return

        const strategy = selector.value
        if (!strategy || !window.TRADING_STRATEGIES[strategy]) {
          console.error('[MenuStrategyFix] Invalid strategy selected:', strategy)
          return
        }

        console.log('[MenuStrategyFix] Applying strategy:', strategy)

        // Save to localStorage
        try {
          localStorage.setItem('currentStrategy', strategy)
        } catch (err) {}

        // Update global variable
        window.currentStrategy = strategy

        // Update UI
        updateHelperContent(strategy)

        // Show animation if available
        if (typeof window.enhancedShowStrategyAnimation === 'function') {
          window.enhancedShowStrategyAnimation(strategy)
        } else if (typeof window.showStrategyAnimation === 'function') {
          window.showStrategyAnimation(strategy)
        }

        // Switch strategy in backend
        if (typeof window.switchStrategy === 'function') {
          window.switchStrategy(strategy)
        }

        // Ensure all indicators are displayed
        ensureAllIndicatorsDisplayed(strategy)

        // Close strategy menu
        const strategyMenu = document.getElementById('strategyMenu')
        if (strategyMenu) {
          strategyMenu.style.display = 'none'
        }
      })
    }

    console.log('[MenuStrategyFix] Fixed strategy switching')
  } catch (e) {
    console.error('[MenuStrategyFix] Error fixing strategy switch:', e)
  }
}

// Update helper content for a strategy
function updateHelperContent(strategy) {
  try {
    if (!window.TRADING_STRATEGIES[strategy]) return

    const strategyData = window.TRADING_STRATEGIES[strategy]

    // Update helper title
    const helperTitle = document.querySelector('.helper-container .section-header h3')
    if (helperTitle) {
      helperTitle.textContent = `Trading Helper: ${strategyData.name}`
    }

    // Update helper content
    const helperContent = document.querySelector('.helper-container .helper-content')
    if (helperContent) {
      helperContent.innerHTML = strategyData.helperText || generateHelperSteps(strategyData)
    }

    console.log('[MenuStrategyFix] Helper content updated for:', strategy)
  } catch (e) {
    console.error('[MenuStrategyFix] Error updating helper:', e)
  }
}

// Ensure all indicators for a strategy are displayed
function ensureAllIndicatorsDisplayed(strategy) {
  try {
    if (!window.TRADING_STRATEGIES[strategy]) return

    const indicators = window.TRADING_STRATEGIES[strategy].indicators || []

    // First, check if the indicator row creator function exists
    if (typeof window.ensureIndicatorRowsExist === 'function') {
      window.ensureIndicatorRowsExist(indicators)
      console.log('[MenuStrategyFix] Used existing ensureIndicatorRowsExist for:', strategy)
      return
    }

    // If not, implement our own version
    const momentumTable = document.querySelector('.momentum-table tbody')
    if (!momentumTable) {
      console.error('[MenuStrategyFix] Momentum table not found')
      return
    }

    // Process each indicator
    indicators.forEach(indicator => {
      // Check if row already exists
      let row = momentumTable.querySelector(`tr[data-indicator="${indicator}"]`)

      if (!row) {
        // Create new row with consistent data attributes
        row = document.createElement('tr')
        row.setAttribute('data-indicator', indicator)
        row.setAttribute('data-ind', indicator) // Add this for compatibility
        row.id = `indicator-row-${indicator}` // Add a unique ID for targeting
        row.className = 'indicator-row signal-row' // Keep both classes

        // Create name cell with simple display name
        const nameCell = document.createElement('td')
        nameCell.className = 'signal-name'
        nameCell.setAttribute('data-indicator', indicator) // Ensure cell has indicator attribute
        nameCell.textContent = indicator.toUpperCase()
        row.appendChild(nameCell)

        // Create signal cells
        const timeframes = window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
        timeframes.forEach(tf => {
          const signalCell = document.createElement('td')
          signalCell.className = 'signal-light-cell'
          signalCell.setAttribute('data-timeframe', tf)

          // Create signal circle with proper class and data attributes to match update-signal-lights.js
          const signalCircle = document.createElement('div')
          signalCircle.className = 'signal-circle grey-light' // Critical: Use signal-circle class
          signalCircle.id = `${indicator}-${tf}-signal`
          signalCircle.setAttribute('data-ind', indicator) // Critical: This is what update-signal-lights.js looks for
          signalCircle.setAttribute('data-tf', tf) // Critical: This is what update-signal-lights.js looks for
          signalCircle.setAttribute('data-indicator', indicator) // For consistency
          signalCircle.setAttribute('data-timeframe', tf) // For consistency

          // Add click handler
          signalCircle.addEventListener('click', () => {
            if (typeof window.handleSignalClick === 'function') {
              window.handleSignalClick(indicator, tf)
            }
          })

          signalCell.appendChild(signalCircle)
          row.appendChild(signalCell)
        })

        // Add row to table
        momentumTable.appendChild(row)

        // Show existing row
        row.style.display = ''
      }
    })

    // Hide rows not in current strategy
    const allRows = momentumTable.querySelectorAll('tr[data-indicator]')
    allRows.forEach(row => {
      const rowIndicator = row.getAttribute('data-indicator')
      if (!indicators.includes(rowIndicator)) {
        row.style.display = 'none'
      }
    })

    console.log('[MenuStrategyFix] All indicators displayed for strategy:', strategy)
  } catch (e) {
    console.error('[MenuStrategyFix] Error displaying indicators:', e)
  }
}

// Format indicator name for display
function formatIndicatorName(indicator) {
  // Use the global INDICATOR_DISPLAY_NAMES from global-variables.js instead of a local map
  // This ensures consistency across the entire application
  if (window.INDICATOR_DISPLAY_NAMES && window.INDICATOR_DISPLAY_NAMES[indicator]) {
    return window.INDICATOR_DISPLAY_NAMES[indicator]
  }

  // Special formatting for certain indicators that might need specific formatting
  if (indicator === 'ultimateOscillator') {
    return 'ULTIMATE OSCILLATOR'
  } else if (indicator === 'bollingerBands') {
    return 'BOLLINGER BANDS'
  } else if (indicator === 'williamsR') {
    return 'WILLIAMS %R'
  } else if (indicator === 'vwap') {
    return 'VWAP GUARDIAN'
  } else if (indicator === 'atr') {
    return 'ATR (VOLATILITY)'
  } else if (indicator === 'volume') {
    return 'VOLUME ANALYSIS'
  } else if (indicator === 'fractal') {
    return 'FRACTAL PATTERN'
  } else if (indicator === 'sentiment') {
    return 'SENTIMENT ANALYSIS'
  } else if (indicator === 'entropy') {
    return 'MARKET ENTROPY'
  } else if (indicator === 'correlation') {
    return 'MARKET CORRELATION'
  } else if (indicator === 'time_anomaly') {
    return 'TIME ANOMALY'
  }

  // Default fallback
  return typeof indicator === 'string' ? indicator.toUpperCase() : String(indicator)
}

// Setup click outside to close menus
function setupClickOutsideToCloseMenus() {
  try {
    // Add global click handler
    document.addEventListener('click', (e) => {
      // Check if the click is outside any menu or menu button
      const menus = document.querySelectorAll('.menu-content')
      const buttons = document.querySelectorAll('.menu-button')

      // If click target is not inside any menu or button, close all menus
      let insideMenuSystem = false

      // Check if click inside a menu
      menus.forEach(menu => {
        if (menu.contains(e.target)) {
          insideMenuSystem = true
        }
      })

      // Check if click is on a menu button
      buttons.forEach(button => {
        if (button.contains(e.target)) {
          insideMenuSystem = true
        }
      })

      // If outside, close all menus
      if (!insideMenuSystem) {
        menus.forEach(menu => {
          menu.style.display = 'none'
        })
      }
    })

    console.log('[MenuStrategyFix] Added click-outside-to-close behavior')
  } catch (e) {
    console.error('[MenuStrategyFix] Error setting up click outside:', e)
  }
}

// Initialize the fix
function initializeMenuStrategyFix() {
  console.log('[MenuStrategyFix] Fix fully loaded and initialized')
}

// Make functions globally available
window.fixAllMenuButtons = fixAllMenuButtons
window.fixStrategyDefinitions = fixStrategyDefinitions
window.fixStrategySwitch = fixStrategySwitch
window.ensureAllIndicatorsDisplayed = ensureAllIndicatorsDisplayed
window.updateHelperContent = updateHelperContent

// Run initialization
initializeMenuStrategyFix()
