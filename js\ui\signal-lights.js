/**
 * Signal Lights - UI Component for Displaying Signal Indicators
 * Integrates with SignalManager and SignalInitializer to provide visual feedback for trading signals
 */
/**
 * Check and log information about signal elements (debug only)
 */
function checkSignalElements() {
  if (!window.DEBUG_MODE) return;
  
  try {
    const signals = document.querySelectorAll('.signal-circle');
    window.debugLog(`Found ${signals.length} signal elements`);
    
    signals.forEach((signal, index) => {
      window.debugLog(`Signal ${index + 1}:`, {
        id: signal.id,
        indicator: signal.dataset.indicator || signal.dataset.ind,
        timeframe: signal.dataset.timeframe || signal.dataset.tf,
        classes: signal.className,
      });
    });
  } catch (error) {
    console.error('[SignalLights] Error checking signal elements:', error);
  }
}
// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  try {
    window.debugLog('[SignalLights] DOM Content Loaded - Starting initialization');
    
    // Initialize immediately
    initSignalLights();
    
    // Set up fallback SignalManager if not available
    if (!window.SignalManager) {
      window.debugLog('[SignalLights] Main SignalManager not found, setting up fallback');
      setupFallbackSignalManager();
    } else {
      window.debugLog('[SignalLights] Using main SignalManager');
    }
    
    // Check for signal elements (debug only)
    if (window.DEBUG_MODE) {
      checkSignalElements();
      
      // Log info about the current environment
      window.debugLog('[SignalLights] Environment:', {
        DEBUG_MODE: window.DEBUG_MODE,
        SignalManager: window.SignalManager ? 'Available' : 'Not Available',
        signalLightsInitialized: window.signalLightsInitialized || false
      });
    }
  } catch (error) {
    console.error('[SignalLights] Critical initialization error:', error);
    
    // Try to recover by setting up the fallback
    try {
      if (!window.SignalManager) {
        console.warn('[SignalLights] Attempting recovery with fallback SignalManager');
        setupFallbackSignalManager();
      }
    } catch (fallbackError) {
      console.error('[SignalLights] Recovery failed:', fallbackError);
    }
  }

  let attempts = 0;  // Initialize attempts counter
  const maxAttempts = 30;  // 3 seconds total (30 * 100ms)
  
  const checkSignalManager = setInterval(() => {
    attempts++;
    if (window.SignalManager) {
      clearInterval(checkSignalManager);
      console.log(`[SignalLights] SignalManager found after ${attempts * 100}ms`);
      initSignalLights();
    } else if (attempts >= maxAttempts) {
      clearInterval(checkSignalManager)
      console.warn('[SignalLights] SignalManager not found after max attempts, initializing with fallback')

      // Create a minimal SignalManager fallback that works with our new system
      window.SignalManager = {
        updateSignal: (ind, tf, data) => {
          if (!ind || !tf || !data) return

          const key = `${ind}_${tf}`
          console.log(`[Fallback] Updating signal: ${key}`, data)

          // Try to find the element
          const element = document.querySelector(`#${ind}-${tf}`)
          if (!element) {
            console.warn(`[Fallback] Signal element not found: ${ind}-${tf}`)
            return
          }

          // Use the signal initializer if available
          if (window.signalInitializer) {
            const signalClass = getSignalClass(data.signal || 'neutral', data.strength || 0.5)
            window.signalInitializer.queueUpdate(element, signalClass, {
              tooltip: data.tooltip || `${key}: ${signalClass}`,
              strength: data.strength || 0.5,
              timestamp: Date.now(),
            })
          } else {
            // Fallback to direct update
            if (data.color) element.style.backgroundColor = data.color
            if (data.tooltip) element.title = data.tooltip
          }
        },
      }

      initSignalLights()
    }
    attempts++
  }, 100)
})

/**
 * Get signal class based on signal type and strength
 */
function getSignalClass(signal, strength = 0.5) {
  const baseSignal = (signal || '').toLowerCase()

  if (baseSignal.includes('buy')) {
    return strength > 0.6 ? 'strong-buy' : 'mild-buy'
  } else if (baseSignal.includes('sell')) {
    return strength > 0.6 ? 'strong-sell' : 'mild-sell'
  } else if (baseSignal.includes('neutral')) {
    return 'neutral'
  }

  return 'neutral'
}

/**
 * Initialize the signal lights component
 */
function initSignalLights() {
  console.log('[SignalLights] Initializing...')

  // Create styles for signal lights if they don't exist
  if (!document.getElementById('signal-lights-styles')) {
    const style = document.createElement('style')
    style.id = 'signal-lights-styles'
    style.textContent = `
            .signal-circle {
                display: inline-block;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                margin: 2px;
                transition: all 0.3s ease;
                position: relative;
                cursor: pointer;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-sizing: border-box;
            }
            
            /* Signal states */
            .strong-buy { background-color: #00FF00; }
            .mild-buy { background-color: #00AAFF; }
            .neutral { background-color: #808080; }
            .mild-sell { background-color: #FFA500; }
            .strong-sell { background-color: #FF0000; }
            .error { background-color: #FF00FF; }
            
            /* Pulse animation for strong signals */
            .pulse {
                animation: pulse 2s infinite;
                box-shadow: 0 0 0 rgba(255, 255, 255, 0.7);
            }
            
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4); }
                70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
                100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
            }
            
            /* Tooltip styles */
            .custom-tooltip {
                position: fixed;
                background: rgba(0, 0, 0, 0.8);
                color: #fff;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                z-index: 1000;
                pointer-events: none;
                margin-bottom: 5px;
            }
        `
    document.head.appendChild(style)
  }

  // Track existing tooltips
  const existingTooltips = new Map()

  // Remove any existing tooltip event listeners
  document.querySelectorAll('.signal-light').forEach(el => {
    el.removeEventListener('mouseenter', handleTooltipShow)
    el.removeEventListener('mouseleave', handleTooltipHide)
  })

  // Clean up any existing tooltips
  document.querySelectorAll('.custom-tooltip').forEach(el => el.remove())
  existingTooltips.clear()

  // DISABLED - Enhanced tooltips handle this now
  console.log('[SignalLights] DISABLED - Tooltip handling managed by enhanced tooltip system')

  // Listen for signal updates
  document.addEventListener('signalUpdate', handleSignalUpdate)

  console.log('[SignalLights] Initialized')
}

// Handle tooltip display
function handleTooltipShow(event) {
  const signalId = this.getAttribute('data-signal-id')
  if (!signalId || existingTooltips.has(signalId)) return

  const tooltip = document.createElement('div')
  tooltip.className = 'custom-tooltip'
  tooltip.textContent = this.getAttribute('title') || 'No data'

  // Position tooltip
  const rect = this.getBoundingClientRect()
  tooltip.style.left = `${rect.left + window.scrollX}px`
  tooltip.style.top = `${rect.top + window.scrollY - 30}px`

  document.body.appendChild(tooltip)
  existingTooltips.set(signalId, tooltip)

  // Remove default title to prevent double tooltip
  const title = this.getAttribute('title')
  if (title) {
    this.setAttribute('data-original-title', title)
    this.removeAttribute('title')
  }
}

// Handle tooltip hide
function handleTooltipHide() {
  const signalId = this.getAttribute('data-signal-id')
  if (!signalId) return

  const tooltip = existingTooltips.get(signalId)
  if (tooltip) {
    tooltip.remove()
    existingTooltips.delete(signalId)
  }

  // Restore original title
  const originalTitle = this.getAttribute('data-original-title')
  if (originalTitle) {
    this.setAttribute('title', originalTitle)
    this.removeAttribute('data-original-title')
  }
}

// Generate tooltip text
function generateTooltipText(indicator, timeframe, data) {
  if (!data) return `${indicator} (${timeframe}): No data`

  let text = `${indicator.toUpperCase()} (${timeframe})\n-------------------\n`

  // Add indicator-specific data
  switch (indicator) {
    case 'rsi':
      text += `Value: ${typeof data?.value === 'number' ? data.value.toFixed(2) : 'N/A'}\n`
      text += `Signal: ${typeof data?.value === 'number' ? (data.value > 70 ? 'Overbought' : data.value < 30 ? 'Oversold' : 'Neutral') : 'Unknown'}`
      break
    case 'macd':
      text += `Histogram: ${typeof data?.histogram === 'number' ? data.histogram.toFixed(4) : 'N/A'}\n`
      text += `Signal: ${typeof data?.histogram === 'number' ? (data.histogram > 0 ? 'Bullish' : 'Bearish') : 'Unknown'}`
      break
      // Add more cases as needed
    default:
      text += JSON.stringify(data, null, 2)
  }

  return text
}

// Update signal light with new data
function updateSignalLight(indicator, timeframe, data) {
  const signalId = `${indicator}-${timeframe}`
  let signalElement = document.querySelector(`[data-signal-id="${signalId}"]`)

  if (!signalElement) {
    signalElement = createSignalLightElement(indicator, timeframe)
    if (!signalElement) return
  }

  // Update signal appearance based on data
  updateSignalAppearance(signalElement, indicator, data)

  // Update tooltip content
  const tooltipText = generateTooltipText(indicator, timeframe, data)
  signalElement.setAttribute('data-original-title', tooltipText)

  // If tooltip is currently showing, update it
  const existingTooltip = existingTooltips.get(signalId)
  if (existingTooltip) {
    existingTooltip.textContent = tooltipText
  }
}

// Create a new signal light element
function createSignalLightElement(indicator, timeframe) {
  const container = document.getElementById('signal-matrix') || document.body
  if (!container) return null

  const signalId = `${indicator}-${timeframe}`
  const signalElement = document.createElement('div')
  signalElement.className = 'signal-light'
  signalElement.setAttribute('data-signal-id', signalId)

  // Add to container
  container.appendChild(signalElement)

  // Add event listeners
  signalElement.addEventListener('mouseenter', handleTooltipShow)
  signalElement.addEventListener('mouseleave', handleTooltipHide)

  return signalElement
}

// Update signal appearance based on data
function updateSignalAppearance(element, indicator, data) {
  if (!element || !data) return

  // Reset classes
  element.className = 'signal-light'

  // Determine signal state and color
  let state = 'neutral'
  let color = '#808080'

  // Your existing signal logic here
  if (indicator === 'rsi') {
    if (data.value > 70) {
      state = 'overbought'
      color = '#ff4444'
    } else if (data.value < 30) {
      state = 'oversold'
      color = '#44ff44'
    }
  }
  // Add more indicator logic as needed

  // Apply styles
  element.style.backgroundColor = color
  element.classList.add(state)
}

/**
 * Handle signal update events from the WebSocket.
 * This function finds the correct signal circle in the DOM and updates it.
 */
function handleSignalUpdate(event) {
  const { indicator, timeframe, data } = event.detail;

  if (!indicator || !timeframe || !data) {
    console.warn('[SignalLights] Invalid signal update received:', event.detail);
    return;
  }

  // Correctly select the signal circle element within the matrix
  const selector = `.signal-circle[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`;
  let element = document.querySelector(selector);

  // If the element doesn't exist, the matrix may not be fully initialized.
  // We rely on other modules like signal-matrix.js to create the initial structure.
  // Creating elements here can lead to a disorganized DOM.
  if (!element) {
    // console.warn(`[SignalLights] Signal element not found for selector: ${selector}. Update ignored.`);
    return; // Do not create elements here to avoid side-effects
  }

  // Update the element with the new data
  try {
    updateSignalElement(element, indicator, timeframe, data);
  } catch (error) {
    console.error(`[SignalLights] Failed to update element for ${indicator}/${timeframe}:`, error);
  }
}

/**
 * Create a new signal element
 */
function createSignalElement(indicator, timeframe) {
  try {
    // Find or create the parent row
    const row = document.querySelector(`tr[data-indicator="${indicator}"]`)

    if (!row) {
      console.warn(`[SignalLights] Could not find row for indicator: ${indicator}`)
      return null
    }

    // Create signal cell if it doesn't exist
    let cell = row.querySelector(`td[data-timeframe="${timeframe}"]`)
    if (!cell) {
      cell = document.createElement('td')
      cell.className = 'signal-cell'
      cell.setAttribute('data-timeframe', timeframe)
      row.appendChild(cell)
    }

    // Create signal circle
    const signalCircle = document.createElement('div')
    signalCircle.className = 'signal-circle'
    signalCircle.setAttribute('data-indicator', indicator)
    signalCircle.setAttribute('data-timeframe', timeframe)
    signalCircle.setAttribute('data-tooltip', `${indicator.toUpperCase()} (${timeframe}): Loading...`)

    // Add click handler
    signalCircle.addEventListener('click', (e) => {
      e.stopPropagation()
      const event = new CustomEvent('signalClick', {
        detail: { indicator, timeframe, element: signalCircle },
      })
      document.dispatchEvent(event)
    })

    cell.appendChild(signalCircle)
    return signalCircle
  } catch (error) {
    console.error(`[SignalLights] Error creating signal element for ${indicator} ${timeframe}:`, error)
    return null
  }
}

/**
 * Update a signal element with new data
 */
function updateSignalElement(element, indicator, timeframe, data) {
  if (!element) {
    console.warn(`[SignalLights] Cannot update null element for ${indicator} ${timeframe}`)
    return
  }

  try {
    // Ensure element has the signal-circle class
    if (!element.classList.contains('signal-circle')) {
      element.className = 'signal-circle'
    }

    // Skip if no data
    if (!data) {
      console.log(`[SignalLights] No data for ${indicator} ${timeframe}`)

      // Use signal initializer if available
      if (window.signalInitializer) {
        window.signalInitializer.queueUpdate(element, 'neutral', {
          tooltip: `${indicator} (${timeframe}): No data`,
          strength: 0,
          timestamp: Date.now(),
        })
      } else {
        // Fallback to direct update
        element.className = 'signal-circle neutral'
        element.title = `${indicator} (${timeframe}): No data`
      }
      return
    }

    // Determine signal class safely
    const signal = (data.signal || '').toLowerCase().replace(/\s+/g, '-')
    const validSignals = ['strong-buy', 'mild-buy', 'neutral', 'mild-sell', 'strong-sell']
    const signalClass = validSignals.includes(signal) ? signal : 'neutral'
    const strength = parseFloat(data.strength) || 0
    const tooltipText = data.tooltip || `${indicator} (${timeframe}): ${signal.replace(/-/g, ' ')}`

    // Use signal initializer if available
    if (window.signalInitializer) {
      window.signalInitializer.queueUpdate(element, signalClass, {
        tooltip: tooltipText,
        strength,
        timestamp: Date.now(),
        indicator,
        timeframe,
      })
    } else {
      // Fallback to direct update
      element.className = `signal-circle ${signalClass}`
      element.title = tooltipText

      // Add pulse for strong signals
      if (strength > 0.7) {
        element.classList.add('pulse')
      } else {
        element.classList.remove('pulse')
      }

      // Update data attributes
      element.dataset.indicator = indicator
      element.dataset.timeframe = timeframe
      element.dataset.signal = signal
      element.dataset.strength = strength
    }
  } catch (error) {
    console.error(`[SignalLights] Error updating ${indicator} ${timeframe}:`, error)

    // Try to recover using signal initializer
    try {
      if (window.signalInitializer) {
        window.signalInitializer.queueUpdate(element, 'neutral', {
          tooltip: `${indicator} (${timeframe}): Error`,
          strength: 0,
          timestamp: Date.now(),
          error: error.message,
        })
      } else {
        // Fallback to direct error state
        element.className = 'signal-circle neutral error'
        element.title = `${indicator} (${timeframe}): Error`
      }
    } catch (recoveryError) {
      console.error('[SignalLights] Error during recovery:', recoveryError)
      // Last resort
      element.className = 'signal-circle neutral error'
      element.title = `${indicator} (${timeframe}): Critical Error`
    }
  }
}

// Fallback SignalManager implementation
function setupFallbackSignalManager() {
  if (window.SignalManager) return;
  
  console.log('[SignalLights] Setting up fallback SignalManager');
  
  window.SignalManager = {
    updateSignal: (ind, tf, data) => {
      if (!ind || !tf || !data) return;
      
      const key = `${ind}_${tf}`;
      
      // Try to find the element
      const element = document.querySelector(`#${ind}-${tf}`) || 
                     document.querySelector(`[data-indicator="${ind}"][data-timeframe="${tf}"]`);
      
      if (!element) {
        if (window.DEBUG_MODE) {
          console.warn(`[Fallback] Signal element not found: ${ind}-${tf}`);
        }
        return;
      }
      
      // Update the element
      updateSignalElement(element, ind, tf, data);
    },
    
    // Add other necessary methods with minimal implementation
    init: () => console.log('[Fallback] SignalManager initialized'),
    getSignalState: () => ({}),
    updateSignals: (signals) => {
      signals.forEach(signal => {
        if (signal && signal.indicator && signal.timeframe) {
          window.SignalManager.updateSignal(signal.indicator, signal.timeframe, signal.data || {});
        }
      });
    }
  };
  
  // Initialize the fallback
  window.SignalManager.init();
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    initSignalLights,
    updateSignalLight,
    createSignalLightElement,
    updateSignalAppearance,
    generateTooltipText,
    setupFallbackSignalManager
  };
}
